{"name": "doll_controller_app_rn", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest", "assets": "react-native-asset", "pod": "cd ios && bundle install && bundle exec pod install && cd ..", "mock": "node mock/start.js", "build:android": "cd android && ./gradlew assembleRelease", "build:android-bundle": "cd android && ./gradlew bundleRelease", "build:ios": "cd ios && xcodebuild -workspace doll_controller_app_rn.xcworkspace -scheme doll_controller_app_rn -configuration Release -archivePath doll_controller_app_rn.xcarchive archive && xcodebuild -exportArchive -archivePath doll_controller_app_rn.xcarchive -exportPath ./build -exportOptionsPlist exportOptions.plist"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.1.2", "@react-native-clipboard/clipboard": "^1.16.2", "@react-navigation/bottom-tabs": "^7.3.12", "@react-navigation/drawer": "^7.3.11", "@react-navigation/native": "^7.1.9", "@react-navigation/native-stack": "^7.3.12", "@sayem314/react-native-keep-awake": "^1.3.1", "@types/react-native-global-props": "^1.1.6", "@types/react-native-vector-icons": "^6.4.18", "axios": "^1.9.0", "buffer": "^6.0.3", "jotai": "^2.12.4", "react": "19.0.0", "react-native": "0.79.2", "react-native-audio-recorder-player": "^3.6.12", "react-native-ble-plx": "^3.5.0", "react-native-device-info": "^14.0.4", "react-native-fs": "^2.20.0", "react-native-logs": "^5.3.0", "react-native-markdown-display": "^7.0.2", "react-native-permissions": "^5.4.0", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "^4.10.0", "react-native-simple-toast": "^3.3.2", "react-native-sse": "^1.2.1", "react-native-vector-icons": "^10.2.0", "react-native-wifi-reborn": "^4.13.5"}, "devDependencies": {"@babel/core": "^7.27.1", "@babel/preset-env": "^7.27.2", "@babel/runtime": "^7.27.1", "@react-native-community/cli": "18.0.0", "@react-native-community/cli-platform-android": "18.0.0", "@react-native-community/cli-platform-ios": "18.0.0", "@react-native/babel-preset": "0.79.2", "@react-native/eslint-config": "0.79.2", "@react-native/metro-config": "0.79.2", "@react-native/typescript-config": "0.79.2", "@types/jest": "^29.5.14", "@types/react": "^19.0.0", "@types/react-test-renderer": "^19.0.0", "eslint": "^9.26.0", "jest": "^29.7.0", "prettier": "3.5.3", "react-native-asset": "^2.1.1", "react-test-renderer": "19.0.0", "typescript": "5.8.3"}, "engines": {"node": ">=18"}}