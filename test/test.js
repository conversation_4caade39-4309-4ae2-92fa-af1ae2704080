/**
 * CRC16-CCITT算法实现 (多项式: 0x1021, 初始值: 0x0000)
 * 也称为CRC16-XMODEM
 * @param {number[]} data - 字节数组
 * @returns {number} - CRC16值
 */
function crc16(data) {
  let crc = 0x0000;
  for (let i = 0; i < data.length; i++) {
    crc ^= data[i] << 8;
    for (let j = 0; j < 8; j++) {
      if (crc & 0x8000) {
        crc = (crc << 1) ^ 0x1021;
      } else {
        crc = crc << 1;
      }
    }
  }
  return crc & 0xffff;
}

/**
 * 验证CRC16计算结果
 * @param {number[]} data - 输入数据字节数组
 * @param {number[]} expectedBytes - 期望的CRC16字节数组 [高字节, 低字节]
 * @returns {boolean} - 是否匹配
 */
function verifyCRC16(data, expectedBytes) {
  const result = crc16(data);
  const resultBytes = [(result >> 8) & 0xff, result & 0xff]; // 大端序

  console.log(
    `CRC16计算结果: 0x${result.toString(16).toUpperCase().padStart(4, '0')}`,
  );
  console.log(
    `结果字节: [${resultBytes.map(b => '0x' + b.toString(16).toUpperCase().padStart(2, '0')).join(', ')}]`,
  );
  console.log(
    `期望字节: [${expectedBytes.map(b => '0x' + b.toString(16).toUpperCase().padStart(2, '0')).join(', ')}]`,
  );

  const isMatch =
    resultBytes[0] === expectedBytes[0] && resultBytes[1] === expectedBytes[1];
  console.log(`验证结果: ${isMatch ? '✅ 通过' : '❌ 失败'}`);

  return isMatch;
}

// 测试函数
function testCRC16() {
  const data =
    '00 3B 10 01 7B 22 73 73 69 64 22 3A 22 41 72 74 69 66 69 63 69 61 6C 20 50 72 6F 64 75 63 74 69 76 69 74 79 22 2C 22 70 61 73 73 22 3A 22 73 65 72 76 65 68 75 6D 61 6E 69 74 59 22 7D';
  const crc16Verify = 'D6 B6';
  //   const data = '0 e 10 2 7b 22 73 74 61 74 75 73 22 3a 32 7d';
  //   const crc16Verify = '1a a1';

  const bytes = data.split(' ').map(byte => parseInt(byte, 16));
  const verifyBytes = crc16Verify.split(' ').map(byte => parseInt(byte, 16));

  console.log('=== CRC16算法验证测试 ===');
  console.log(`测试数据长度: ${bytes.length} 字节`);
  console.log(
    `期望CRC16: ${crc16Verify} (0x${verifyBytes[0].toString(16).toUpperCase()}${verifyBytes[1].toString(16).toUpperCase()})`,
  );
  console.log('');

  const result = verifyCRC16(bytes, verifyBytes);

  if (result) {
    console.log('\n🎉 CRC16算法验证成功！');
    console.log('算法类型: CRC16-CCITT (多项式: 0x1021, 初始值: 0x0000)');
  } else {
    console.log('\n❌ CRC16算法验证失败');
  }
}

// 运行测试
testCRC16();
