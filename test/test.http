
// Doll99 API
// This file contains HTTP requests for the Doll99 API.
// The API is used to manage dolls and their information.

@BaseUrl=http://115.190.73.213:8912
# @BaseUrl=https://ap.fifthday.bid
@Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1aWQiOiJkMGplMHEzbjRjdGJqajFrcjNtZyJ9.oGCmRK1PWWa8surJ2vcsOijjIl0vqumpZq89yoB0umY

@DollID=zl
###

// 请求验证码
POST {{BaseUrl}}/app/login/get-verifycode
Content-Type: application/json

{
  "phone": "18600000000"
}

###

// 验证验证码
POST {{BaseUrl}}/app/login/verify-verifycode
Content-Type: application/json

{
  "phone": "18600000000",
  "verifyCode": "6470"
}

###

// 刷新 token
POST {{BaseUrl}}/app/refresh-token
Content-Type: application/json
Authorization: Bearer {{Token}}

{
}

### 
// 绑定小玩偶
POST {{BaseUrl}}/app/doll/bind
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "dollId": "{{DollID}}"
}

###

// 获取小玩偶详情列表
POST {{BaseUrl}}/app/doll/infos
Content-Type: application/json
Authorization: Bearer {{Token}}

{}

###

// 存储聊天内容
POST {{BaseUrl}}/doll/msg/save
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "dollId": "{{DollID}}",
  "query": "我今天不开心",
  "answer": "我陪你一起玩吧"
}

###

// 获取聊天内容
POST {{BaseUrl}}/app/msg/get-doll
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "dollId": "{{DollID}}",
  "limit": 1,
  "timestamp": 0,
  "op": "before"
}

###

// 获取聊天总结
POST {{BaseUrl}}/app/msg/get-summary
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "dollId": "{{DollID}}"
}

###

// 获取角色列表
POST {{BaseUrl}}/app/role/get-role
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "dollId": "{{DollID}}"
}

###

// 切换角色
POST {{BaseUrl}}/app/role/change
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "dollId": "{{DollID}}",
  "newRoleId": "r_2"
}

###

// 删除角色
POST {{BaseUrl}}/app/role/delete
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "dollId": "{{DollID}}",
  "roleId": "{{DollID}}_d0gvbi3n4ct31vgt7mig"
}

###

// 获取硬件状态（电量、音量等）
POST {{BaseUrl}}/app/doll/get-state
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "dollId": "661"
}

###

// 家长助手聊天
POST {{BaseUrl}}/app/assistant/chat
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "queryType": "text",
  "query": "孩子今天在干嘛？"
}

### 

// 一键生成角色内容
POST {{BaseUrl}}/app/role/gen
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "dollId": "661"
}

###

// 创建/修改自定义角色
POST {{BaseUrl}}/app/role/upsert
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "dollId": "661",
  "roleId": "",
  "roleContent": "我是一个智慧的故事讲述者",
  "timbreId": "original_1"
}

###

// 获取音色列表
POST {{BaseUrl}}/app/timbre/list
Content-Type: application/json
Authorization: Bearer {{Token}}

{}

###

// 删除音色
POST {{BaseUrl}}/app/timbre/delete
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "timbreId": "t_001"
}

###

// 录制音色（voice 为 base64 编码音频，最长 20 秒）
POST {{BaseUrl}}/app/timbre/record
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "voice": "BASE64_ENCODED_AUDIO"
}

###

// 获取家长助手消息记录
POST {{BaseUrl}}/app/msg/get-parent
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "dollId": "{{DollID}}",
  "offset": 0,
  "limit": 20
}

###

// 获取订阅链接
POST {{BaseUrl}}/app/setting/subscribe
Content-Type: application/json
Authorization: Bearer {{Token}}

{}

###

// 获取知识库导入链接
POST {{BaseUrl}}/app/setting/repository
Content-Type: application/json
Authorization: Bearer {{Token}}

{}


###

// 退出登录
POST {{BaseUrl}}/app/setting/quit-account
Content-Type: application/json
Authorization: Bearer {{Token}}

{}

###

// 注销账号
POST {{BaseUrl}}/app/setting/delete-account
Content-Type: application/json
Authorization: Bearer {{Token}}

{}



