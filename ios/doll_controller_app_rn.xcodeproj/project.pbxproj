// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		004D3D094CE241FCA6EA2F7C /* chat_bg_purple.png in Resources */ = {isa = PBXBuildFile; fileRef = B27756FA35FC4B96B2097297 /* chat_bg_purple.png */; };
		07AD6D69A9D34A90AAA1A2F8 /* union_3x.png in Resources */ = {isa = PBXBuildFile; fileRef = 513F378B33014603ADF35037 /* union_3x.png */; };
		083750089B13470BB22DC829 /* group_538_3x.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D9F46A27864424D8126459B /* group_538_3x.png */; };
		0ABCADBD391442E386C5C30F /* circle_avatar_purple_b.png in Resources */ = {isa = PBXBuildFile; fileRef = 3ADC2489831E41EAAB2E3CEE /* circle_avatar_purple_b.png */; };
		0C0C4C0D85C04797AC1BA7EA /* group_46_3x.png in Resources */ = {isa = PBXBuildFile; fileRef = 7EC132F5D1844414B98D2974 /* group_46_3x.png */; };
		0C80B921A6F3F58F76C31292 /* libPods-doll_controller_app_rn.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 5DCACB8F33CDC322A6C60F78 /* libPods-doll_controller_app_rn.a */; };
		0D2DBC30F37A4334B26B5C47 /* group_537_3x.png in Resources */ = {isa = PBXBuildFile; fileRef = 44CA415A8D7A410EA9E8023D /* group_537_3x.png */; };
		1063A25DED3540F49CBDFFC2 /* modal_bg_2.png in Resources */ = {isa = PBXBuildFile; fileRef = EBC5C07283124A7187849C14 /* modal_bg_2.png */; };
		10B2937563BF4202B3FDFF5C /* group_535_3x.png in Resources */ = {isa = PBXBuildFile; fileRef = 787887C031964A7F8DDCF51B /* group_535_3x.png */; };
		10B957E1B67848EEB947E283 /* mask_group_3x_15_.png in Resources */ = {isa = PBXBuildFile; fileRef = D38C34BD09AA4EC9A743C2DD /* mask_group_3x_15_.png */; };
		13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		13E4A96869064DBC8B954564 /* battery_green.png in Resources */ = {isa = PBXBuildFile; fileRef = 8B366FDF994542E3B97C8A02 /* battery_green.png */; };
		179A2E3A42674A98AFB35116 /* group_342_3x.png in Resources */ = {isa = PBXBuildFile; fileRef = A6CFFD8CD5854403BA8E60AB /* group_342_3x.png */; };
		1887688E04D146DF8463AF34 /* lujing_4660_3x.png in Resources */ = {isa = PBXBuildFile; fileRef = 463446C10268438AB5D17446 /* lujing_4660_3x.png */; };
		1B81C6B4B13B41E985CFD3C6 /* group_227_3x_1_.png in Resources */ = {isa = PBXBuildFile; fileRef = F3B5D60B04E242419638E2E8 /* group_227_3x_1_.png */; };
		1EF409662F414D34B6903E66 /* star_6_3x.png in Resources */ = {isa = PBXBuildFile; fileRef = 271D61E3E64941B59D833091 /* star_6_3x.png */; };
		21FA913B9F5047F4B747AEE7 /* mask_group_3x.png in Resources */ = {isa = PBXBuildFile; fileRef = B3F48AFD74FC45DAB1BCC2EA /* mask_group_3x.png */; };
		22895DAA8BA14A28AD569760 /* lujing_4762_3x.png in Resources */ = {isa = PBXBuildFile; fileRef = 6E4A62B6A73E45F2B52F0756 /* lujing_4762_3x.png */; };
		233B799751DB4131842117CB /* summary_board_blue.png in Resources */ = {isa = PBXBuildFile; fileRef = 12A4845902AA4C61B28EC787 /* summary_board_blue.png */; };
		28073AD5A12B4AFD8AD16E40 /* voice_playing_icon_1_left.png in Resources */ = {isa = PBXBuildFile; fileRef = 2237AD7011BE48E4A8C73629 /* voice_playing_icon_1_left.png */; };
		28381B0A1A85470F9CE3CE76 /* battery_yellow_charge.png in Resources */ = {isa = PBXBuildFile; fileRef = E4666E173C394930B1DE69B6 /* battery_yellow_charge.png */; };
		29A2F242B26048B689B7368A /* voice_btn_icon_0.png in Resources */ = {isa = PBXBuildFile; fileRef = 0978EEB79F474684A7CCD3CA /* voice_btn_icon_0.png */; };
		2B97BAAC034A474AB422B5C7 /* lujing_4645_3x.png in Resources */ = {isa = PBXBuildFile; fileRef = 1315ACBB34A046C5B96761B4 /* lujing_4645_3x.png */; };
		2D77BCA507AF46F186FF113D /* summary_bg_content.png in Resources */ = {isa = PBXBuildFile; fileRef = 1E4B02FB9F074CECAA9645F3 /* summary_bg_content.png */; };
		2E457700007949F8BF1E3508 /* group_258_3x.png in Resources */ = {isa = PBXBuildFile; fileRef = 36EAD7C370A44702B706FBFA /* group_258_3x.png */; };
		3268E18328C6454CB0B37706 /* group_257_3x.png in Resources */ = {isa = PBXBuildFile; fileRef = 526C43188F5A426BAA946248 /* group_257_3x.png */; };
		341678032F7547E587D899D2 /* lujing_4812_3x.png in Resources */ = {isa = PBXBuildFile; fileRef = 3A31B6EAD1094403A1A77775 /* lujing_4812_3x.png */; };
		34DFD3D2D6354D95B030DCAD /* lujing_4684_3x.png in Resources */ = {isa = PBXBuildFile; fileRef = E84A96EB831A409E9925F30A /* lujing_4684_3x.png */; };
		36CC0FCF76AD4441BFAE1EEE /* voice_playing_icon_1_right.png in Resources */ = {isa = PBXBuildFile; fileRef = ED8E68C337A04D8DA1C1BCD0 /* voice_playing_icon_1_right.png */; };
		379F8CAD9DBF47CC89E50CDD /* chat_bg_rabbit.png in Resources */ = {isa = PBXBuildFile; fileRef = 9212D9E02B094A368700E22E /* chat_bg_rabbit.png */; };
		3BF17B19AF904BFA8C78B28B /* lujing_4895_3x.png in Resources */ = {isa = PBXBuildFile; fileRef = 37F50524D1A44C259FCC7310 /* lujing_4895_3x.png */; };
		3C6CF1A223574A8795052491 /* group_260_3x.png in Resources */ = {isa = PBXBuildFile; fileRef = 5D23A81825994FB7B34EE686 /* group_260_3x.png */; };
		3D07D4D24F9A4FD58C861429 /* voice_playing_icon_3_left.png in Resources */ = {isa = PBXBuildFile; fileRef = EFE5B558D2BB46F9B1A560D8 /* voice_playing_icon_3_left.png */; };
		4094DBD97B0B4D3495B8B005 /* lujing_4807_3x.png in Resources */ = {isa = PBXBuildFile; fileRef = E2C1D2213D3D42DF905DBA40 /* lujing_4807_3x.png */; };
		4169ACE5E16440DF86F8C926 /* circle_avatar_yellow_b.png in Resources */ = {isa = PBXBuildFile; fileRef = A11DD136841F451685B24605 /* circle_avatar_yellow_b.png */; };
		41B3105870974A07BC1B95C8 /* img_8g_3x.png in Resources */ = {isa = PBXBuildFile; fileRef = DD385D899A654967836AB959 /* img_8g_3x.png */; };
		44E422B3955445DD81F79DD8 /* avatar_rabbit.png in Resources */ = {isa = PBXBuildFile; fileRef = A1AD807CFEBD44A484E9A3D7 /* avatar_rabbit.png */; };
		4869F84F8C8340F58BE32F59 /* mask_group_3x_12_.png in Resources */ = {isa = PBXBuildFile; fileRef = 038E6FA2BE5B417E84745570 /* mask_group_3x_12_.png */; };
		48F344BA22CD4738AC4ECAFA /* mask_group_3x_4_.png in Resources */ = {isa = PBXBuildFile; fileRef = A35A0C1ABF0D48A19077119D /* mask_group_3x_4_.png */; };
		4AF64873734745A28248F8B9 /* AlibabaPuHuiTi-3-55-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 5FBA274542754E368E11DF19 /* AlibabaPuHuiTi-3-55-Regular.ttf */; };
		51CC6F34F94B4E19ABF38666 /* group_339_3x.png in Resources */ = {isa = PBXBuildFile; fileRef = 0FE55713072E48678186FEE8 /* group_339_3x.png */; };
		54483B5DE9CF4352AC38538C /* circle_avatar_blue_b.png in Resources */ = {isa = PBXBuildFile; fileRef = 591FAD5385C04111A9B2B14C /* circle_avatar_blue_b.png */; };
		56A6D180B89F426EB06726B1 /* circle_avatar_purple_p.png in Resources */ = {isa = PBXBuildFile; fileRef = 155D65A694C74BF08D27780F /* circle_avatar_purple_p.png */; };
		57EDF6FD02C2430E8EA53D48 /* vector_3x.png in Resources */ = {isa = PBXBuildFile; fileRef = 9F06298A447A46CD8B757480 /* vector_3x.png */; };
		5B8EAA5287384EAAA7B41E19 /* lujing_4895_3x_2_.png in Resources */ = {isa = PBXBuildFile; fileRef = 8025F74352DE44B6BEC9783C /* lujing_4895_3x_2_.png */; };
		5B989DAB0567426F9AF16551 /* group_47_3x.png in Resources */ = {isa = PBXBuildFile; fileRef = 2D5A565D33AB4276BDFBD2B9 /* group_47_3x.png */; };
		5BEA763829AF4E8294EE74AE /* zu_984_3x.png in Resources */ = {isa = PBXBuildFile; fileRef = 55FA2790979044C1B1F02D4F /* zu_984_3x.png */; };
		605081C7B5074C9C9520B0A8 /* battery_full.png in Resources */ = {isa = PBXBuildFile; fileRef = 1514E54524204595B1D9DAE0 /* battery_full.png */; };
		6078A2E28C674F1E89C16331 /* zu_987_3x.png in Resources */ = {isa = PBXBuildFile; fileRef = D02EB1895FAB47DCBAADC7FB /* zu_987_3x.png */; };
		6079EDACB7F04FD4B4463189 /* bg_doll.png in Resources */ = {isa = PBXBuildFile; fileRef = 2D13D57776644737AB132262 /* bg_doll.png */; };
		65C3930980DE475084CE2D53 /* voice_playing_icon_3_right.png in Resources */ = {isa = PBXBuildFile; fileRef = A7D1143C5C10437C8C0483BE /* voice_playing_icon_3_right.png */; };
		6CF68EFD0D8C4609A3050609 /* circle_avatar_rabbit_p.png in Resources */ = {isa = PBXBuildFile; fileRef = 80FE2DAA11FD4E4884ABBC47 /* circle_avatar_rabbit_p.png */; };
		71B36194CB484D30BDCEA857 /* summary_board_rabbit.png in Resources */ = {isa = PBXBuildFile; fileRef = 1CA5FB03E484433DA743A0F2 /* summary_board_rabbit.png */; };
		7457521FF77445B789DE02C0 /* summary_board_yellow.png in Resources */ = {isa = PBXBuildFile; fileRef = C2BB900DC758468B99E30241 /* summary_board_yellow.png */; };
		761780ED2CA45674006654EE /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 761780EC2CA45674006654EE /* AppDelegate.swift */; };
		77CD0CE793A94674AA8922AB /* summary_bg_header.png in Resources */ = {isa = PBXBuildFile; fileRef = 3029552AF5D94D52A1A15D68 /* summary_bg_header.png */; };
		7DAAF95BFC734D67925011F2 /* zu_976_3x.png in Resources */ = {isa = PBXBuildFile; fileRef = C435A4A4DC72432AB3F59549 /* zu_976_3x.png */; };
		81AB9BB82411601600AC10FF /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */; };
		82B3604F9B3E47A19E5E2411 /* mask_group_3x_5_.png in Resources */ = {isa = PBXBuildFile; fileRef = A0291AA8F4834234A4BB8134 /* mask_group_3x_5_.png */; };
		8477D890FFEB4BDE95676C2D /* circle_avatar_pink_b.png in Resources */ = {isa = PBXBuildFile; fileRef = 1B0FE9D3244E4CC09235D70E /* circle_avatar_pink_b.png */; };
		8AD8766D5DF94F9B9A82FC16 /* group_534_3x.png in Resources */ = {isa = PBXBuildFile; fileRef = E6A1065579114EE0AE65951E /* group_534_3x.png */; };
		8C239481932C4A5C81F6423E /* bg_chat.png in Resources */ = {isa = PBXBuildFile; fileRef = 7DC45B28526640429C88CB3A /* bg_chat.png */; };
		8CE4CBD31B9E46259BDF3C02 /* mask_group_3x_10_.png in Resources */ = {isa = PBXBuildFile; fileRef = 996B5DA6F58F4ADF8D740B78 /* mask_group_3x_10_.png */; };
		8D9FD04E148140C6AE97508A /* mask_group_3x_6_.png in Resources */ = {isa = PBXBuildFile; fileRef = 4F2240CC8F314B339C8AF828 /* mask_group_3x_6_.png */; };
		90211203C5BA4061A500A380 /* voice_btn_icon_1.png in Resources */ = {isa = PBXBuildFile; fileRef = AE96CD6B970A45468A5F238A /* voice_btn_icon_1.png */; };
		90F31D47058F4A95A4AF558B /* lujing_4686_3x.png in Resources */ = {isa = PBXBuildFile; fileRef = F282D355FF9244FEA1080A9B /* lujing_4686_3x.png */; };
		92934E8A253A457C90C61F56 /* battery_yellow.png in Resources */ = {isa = PBXBuildFile; fileRef = 533A54F0D51F4BCCB5D8DD1E /* battery_yellow.png */; };
		92C76D9F9FEC4ECDAB24952A /* group_539_3x.png in Resources */ = {isa = PBXBuildFile; fileRef = 843464D187994E71A80E362E /* group_539_3x.png */; };
		9359733A75874D2D8626992E /* avatar_pink.png in Resources */ = {isa = PBXBuildFile; fileRef = 6CA9A7651DFD4120A226A598 /* avatar_pink.png */; };
		93D990AD0DE5442C99280766 /* lujing_4683_3x.png in Resources */ = {isa = PBXBuildFile; fileRef = 713E69D8E8984FB688DCC89B /* lujing_4683_3x.png */; };
		9422EA0E17CC406B9C528863 /* circle_avatar_yellow_p.png in Resources */ = {isa = PBXBuildFile; fileRef = 238F0BC7ADA444148C7F1C65 /* circle_avatar_yellow_p.png */; };
		9770F85BF3014E929549BDD8 /* circle_avatar_pink_p.png in Resources */ = {isa = PBXBuildFile; fileRef = 6DE8C216AE4F445399DD21DE /* circle_avatar_pink_p.png */; };
		97A5DA5392DA4A62BBD33E5C /* lujing_4686_3x_1_.png in Resources */ = {isa = PBXBuildFile; fileRef = E06A5A785C1446578B0DDC8A /* lujing_4686_3x_1_.png */; };
		987E4B6266E6E4C2A1AECDC2 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB81A68108700A75B9A /* PrivacyInfo.xcprivacy */; };
		98C042B6D8624F3EA2680C99 /* battery_red_charge.png in Resources */ = {isa = PBXBuildFile; fileRef = 14A82696C40F44AA95220DC0 /* battery_red_charge.png */; };
		9E9DCAC476F048F6B0F55409 /* voice_playing_icon_2_right.png in Resources */ = {isa = PBXBuildFile; fileRef = F327CCE6E72F4C38B6E7F0C1 /* voice_playing_icon_2_right.png */; };
		A4D8C97728F24B8A80CCAEFD /* mask_group_3x_7_.png in Resources */ = {isa = PBXBuildFile; fileRef = 8312F6B43D584AA6B8B56E10 /* mask_group_3x_7_.png */; };
		A6907758FB6C45E79FC2A78F /* group_244_3x.png in Resources */ = {isa = PBXBuildFile; fileRef = EA39669BB04A4EDA885F8500 /* group_244_3x.png */; };
		A9405414D9A941409A42A6E8 /* lujing_4849_3x.png in Resources */ = {isa = PBXBuildFile; fileRef = 0A55E1B79B8C4EB3B9C5FCB1 /* lujing_4849_3x.png */; };
		AA1EFAECBD164EF0AA9C207F /* modal_bg_3.png in Resources */ = {isa = PBXBuildFile; fileRef = A3BE732CAD4445E3BC287837 /* modal_bg_3.png */; };
		AC4C8971591F47538A697F7E /* mask_group_3x_2_.png in Resources */ = {isa = PBXBuildFile; fileRef = 8D400478FD614EA38077C681 /* mask_group_3x_2_.png */; };
		AC51413347CF4AFBAB3FDC4C /* summary_board_purple.png in Resources */ = {isa = PBXBuildFile; fileRef = 7296F63001B34988A73DD60A /* summary_board_purple.png */; };
		B25FDB4F317C4DDD9368CB69 /* mask_group_3x_1_.png in Resources */ = {isa = PBXBuildFile; fileRef = E60792D660574C3C904884D3 /* mask_group_3x_1_.png */; };
		B5878A4A7A724A619A82E8DD /* group_536_3x.png in Resources */ = {isa = PBXBuildFile; fileRef = B678675FA7894D4ABBF6161F /* group_536_3x.png */; };
		B6DC56DA46D341608E539BFB /* voice_playing_icon_2_left.png in Resources */ = {isa = PBXBuildFile; fileRef = 19C1F0762F5840C996F953DB /* voice_playing_icon_2_left.png */; };
		BE7FA4A8D39B41FEBE0BE0FB /* btn_profile.png in Resources */ = {isa = PBXBuildFile; fileRef = BC8DC1E41E6B453C856E7449 /* btn_profile.png */; };
		C0CEA80488D648429651C8C1 /* avatar_purple.png in Resources */ = {isa = PBXBuildFile; fileRef = 116B932EB7174ABBB8C5BA28 /* avatar_purple.png */; };
		C18E02881D534A31A6768663 /* voice_btn_icon_2.png in Resources */ = {isa = PBXBuildFile; fileRef = 88791EF7A2CE4AA28A065F3D /* voice_btn_icon_2.png */; };
		C1E17DB6FAA2453D95B4319A /* avatar_yellow.png in Resources */ = {isa = PBXBuildFile; fileRef = 1FD4897150594688A04471E4 /* avatar_yellow.png */; };
		C22E7B8FCF9F43BD87F8D689 /* chat_bg_yellow.png in Resources */ = {isa = PBXBuildFile; fileRef = 05E9BAF799CD46A1A97ABEB6 /* chat_bg_yellow.png */; };
		C47E443DB37B4351910F910F /* group_4_3x.png in Resources */ = {isa = PBXBuildFile; fileRef = ECCE3CE1345047F0A4CB7871 /* group_4_3x.png */; };
		C7D6EC93AB344923AD513C8A /* bg_profile.png in Resources */ = {isa = PBXBuildFile; fileRef = 370A53EDD04B46D8BF9E8856 /* bg_profile.png */; };
		D23E0183493F471FA06F4748 /* summary_board_pink.png in Resources */ = {isa = PBXBuildFile; fileRef = BF00E49FB8104A8194F1D2D4 /* summary_board_pink.png */; };
		D381F872C25C4EC581B33401 /* battery_full_charge.png in Resources */ = {isa = PBXBuildFile; fileRef = 2020CAC3497B4D8EBACC52FE /* battery_full_charge.png */; };
		D4AB782AF09844139ADEE801 /* battery_green_charge.png in Resources */ = {isa = PBXBuildFile; fileRef = 33F94190C837421E9D80F417 /* battery_green_charge.png */; };
		DAFAC46D60BF40E3B94BB890 /* circle_avatar_rabbit_b.png in Resources */ = {isa = PBXBuildFile; fileRef = C48102F96B874795B1E51461 /* circle_avatar_rabbit_b.png */; };
		DB42A99A4E0D4F2292464D4D /* bg_login.png in Resources */ = {isa = PBXBuildFile; fileRef = B1DC62D1F0D24B09A5556BB6 /* bg_login.png */; };
		DE1D8DD5A8EE48FCBA59EB84 /* group_237_3x.png in Resources */ = {isa = PBXBuildFile; fileRef = 0C19CFEAC37C4AEEB4CC4975 /* group_237_3x.png */; };
		E0AC52E7FE4A461880798B02 /* battery_red.png in Resources */ = {isa = PBXBuildFile; fileRef = B6D18A1B60E4452091210BAA /* battery_red.png */; };
		E2D76A43A6EF4BAA9A8B7F7C /* tuceng_2_3x.png in Resources */ = {isa = PBXBuildFile; fileRef = AD3FFF9E9A00422F9E59B71C /* tuceng_2_3x.png */; };
		E3B2DC6B07304D0891A4B3BC /* chat_bg_blue.png in Resources */ = {isa = PBXBuildFile; fileRef = 51899771B1E54F14B9967123 /* chat_bg_blue.png */; };
		E44341E7652A4E8483E3841A /* bg_splash.png in Resources */ = {isa = PBXBuildFile; fileRef = 72EA85B253FE49018D2BC5E1 /* bg_splash.png */; };
		EC425049C9834C44ABCE1E4E /* g_3x.png in Resources */ = {isa = PBXBuildFile; fileRef = DAED5CE7A2B14EC783113441 /* g_3x.png */; };
		ED150DC2EA5E4052A815C958 /* mask_group_3x_11_.png in Resources */ = {isa = PBXBuildFile; fileRef = 208878EF972742119EE11A65 /* mask_group_3x_11_.png */; };
		EE332D927B284787A00B303D /* avatar_blue.png in Resources */ = {isa = PBXBuildFile; fileRef = 7EC912AD312841F59C75174E /* avatar_blue.png */; };
		F46DD90DE533434B835E9EA3 /* voice_playing_icon_0.png in Resources */ = {isa = PBXBuildFile; fileRef = 05FDF9D97E5C441F9A76BD27 /* voice_playing_icon_0.png */; };
		F84DF5C9F74C4796A95C063F /* chat_bg_pink.png in Resources */ = {isa = PBXBuildFile; fileRef = 93FB3F74134A4554BB9FA71B /* chat_bg_pink.png */; };
		F8BC8DF91913499886D583AB /* img_15_1_3x.png in Resources */ = {isa = PBXBuildFile; fileRef = B8A94037646048E88322012D /* img_15_1_3x.png */; };
		F8ED10C2230247BDB9D42A34 /* circle_avatar_blue_p.png in Resources */ = {isa = PBXBuildFile; fileRef = 6247579271FE41DBA86435F7 /* circle_avatar_blue_p.png */; };
		FA08DFA435F940299E98AE45 /* doll_list_item_bg.png in Resources */ = {isa = PBXBuildFile; fileRef = 773309FA8CAB41D7B03C2459 /* doll_list_item_bg.png */; };
		FB222EDE664E41E8A7F78E33 /* modal_bg_1.png in Resources */ = {isa = PBXBuildFile; fileRef = 3725DBD950324B2481CEAF01 /* modal_bg_1.png */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		038E6FA2BE5B417E84745570 /* mask_group_3x_12_.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = mask_group_3x_12_.png; path = ../app/assets/img/mask_group_3x_12_.png; sourceTree = "<group>"; };
		05E9BAF799CD46A1A97ABEB6 /* chat_bg_yellow.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = chat_bg_yellow.png; path = ../app/assets/img/chat_bg_yellow.png; sourceTree = "<group>"; };
		05FDF9D97E5C441F9A76BD27 /* voice_playing_icon_0.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = voice_playing_icon_0.png; path = ../app/assets/img/voice_playing_icon_0.png; sourceTree = "<group>"; };
		0978EEB79F474684A7CCD3CA /* voice_btn_icon_0.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = voice_btn_icon_0.png; path = ../app/assets/img/voice_btn_icon_0.png; sourceTree = "<group>"; };
		0A55E1B79B8C4EB3B9C5FCB1 /* lujing_4849_3x.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = lujing_4849_3x.png; path = ../app/assets/img/lujing_4849_3x.png; sourceTree = "<group>"; };
		0C19CFEAC37C4AEEB4CC4975 /* group_237_3x.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = group_237_3x.png; path = ../app/assets/img/group_237_3x.png; sourceTree = "<group>"; };
		0D9F46A27864424D8126459B /* group_538_3x.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = group_538_3x.png; path = ../app/assets/img/group_538_3x.png; sourceTree = "<group>"; };
		0FE55713072E48678186FEE8 /* group_339_3x.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = group_339_3x.png; path = ../app/assets/img/group_339_3x.png; sourceTree = "<group>"; };
		116B932EB7174ABBB8C5BA28 /* avatar_purple.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = avatar_purple.png; path = ../app/assets/img/avatar_purple.png; sourceTree = "<group>"; };
		12A4845902AA4C61B28EC787 /* summary_board_blue.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = summary_board_blue.png; path = ../app/assets/img/summary_board_blue.png; sourceTree = "<group>"; };
		1315ACBB34A046C5B96761B4 /* lujing_4645_3x.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = lujing_4645_3x.png; path = ../app/assets/img/lujing_4645_3x.png; sourceTree = "<group>"; };
		13B07F961A680F5B00A75B9A /* doll_controller_app_rn.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = doll_controller_app_rn.app; sourceTree = BUILT_PRODUCTS_DIR; };
		13B07FB51A68108700A75B9A /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = doll_controller_app_rn/Images.xcassets; sourceTree = "<group>"; };
		13B07FB61A68108700A75B9A /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = Info.plist; path = doll_controller_app_rn/Info.plist; sourceTree = "<group>"; };
		13B07FB81A68108700A75B9A /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = PrivacyInfo.xcprivacy; path = doll_controller_app_rn/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		14A82696C40F44AA95220DC0 /* battery_red_charge.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = battery_red_charge.png; path = ../app/assets/img/battery_red_charge.png; sourceTree = "<group>"; };
		1514E54524204595B1D9DAE0 /* battery_full.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = battery_full.png; path = ../app/assets/img/battery_full.png; sourceTree = "<group>"; };
		155D65A694C74BF08D27780F /* circle_avatar_purple_p.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = circle_avatar_purple_p.png; path = ../app/assets/img/circle_avatar_purple_p.png; sourceTree = "<group>"; };
		19C1F0762F5840C996F953DB /* voice_playing_icon_2_left.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = voice_playing_icon_2_left.png; path = ../app/assets/img/voice_playing_icon_2_left.png; sourceTree = "<group>"; };
		1B0FE9D3244E4CC09235D70E /* circle_avatar_pink_b.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = circle_avatar_pink_b.png; path = ../app/assets/img/circle_avatar_pink_b.png; sourceTree = "<group>"; };
		1CA5FB03E484433DA743A0F2 /* summary_board_rabbit.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = summary_board_rabbit.png; path = ../app/assets/img/summary_board_rabbit.png; sourceTree = "<group>"; };
		1E4B02FB9F074CECAA9645F3 /* summary_bg_content.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = summary_bg_content.png; path = ../app/assets/img/summary_bg_content.png; sourceTree = "<group>"; };
		1FD4897150594688A04471E4 /* avatar_yellow.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = avatar_yellow.png; path = ../app/assets/img/avatar_yellow.png; sourceTree = "<group>"; };
		2020CAC3497B4D8EBACC52FE /* battery_full_charge.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = battery_full_charge.png; path = ../app/assets/img/battery_full_charge.png; sourceTree = "<group>"; };
		208878EF972742119EE11A65 /* mask_group_3x_11_.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = mask_group_3x_11_.png; path = ../app/assets/img/mask_group_3x_11_.png; sourceTree = "<group>"; };
		2237AD7011BE48E4A8C73629 /* voice_playing_icon_1_left.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = voice_playing_icon_1_left.png; path = ../app/assets/img/voice_playing_icon_1_left.png; sourceTree = "<group>"; };
		238F0BC7ADA444148C7F1C65 /* circle_avatar_yellow_p.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = circle_avatar_yellow_p.png; path = ../app/assets/img/circle_avatar_yellow_p.png; sourceTree = "<group>"; };
		271D61E3E64941B59D833091 /* star_6_3x.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = star_6_3x.png; path = ../app/assets/img/star_6_3x.png; sourceTree = "<group>"; };
		2D13D57776644737AB132262 /* bg_doll.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = bg_doll.png; path = ../app/assets/img/bg_doll.png; sourceTree = "<group>"; };
		2D5A565D33AB4276BDFBD2B9 /* group_47_3x.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = group_47_3x.png; path = ../app/assets/img/group_47_3x.png; sourceTree = "<group>"; };
		3029552AF5D94D52A1A15D68 /* summary_bg_header.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = summary_bg_header.png; path = ../app/assets/img/summary_bg_header.png; sourceTree = "<group>"; };
		33F94190C837421E9D80F417 /* battery_green_charge.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = battery_green_charge.png; path = ../app/assets/img/battery_green_charge.png; sourceTree = "<group>"; };
		36EAD7C370A44702B706FBFA /* group_258_3x.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = group_258_3x.png; path = ../app/assets/img/group_258_3x.png; sourceTree = "<group>"; };
		370A53EDD04B46D8BF9E8856 /* bg_profile.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = bg_profile.png; path = ../app/assets/img/bg_profile.png; sourceTree = "<group>"; };
		3725DBD950324B2481CEAF01 /* modal_bg_1.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = modal_bg_1.png; path = ../app/assets/img/modal_bg_1.png; sourceTree = "<group>"; };
		37F50524D1A44C259FCC7310 /* lujing_4895_3x.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = lujing_4895_3x.png; path = ../app/assets/img/lujing_4895_3x.png; sourceTree = "<group>"; };
		3A31B6EAD1094403A1A77775 /* lujing_4812_3x.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = lujing_4812_3x.png; path = ../app/assets/img/lujing_4812_3x.png; sourceTree = "<group>"; };
		3ADC2489831E41EAAB2E3CEE /* circle_avatar_purple_b.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = circle_avatar_purple_b.png; path = ../app/assets/img/circle_avatar_purple_b.png; sourceTree = "<group>"; };
		3B4392A12AC88292D35C810B /* Pods-doll_controller_app_rn.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-doll_controller_app_rn.debug.xcconfig"; path = "Target Support Files/Pods-doll_controller_app_rn/Pods-doll_controller_app_rn.debug.xcconfig"; sourceTree = "<group>"; };
		44CA415A8D7A410EA9E8023D /* group_537_3x.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = group_537_3x.png; path = ../app/assets/img/group_537_3x.png; sourceTree = "<group>"; };
		463446C10268438AB5D17446 /* lujing_4660_3x.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = lujing_4660_3x.png; path = ../app/assets/img/lujing_4660_3x.png; sourceTree = "<group>"; };
		4F2240CC8F314B339C8AF828 /* mask_group_3x_6_.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = mask_group_3x_6_.png; path = ../app/assets/img/mask_group_3x_6_.png; sourceTree = "<group>"; };
		513F378B33014603ADF35037 /* union_3x.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = union_3x.png; path = ../app/assets/img/union_3x.png; sourceTree = "<group>"; };
		51899771B1E54F14B9967123 /* chat_bg_blue.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = chat_bg_blue.png; path = ../app/assets/img/chat_bg_blue.png; sourceTree = "<group>"; };
		526C43188F5A426BAA946248 /* group_257_3x.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = group_257_3x.png; path = ../app/assets/img/group_257_3x.png; sourceTree = "<group>"; };
		533A54F0D51F4BCCB5D8DD1E /* battery_yellow.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = battery_yellow.png; path = ../app/assets/img/battery_yellow.png; sourceTree = "<group>"; };
		55FA2790979044C1B1F02D4F /* zu_984_3x.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = zu_984_3x.png; path = ../app/assets/img/zu_984_3x.png; sourceTree = "<group>"; };
		5709B34CF0A7D63546082F79 /* Pods-doll_controller_app_rn.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-doll_controller_app_rn.release.xcconfig"; path = "Target Support Files/Pods-doll_controller_app_rn/Pods-doll_controller_app_rn.release.xcconfig"; sourceTree = "<group>"; };
		591FAD5385C04111A9B2B14C /* circle_avatar_blue_b.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = circle_avatar_blue_b.png; path = ../app/assets/img/circle_avatar_blue_b.png; sourceTree = "<group>"; };
		5D23A81825994FB7B34EE686 /* group_260_3x.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = group_260_3x.png; path = ../app/assets/img/group_260_3x.png; sourceTree = "<group>"; };
		5DCACB8F33CDC322A6C60F78 /* libPods-doll_controller_app_rn.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-doll_controller_app_rn.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		5FBA274542754E368E11DF19 /* AlibabaPuHuiTi-3-55-Regular.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "AlibabaPuHuiTi-3-55-Regular.ttf"; path = "../app/assets/fonts/AlibabaPuHuiTi-3-55-Regular.ttf"; sourceTree = "<group>"; };
		6247579271FE41DBA86435F7 /* circle_avatar_blue_p.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = circle_avatar_blue_p.png; path = ../app/assets/img/circle_avatar_blue_p.png; sourceTree = "<group>"; };
		6CA9A7651DFD4120A226A598 /* avatar_pink.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = avatar_pink.png; path = ../app/assets/img/avatar_pink.png; sourceTree = "<group>"; };
		6DE8C216AE4F445399DD21DE /* circle_avatar_pink_p.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = circle_avatar_pink_p.png; path = ../app/assets/img/circle_avatar_pink_p.png; sourceTree = "<group>"; };
		6E4A62B6A73E45F2B52F0756 /* lujing_4762_3x.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = lujing_4762_3x.png; path = ../app/assets/img/lujing_4762_3x.png; sourceTree = "<group>"; };
		713E69D8E8984FB688DCC89B /* lujing_4683_3x.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = lujing_4683_3x.png; path = ../app/assets/img/lujing_4683_3x.png; sourceTree = "<group>"; };
		7296F63001B34988A73DD60A /* summary_board_purple.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = summary_board_purple.png; path = ../app/assets/img/summary_board_purple.png; sourceTree = "<group>"; };
		72EA85B253FE49018D2BC5E1 /* bg_splash.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = bg_splash.png; path = ../app/assets/img/bg_splash.png; sourceTree = "<group>"; };
		761780EC2CA45674006654EE /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; name = AppDelegate.swift; path = doll_controller_app_rn/AppDelegate.swift; sourceTree = "<group>"; };
		773309FA8CAB41D7B03C2459 /* doll_list_item_bg.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = doll_list_item_bg.png; path = ../app/assets/img/doll_list_item_bg.png; sourceTree = "<group>"; };
		787887C031964A7F8DDCF51B /* group_535_3x.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = group_535_3x.png; path = ../app/assets/img/group_535_3x.png; sourceTree = "<group>"; };
		7DC45B28526640429C88CB3A /* bg_chat.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = bg_chat.png; path = ../app/assets/img/bg_chat.png; sourceTree = "<group>"; };
		7EC132F5D1844414B98D2974 /* group_46_3x.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = group_46_3x.png; path = ../app/assets/img/group_46_3x.png; sourceTree = "<group>"; };
		7EC912AD312841F59C75174E /* avatar_blue.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = avatar_blue.png; path = ../app/assets/img/avatar_blue.png; sourceTree = "<group>"; };
		8025F74352DE44B6BEC9783C /* lujing_4895_3x_2_.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = lujing_4895_3x_2_.png; path = ../app/assets/img/lujing_4895_3x_2_.png; sourceTree = "<group>"; };
		80FE2DAA11FD4E4884ABBC47 /* circle_avatar_rabbit_p.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = circle_avatar_rabbit_p.png; path = ../app/assets/img/circle_avatar_rabbit_p.png; sourceTree = "<group>"; };
		81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; name = LaunchScreen.storyboard; path = doll_controller_app_rn/LaunchScreen.storyboard; sourceTree = "<group>"; };
		8312F6B43D584AA6B8B56E10 /* mask_group_3x_7_.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = mask_group_3x_7_.png; path = ../app/assets/img/mask_group_3x_7_.png; sourceTree = "<group>"; };
		843464D187994E71A80E362E /* group_539_3x.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = group_539_3x.png; path = ../app/assets/img/group_539_3x.png; sourceTree = "<group>"; };
		88791EF7A2CE4AA28A065F3D /* voice_btn_icon_2.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = voice_btn_icon_2.png; path = ../app/assets/img/voice_btn_icon_2.png; sourceTree = "<group>"; };
		8B366FDF994542E3B97C8A02 /* battery_green.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = battery_green.png; path = ../app/assets/img/battery_green.png; sourceTree = "<group>"; };
		8D400478FD614EA38077C681 /* mask_group_3x_2_.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = mask_group_3x_2_.png; path = ../app/assets/img/mask_group_3x_2_.png; sourceTree = "<group>"; };
		9212D9E02B094A368700E22E /* chat_bg_rabbit.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = chat_bg_rabbit.png; path = ../app/assets/img/chat_bg_rabbit.png; sourceTree = "<group>"; };
		93FB3F74134A4554BB9FA71B /* chat_bg_pink.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = chat_bg_pink.png; path = ../app/assets/img/chat_bg_pink.png; sourceTree = "<group>"; };
		996B5DA6F58F4ADF8D740B78 /* mask_group_3x_10_.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = mask_group_3x_10_.png; path = ../app/assets/img/mask_group_3x_10_.png; sourceTree = "<group>"; };
		9F06298A447A46CD8B757480 /* vector_3x.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = vector_3x.png; path = ../app/assets/img/vector_3x.png; sourceTree = "<group>"; };
		A0291AA8F4834234A4BB8134 /* mask_group_3x_5_.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = mask_group_3x_5_.png; path = ../app/assets/img/mask_group_3x_5_.png; sourceTree = "<group>"; };
		A11DD136841F451685B24605 /* circle_avatar_yellow_b.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = circle_avatar_yellow_b.png; path = ../app/assets/img/circle_avatar_yellow_b.png; sourceTree = "<group>"; };
		A1AD807CFEBD44A484E9A3D7 /* avatar_rabbit.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = avatar_rabbit.png; path = ../app/assets/img/avatar_rabbit.png; sourceTree = "<group>"; };
		A35A0C1ABF0D48A19077119D /* mask_group_3x_4_.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = mask_group_3x_4_.png; path = ../app/assets/img/mask_group_3x_4_.png; sourceTree = "<group>"; };
		A3BE732CAD4445E3BC287837 /* modal_bg_3.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = modal_bg_3.png; path = ../app/assets/img/modal_bg_3.png; sourceTree = "<group>"; };
		A6CFFD8CD5854403BA8E60AB /* group_342_3x.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = group_342_3x.png; path = ../app/assets/img/group_342_3x.png; sourceTree = "<group>"; };
		A7D1143C5C10437C8C0483BE /* voice_playing_icon_3_right.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = voice_playing_icon_3_right.png; path = ../app/assets/img/voice_playing_icon_3_right.png; sourceTree = "<group>"; };
		AD3FFF9E9A00422F9E59B71C /* tuceng_2_3x.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = tuceng_2_3x.png; path = ../app/assets/img/tuceng_2_3x.png; sourceTree = "<group>"; };
		AE96CD6B970A45468A5F238A /* voice_btn_icon_1.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = voice_btn_icon_1.png; path = ../app/assets/img/voice_btn_icon_1.png; sourceTree = "<group>"; };
		B1DC62D1F0D24B09A5556BB6 /* bg_login.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = bg_login.png; path = ../app/assets/img/bg_login.png; sourceTree = "<group>"; };
		B27756FA35FC4B96B2097297 /* chat_bg_purple.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = chat_bg_purple.png; path = ../app/assets/img/chat_bg_purple.png; sourceTree = "<group>"; };
		B3F48AFD74FC45DAB1BCC2EA /* mask_group_3x.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = mask_group_3x.png; path = ../app/assets/img/mask_group_3x.png; sourceTree = "<group>"; };
		B678675FA7894D4ABBF6161F /* group_536_3x.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = group_536_3x.png; path = ../app/assets/img/group_536_3x.png; sourceTree = "<group>"; };
		B6D18A1B60E4452091210BAA /* battery_red.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = battery_red.png; path = ../app/assets/img/battery_red.png; sourceTree = "<group>"; };
		B8A94037646048E88322012D /* img_15_1_3x.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = img_15_1_3x.png; path = ../app/assets/img/img_15_1_3x.png; sourceTree = "<group>"; };
		BC8DC1E41E6B453C856E7449 /* btn_profile.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = btn_profile.png; path = ../app/assets/img/btn_profile.png; sourceTree = "<group>"; };
		BF00E49FB8104A8194F1D2D4 /* summary_board_pink.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = summary_board_pink.png; path = ../app/assets/img/summary_board_pink.png; sourceTree = "<group>"; };
		C2BB900DC758468B99E30241 /* summary_board_yellow.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = summary_board_yellow.png; path = ../app/assets/img/summary_board_yellow.png; sourceTree = "<group>"; };
		C435A4A4DC72432AB3F59549 /* zu_976_3x.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = zu_976_3x.png; path = ../app/assets/img/zu_976_3x.png; sourceTree = "<group>"; };
		C48102F96B874795B1E51461 /* circle_avatar_rabbit_b.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = circle_avatar_rabbit_b.png; path = ../app/assets/img/circle_avatar_rabbit_b.png; sourceTree = "<group>"; };
		D02EB1895FAB47DCBAADC7FB /* zu_987_3x.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = zu_987_3x.png; path = ../app/assets/img/zu_987_3x.png; sourceTree = "<group>"; };
		D38C34BD09AA4EC9A743C2DD /* mask_group_3x_15_.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = mask_group_3x_15_.png; path = ../app/assets/img/mask_group_3x_15_.png; sourceTree = "<group>"; };
		DAED5CE7A2B14EC783113441 /* g_3x.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = g_3x.png; path = ../app/assets/img/g_3x.png; sourceTree = "<group>"; };
		DD385D899A654967836AB959 /* img_8g_3x.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = img_8g_3x.png; path = ../app/assets/img/img_8g_3x.png; sourceTree = "<group>"; };
		E06A5A785C1446578B0DDC8A /* lujing_4686_3x_1_.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = lujing_4686_3x_1_.png; path = ../app/assets/img/lujing_4686_3x_1_.png; sourceTree = "<group>"; };
		E2C1D2213D3D42DF905DBA40 /* lujing_4807_3x.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = lujing_4807_3x.png; path = ../app/assets/img/lujing_4807_3x.png; sourceTree = "<group>"; };
		E4666E173C394930B1DE69B6 /* battery_yellow_charge.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = battery_yellow_charge.png; path = ../app/assets/img/battery_yellow_charge.png; sourceTree = "<group>"; };
		E60792D660574C3C904884D3 /* mask_group_3x_1_.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = mask_group_3x_1_.png; path = ../app/assets/img/mask_group_3x_1_.png; sourceTree = "<group>"; };
		E6A1065579114EE0AE65951E /* group_534_3x.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = group_534_3x.png; path = ../app/assets/img/group_534_3x.png; sourceTree = "<group>"; };
		E84A96EB831A409E9925F30A /* lujing_4684_3x.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = lujing_4684_3x.png; path = ../app/assets/img/lujing_4684_3x.png; sourceTree = "<group>"; };
		EA39669BB04A4EDA885F8500 /* group_244_3x.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = group_244_3x.png; path = ../app/assets/img/group_244_3x.png; sourceTree = "<group>"; };
		EBC5C07283124A7187849C14 /* modal_bg_2.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = modal_bg_2.png; path = ../app/assets/img/modal_bg_2.png; sourceTree = "<group>"; };
		ECCE3CE1345047F0A4CB7871 /* group_4_3x.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = group_4_3x.png; path = ../app/assets/img/group_4_3x.png; sourceTree = "<group>"; };
		ED297162215061F000B7C4FE /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = System/Library/Frameworks/JavaScriptCore.framework; sourceTree = SDKROOT; };
		ED8E68C337A04D8DA1C1BCD0 /* voice_playing_icon_1_right.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = voice_playing_icon_1_right.png; path = ../app/assets/img/voice_playing_icon_1_right.png; sourceTree = "<group>"; };
		EFE5B558D2BB46F9B1A560D8 /* voice_playing_icon_3_left.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = voice_playing_icon_3_left.png; path = ../app/assets/img/voice_playing_icon_3_left.png; sourceTree = "<group>"; };
		F282D355FF9244FEA1080A9B /* lujing_4686_3x.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = lujing_4686_3x.png; path = ../app/assets/img/lujing_4686_3x.png; sourceTree = "<group>"; };
		F327CCE6E72F4C38B6E7F0C1 /* voice_playing_icon_2_right.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = voice_playing_icon_2_right.png; path = ../app/assets/img/voice_playing_icon_2_right.png; sourceTree = "<group>"; };
		F3B5D60B04E242419638E2E8 /* group_227_3x_1_.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = group_227_3x_1_.png; path = ../app/assets/img/group_227_3x_1_.png; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		13B07F8C1A680F5B00A75B9A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0C80B921A6F3F58F76C31292 /* libPods-doll_controller_app_rn.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		13B07FAE1A68108700A75B9A /* doll_controller_app_rn */ = {
			isa = PBXGroup;
			children = (
				13B07FB51A68108700A75B9A /* Images.xcassets */,
				761780EC2CA45674006654EE /* AppDelegate.swift */,
				13B07FB61A68108700A75B9A /* Info.plist */,
				81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */,
				13B07FB81A68108700A75B9A /* PrivacyInfo.xcprivacy */,
			);
			name = doll_controller_app_rn;
			sourceTree = "<group>";
		};
		2D16E6871FA4F8E400B85C8A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				ED297162215061F000B7C4FE /* JavaScriptCore.framework */,
				5DCACB8F33CDC322A6C60F78 /* libPods-doll_controller_app_rn.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		832341AE1AAA6A7D00B99B32 /* Libraries */ = {
			isa = PBXGroup;
			children = (
			);
			name = Libraries;
			sourceTree = "<group>";
		};
		83CBB9F61A601CBA00E9B192 = {
			isa = PBXGroup;
			children = (
				13B07FAE1A68108700A75B9A /* doll_controller_app_rn */,
				832341AE1AAA6A7D00B99B32 /* Libraries */,
				83CBBA001A601CBA00E9B192 /* Products */,
				2D16E6871FA4F8E400B85C8A /* Frameworks */,
				BBD78D7AC51CEA395F1C20DB /* Pods */,
				95A4D2DFABFD4E89954F0576 /* Resources */,
			);
			indentWidth = 2;
			sourceTree = "<group>";
			tabWidth = 2;
			usesTabs = 0;
		};
		83CBBA001A601CBA00E9B192 /* Products */ = {
			isa = PBXGroup;
			children = (
				13B07F961A680F5B00A75B9A /* doll_controller_app_rn.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		95A4D2DFABFD4E89954F0576 /* Resources */ = {
			isa = PBXGroup;
			children = (
				DAED5CE7A2B14EC783113441 /* g_3x.png */,
				0C19CFEAC37C4AEEB4CC4975 /* group_237_3x.png */,
				EA39669BB04A4EDA885F8500 /* group_244_3x.png */,
				526C43188F5A426BAA946248 /* group_257_3x.png */,
				36EAD7C370A44702B706FBFA /* group_258_3x.png */,
				0FE55713072E48678186FEE8 /* group_339_3x.png */,
				A6CFFD8CD5854403BA8E60AB /* group_342_3x.png */,
				ECCE3CE1345047F0A4CB7871 /* group_4_3x.png */,
				E6A1065579114EE0AE65951E /* group_534_3x.png */,
				B8A94037646048E88322012D /* img_15_1_3x.png */,
				DD385D899A654967836AB959 /* img_8g_3x.png */,
				1315ACBB34A046C5B96761B4 /* lujing_4645_3x.png */,
				463446C10268438AB5D17446 /* lujing_4660_3x.png */,
				713E69D8E8984FB688DCC89B /* lujing_4683_3x.png */,
				E84A96EB831A409E9925F30A /* lujing_4684_3x.png */,
				F282D355FF9244FEA1080A9B /* lujing_4686_3x.png */,
				6E4A62B6A73E45F2B52F0756 /* lujing_4762_3x.png */,
				E2C1D2213D3D42DF905DBA40 /* lujing_4807_3x.png */,
				3A31B6EAD1094403A1A77775 /* lujing_4812_3x.png */,
				0A55E1B79B8C4EB3B9C5FCB1 /* lujing_4849_3x.png */,
				37F50524D1A44C259FCC7310 /* lujing_4895_3x.png */,
				B3F48AFD74FC45DAB1BCC2EA /* mask_group_3x.png */,
				271D61E3E64941B59D833091 /* star_6_3x.png */,
				AD3FFF9E9A00422F9E59B71C /* tuceng_2_3x.png */,
				513F378B33014603ADF35037 /* union_3x.png */,
				9F06298A447A46CD8B757480 /* vector_3x.png */,
				C435A4A4DC72432AB3F59549 /* zu_976_3x.png */,
				55FA2790979044C1B1F02D4F /* zu_984_3x.png */,
				D02EB1895FAB47DCBAADC7FB /* zu_987_3x.png */,
				F3B5D60B04E242419638E2E8 /* group_227_3x_1_.png */,
				E06A5A785C1446578B0DDC8A /* lujing_4686_3x_1_.png */,
				8025F74352DE44B6BEC9783C /* lujing_4895_3x_2_.png */,
				996B5DA6F58F4ADF8D740B78 /* mask_group_3x_10_.png */,
				208878EF972742119EE11A65 /* mask_group_3x_11_.png */,
				038E6FA2BE5B417E84745570 /* mask_group_3x_12_.png */,
				D38C34BD09AA4EC9A743C2DD /* mask_group_3x_15_.png */,
				E60792D660574C3C904884D3 /* mask_group_3x_1_.png */,
				8D400478FD614EA38077C681 /* mask_group_3x_2_.png */,
				A35A0C1ABF0D48A19077119D /* mask_group_3x_4_.png */,
				A0291AA8F4834234A4BB8134 /* mask_group_3x_5_.png */,
				4F2240CC8F314B339C8AF828 /* mask_group_3x_6_.png */,
				8312F6B43D584AA6B8B56E10 /* mask_group_3x_7_.png */,
				787887C031964A7F8DDCF51B /* group_535_3x.png */,
				B678675FA7894D4ABBF6161F /* group_536_3x.png */,
				44CA415A8D7A410EA9E8023D /* group_537_3x.png */,
				0D9F46A27864424D8126459B /* group_538_3x.png */,
				843464D187994E71A80E362E /* group_539_3x.png */,
				7EC132F5D1844414B98D2974 /* group_46_3x.png */,
				2D5A565D33AB4276BDFBD2B9 /* group_47_3x.png */,
				7EC912AD312841F59C75174E /* avatar_blue.png */,
				6CA9A7651DFD4120A226A598 /* avatar_pink.png */,
				116B932EB7174ABBB8C5BA28 /* avatar_purple.png */,
				A1AD807CFEBD44A484E9A3D7 /* avatar_rabbit.png */,
				1FD4897150594688A04471E4 /* avatar_yellow.png */,
				51899771B1E54F14B9967123 /* chat_bg_blue.png */,
				93FB3F74134A4554BB9FA71B /* chat_bg_pink.png */,
				B27756FA35FC4B96B2097297 /* chat_bg_purple.png */,
				9212D9E02B094A368700E22E /* chat_bg_rabbit.png */,
				05E9BAF799CD46A1A97ABEB6 /* chat_bg_yellow.png */,
				773309FA8CAB41D7B03C2459 /* doll_list_item_bg.png */,
				12A4845902AA4C61B28EC787 /* summary_board_blue.png */,
				BF00E49FB8104A8194F1D2D4 /* summary_board_pink.png */,
				7296F63001B34988A73DD60A /* summary_board_purple.png */,
				1CA5FB03E484433DA743A0F2 /* summary_board_rabbit.png */,
				C2BB900DC758468B99E30241 /* summary_board_yellow.png */,
				1E4B02FB9F074CECAA9645F3 /* summary_bg_content.png */,
				3029552AF5D94D52A1A15D68 /* summary_bg_header.png */,
				0978EEB79F474684A7CCD3CA /* voice_btn_icon_0.png */,
				AE96CD6B970A45468A5F238A /* voice_btn_icon_1.png */,
				88791EF7A2CE4AA28A065F3D /* voice_btn_icon_2.png */,
				05FDF9D97E5C441F9A76BD27 /* voice_playing_icon_0.png */,
				2237AD7011BE48E4A8C73629 /* voice_playing_icon_1_left.png */,
				ED8E68C337A04D8DA1C1BCD0 /* voice_playing_icon_1_right.png */,
				19C1F0762F5840C996F953DB /* voice_playing_icon_2_left.png */,
				F327CCE6E72F4C38B6E7F0C1 /* voice_playing_icon_2_right.png */,
				EFE5B558D2BB46F9B1A560D8 /* voice_playing_icon_3_left.png */,
				A7D1143C5C10437C8C0483BE /* voice_playing_icon_3_right.png */,
				7DC45B28526640429C88CB3A /* bg_chat.png */,
				2D13D57776644737AB132262 /* bg_doll.png */,
				B1DC62D1F0D24B09A5556BB6 /* bg_login.png */,
				370A53EDD04B46D8BF9E8856 /* bg_profile.png */,
				72EA85B253FE49018D2BC5E1 /* bg_splash.png */,
				5D23A81825994FB7B34EE686 /* group_260_3x.png */,
				3725DBD950324B2481CEAF01 /* modal_bg_1.png */,
				EBC5C07283124A7187849C14 /* modal_bg_2.png */,
				A3BE732CAD4445E3BC287837 /* modal_bg_3.png */,
				1514E54524204595B1D9DAE0 /* battery_full.png */,
				2020CAC3497B4D8EBACC52FE /* battery_full_charge.png */,
				8B366FDF994542E3B97C8A02 /* battery_green.png */,
				33F94190C837421E9D80F417 /* battery_green_charge.png */,
				B6D18A1B60E4452091210BAA /* battery_red.png */,
				14A82696C40F44AA95220DC0 /* battery_red_charge.png */,
				533A54F0D51F4BCCB5D8DD1E /* battery_yellow.png */,
				E4666E173C394930B1DE69B6 /* battery_yellow_charge.png */,
				BC8DC1E41E6B453C856E7449 /* btn_profile.png */,
				5FBA274542754E368E11DF19 /* AlibabaPuHuiTi-3-55-Regular.ttf */,
				591FAD5385C04111A9B2B14C /* circle_avatar_blue_b.png */,
				6247579271FE41DBA86435F7 /* circle_avatar_blue_p.png */,
				1B0FE9D3244E4CC09235D70E /* circle_avatar_pink_b.png */,
				6DE8C216AE4F445399DD21DE /* circle_avatar_pink_p.png */,
				3ADC2489831E41EAAB2E3CEE /* circle_avatar_purple_b.png */,
				155D65A694C74BF08D27780F /* circle_avatar_purple_p.png */,
				C48102F96B874795B1E51461 /* circle_avatar_rabbit_b.png */,
				80FE2DAA11FD4E4884ABBC47 /* circle_avatar_rabbit_p.png */,
				A11DD136841F451685B24605 /* circle_avatar_yellow_b.png */,
				238F0BC7ADA444148C7F1C65 /* circle_avatar_yellow_p.png */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		BBD78D7AC51CEA395F1C20DB /* Pods */ = {
			isa = PBXGroup;
			children = (
				3B4392A12AC88292D35C810B /* Pods-doll_controller_app_rn.debug.xcconfig */,
				5709B34CF0A7D63546082F79 /* Pods-doll_controller_app_rn.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		13B07F861A680F5B00A75B9A /* doll_controller_app_rn */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "doll_controller_app_rn" */;
			buildPhases = (
				C38B50BA6285516D6DCD4F65 /* [CP] Check Pods Manifest.lock */,
				13B07F871A680F5B00A75B9A /* Sources */,
				13B07F8C1A680F5B00A75B9A /* Frameworks */,
				13B07F8E1A680F5B00A75B9A /* Resources */,
				00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */,
				00EEFC60759A1932668264C0 /* [CP] Embed Pods Frameworks */,
				E235C05ADACE081382539298 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = doll_controller_app_rn;
			productName = doll_controller_app_rn;
			productReference = 13B07F961A680F5B00A75B9A /* doll_controller_app_rn.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		83CBB9F71A601CBA00E9B192 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 1210;
				TargetAttributes = {
					13B07F861A680F5B00A75B9A = {
						LastSwiftMigration = 1120;
					};
				};
			};
			buildConfigurationList = 83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "doll_controller_app_rn" */;
			compatibilityVersion = "Xcode 12.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 83CBB9F61A601CBA00E9B192;
			productRefGroup = 83CBBA001A601CBA00E9B192 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				13B07F861A680F5B00A75B9A /* doll_controller_app_rn */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		13B07F8E1A680F5B00A75B9A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				81AB9BB82411601600AC10FF /* LaunchScreen.storyboard in Resources */,
				13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */,
				987E4B6266E6E4C2A1AECDC2 /* PrivacyInfo.xcprivacy in Resources */,
				EC425049C9834C44ABCE1E4E /* g_3x.png in Resources */,
				DE1D8DD5A8EE48FCBA59EB84 /* group_237_3x.png in Resources */,
				A6907758FB6C45E79FC2A78F /* group_244_3x.png in Resources */,
				3268E18328C6454CB0B37706 /* group_257_3x.png in Resources */,
				2E457700007949F8BF1E3508 /* group_258_3x.png in Resources */,
				51CC6F34F94B4E19ABF38666 /* group_339_3x.png in Resources */,
				179A2E3A42674A98AFB35116 /* group_342_3x.png in Resources */,
				C47E443DB37B4351910F910F /* group_4_3x.png in Resources */,
				8AD8766D5DF94F9B9A82FC16 /* group_534_3x.png in Resources */,
				F8BC8DF91913499886D583AB /* img_15_1_3x.png in Resources */,
				41B3105870974A07BC1B95C8 /* img_8g_3x.png in Resources */,
				2B97BAAC034A474AB422B5C7 /* lujing_4645_3x.png in Resources */,
				1887688E04D146DF8463AF34 /* lujing_4660_3x.png in Resources */,
				93D990AD0DE5442C99280766 /* lujing_4683_3x.png in Resources */,
				34DFD3D2D6354D95B030DCAD /* lujing_4684_3x.png in Resources */,
				90F31D47058F4A95A4AF558B /* lujing_4686_3x.png in Resources */,
				22895DAA8BA14A28AD569760 /* lujing_4762_3x.png in Resources */,
				4094DBD97B0B4D3495B8B005 /* lujing_4807_3x.png in Resources */,
				341678032F7547E587D899D2 /* lujing_4812_3x.png in Resources */,
				A9405414D9A941409A42A6E8 /* lujing_4849_3x.png in Resources */,
				3BF17B19AF904BFA8C78B28B /* lujing_4895_3x.png in Resources */,
				21FA913B9F5047F4B747AEE7 /* mask_group_3x.png in Resources */,
				1EF409662F414D34B6903E66 /* star_6_3x.png in Resources */,
				E2D76A43A6EF4BAA9A8B7F7C /* tuceng_2_3x.png in Resources */,
				07AD6D69A9D34A90AAA1A2F8 /* union_3x.png in Resources */,
				57EDF6FD02C2430E8EA53D48 /* vector_3x.png in Resources */,
				7DAAF95BFC734D67925011F2 /* zu_976_3x.png in Resources */,
				5BEA763829AF4E8294EE74AE /* zu_984_3x.png in Resources */,
				6078A2E28C674F1E89C16331 /* zu_987_3x.png in Resources */,
				1B81C6B4B13B41E985CFD3C6 /* group_227_3x_1_.png in Resources */,
				97A5DA5392DA4A62BBD33E5C /* lujing_4686_3x_1_.png in Resources */,
				5B8EAA5287384EAAA7B41E19 /* lujing_4895_3x_2_.png in Resources */,
				8CE4CBD31B9E46259BDF3C02 /* mask_group_3x_10_.png in Resources */,
				ED150DC2EA5E4052A815C958 /* mask_group_3x_11_.png in Resources */,
				4869F84F8C8340F58BE32F59 /* mask_group_3x_12_.png in Resources */,
				10B957E1B67848EEB947E283 /* mask_group_3x_15_.png in Resources */,
				B25FDB4F317C4DDD9368CB69 /* mask_group_3x_1_.png in Resources */,
				AC4C8971591F47538A697F7E /* mask_group_3x_2_.png in Resources */,
				48F344BA22CD4738AC4ECAFA /* mask_group_3x_4_.png in Resources */,
				82B3604F9B3E47A19E5E2411 /* mask_group_3x_5_.png in Resources */,
				8D9FD04E148140C6AE97508A /* mask_group_3x_6_.png in Resources */,
				A4D8C97728F24B8A80CCAEFD /* mask_group_3x_7_.png in Resources */,
				10B2937563BF4202B3FDFF5C /* group_535_3x.png in Resources */,
				B5878A4A7A724A619A82E8DD /* group_536_3x.png in Resources */,
				0D2DBC30F37A4334B26B5C47 /* group_537_3x.png in Resources */,
				083750089B13470BB22DC829 /* group_538_3x.png in Resources */,
				92C76D9F9FEC4ECDAB24952A /* group_539_3x.png in Resources */,
				0C0C4C0D85C04797AC1BA7EA /* group_46_3x.png in Resources */,
				5B989DAB0567426F9AF16551 /* group_47_3x.png in Resources */,
				EE332D927B284787A00B303D /* avatar_blue.png in Resources */,
				9359733A75874D2D8626992E /* avatar_pink.png in Resources */,
				C0CEA80488D648429651C8C1 /* avatar_purple.png in Resources */,
				44E422B3955445DD81F79DD8 /* avatar_rabbit.png in Resources */,
				C1E17DB6FAA2453D95B4319A /* avatar_yellow.png in Resources */,
				E3B2DC6B07304D0891A4B3BC /* chat_bg_blue.png in Resources */,
				F84DF5C9F74C4796A95C063F /* chat_bg_pink.png in Resources */,
				004D3D094CE241FCA6EA2F7C /* chat_bg_purple.png in Resources */,
				379F8CAD9DBF47CC89E50CDD /* chat_bg_rabbit.png in Resources */,
				C22E7B8FCF9F43BD87F8D689 /* chat_bg_yellow.png in Resources */,
				FA08DFA435F940299E98AE45 /* doll_list_item_bg.png in Resources */,
				233B799751DB4131842117CB /* summary_board_blue.png in Resources */,
				D23E0183493F471FA06F4748 /* summary_board_pink.png in Resources */,
				AC51413347CF4AFBAB3FDC4C /* summary_board_purple.png in Resources */,
				71B36194CB484D30BDCEA857 /* summary_board_rabbit.png in Resources */,
				7457521FF77445B789DE02C0 /* summary_board_yellow.png in Resources */,
				2D77BCA507AF46F186FF113D /* summary_bg_content.png in Resources */,
				77CD0CE793A94674AA8922AB /* summary_bg_header.png in Resources */,
				29A2F242B26048B689B7368A /* voice_btn_icon_0.png in Resources */,
				90211203C5BA4061A500A380 /* voice_btn_icon_1.png in Resources */,
				C18E02881D534A31A6768663 /* voice_btn_icon_2.png in Resources */,
				F46DD90DE533434B835E9EA3 /* voice_playing_icon_0.png in Resources */,
				28073AD5A12B4AFD8AD16E40 /* voice_playing_icon_1_left.png in Resources */,
				36CC0FCF76AD4441BFAE1EEE /* voice_playing_icon_1_right.png in Resources */,
				B6DC56DA46D341608E539BFB /* voice_playing_icon_2_left.png in Resources */,
				9E9DCAC476F048F6B0F55409 /* voice_playing_icon_2_right.png in Resources */,
				3D07D4D24F9A4FD58C861429 /* voice_playing_icon_3_left.png in Resources */,
				65C3930980DE475084CE2D53 /* voice_playing_icon_3_right.png in Resources */,
				8C239481932C4A5C81F6423E /* bg_chat.png in Resources */,
				6079EDACB7F04FD4B4463189 /* bg_doll.png in Resources */,
				DB42A99A4E0D4F2292464D4D /* bg_login.png in Resources */,
				C7D6EC93AB344923AD513C8A /* bg_profile.png in Resources */,
				E44341E7652A4E8483E3841A /* bg_splash.png in Resources */,
				3C6CF1A223574A8795052491 /* group_260_3x.png in Resources */,
				FB222EDE664E41E8A7F78E33 /* modal_bg_1.png in Resources */,
				1063A25DED3540F49CBDFFC2 /* modal_bg_2.png in Resources */,
				AA1EFAECBD164EF0AA9C207F /* modal_bg_3.png in Resources */,
				605081C7B5074C9C9520B0A8 /* battery_full.png in Resources */,
				D381F872C25C4EC581B33401 /* battery_full_charge.png in Resources */,
				13E4A96869064DBC8B954564 /* battery_green.png in Resources */,
				D4AB782AF09844139ADEE801 /* battery_green_charge.png in Resources */,
				E0AC52E7FE4A461880798B02 /* battery_red.png in Resources */,
				98C042B6D8624F3EA2680C99 /* battery_red_charge.png in Resources */,
				92934E8A253A457C90C61F56 /* battery_yellow.png in Resources */,
				28381B0A1A85470F9CE3CE76 /* battery_yellow_charge.png in Resources */,
				BE7FA4A8D39B41FEBE0BE0FB /* btn_profile.png in Resources */,
				4AF64873734745A28248F8B9 /* AlibabaPuHuiTi-3-55-Regular.ttf in Resources */,
				54483B5DE9CF4352AC38538C /* circle_avatar_blue_b.png in Resources */,
				F8ED10C2230247BDB9D42A34 /* circle_avatar_blue_p.png in Resources */,
				8477D890FFEB4BDE95676C2D /* circle_avatar_pink_b.png in Resources */,
				9770F85BF3014E929549BDD8 /* circle_avatar_pink_p.png in Resources */,
				0ABCADBD391442E386C5C30F /* circle_avatar_purple_b.png in Resources */,
				56A6D180B89F426EB06726B1 /* circle_avatar_purple_p.png in Resources */,
				DAFAC46D60BF40E3B94BB890 /* circle_avatar_rabbit_b.png in Resources */,
				6CF68EFD0D8C4609A3050609 /* circle_avatar_rabbit_p.png in Resources */,
				4169ACE5E16440DF86F8C926 /* circle_avatar_yellow_b.png in Resources */,
				9422EA0E17CC406B9C528863 /* circle_avatar_yellow_p.png in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"$(SRCROOT)/.xcode.env.local",
				"$(SRCROOT)/.xcode.env",
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\n\nWITH_ENVIRONMENT=\"$REACT_NATIVE_PATH/scripts/xcode/with-environment.sh\"\nREACT_NATIVE_XCODE=\"$REACT_NATIVE_PATH/scripts/react-native-xcode.sh\"\n\n/bin/sh -c \"$WITH_ENVIRONMENT $REACT_NATIVE_XCODE\"\n";
		};
		00EEFC60759A1932668264C0 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-doll_controller_app_rn/Pods-doll_controller_app_rn-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-doll_controller_app_rn/Pods-doll_controller_app_rn-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-doll_controller_app_rn/Pods-doll_controller_app_rn-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		C38B50BA6285516D6DCD4F65 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-doll_controller_app_rn-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		E235C05ADACE081382539298 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-doll_controller_app_rn/Pods-doll_controller_app_rn-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-doll_controller_app_rn/Pods-doll_controller_app_rn-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-doll_controller_app_rn/Pods-doll_controller_app_rn-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		13B07F871A680F5B00A75B9A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				761780ED2CA45674006654EE /* AppDelegate.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		13B07F941A680F5B00A75B9A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 3B4392A12AC88292D35C810B /* Pods-doll_controller_app_rn.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CURRENT_PROJECT_VERSION = 1;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = doll_controller_app_rn/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = doll_controller_app_rn;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		13B07F951A680F5B00A75B9A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5709B34CF0A7D63546082F79 /* Pods-doll_controller_app_rn.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CURRENT_PROJECT_VERSION = 1;
				INFOPLIST_FILE = doll_controller_app_rn/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = doll_controller_app_rn;
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		83CBBA201A601CBA00E9B192 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
					"-DFOLLY_HAVE_CLOCK_GETTIME=1",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					" ",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) DEBUG";
				USE_HERMES = true;
			};
			name = Debug;
		};
		83CBBA211A601CBA00E9B192 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
					"-DFOLLY_HAVE_CLOCK_GETTIME=1",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					" ",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				USE_HERMES = true;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "doll_controller_app_rn" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				13B07F941A680F5B00A75B9A /* Debug */,
				13B07F951A680F5B00A75B9A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "doll_controller_app_rn" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				83CBBA201A601CBA00E9B192 /* Debug */,
				83CBBA211A601CBA00E9B192 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 83CBB9F71A601CBA00E9B192 /* Project object */;
}
