<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>doll_controller_app_rn</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<false/>
		<key>NSAllowsLocalNetworking</key>
		<true/>
	</dict>
	<key>NSAppleMusicUsageDescription</key>
	<string>需要访问媒体库以便选择或保存音频文件</string>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>我们需要使用蓝牙功能来发现并连接到附近的设备</string>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>我们需要使用蓝牙功能来发现并连接到附近的设备</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>需要访问您的位置以便连接到WiFi网络和发现附近的蓝牙设备</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>需要访问您的位置以便连接到WiFi网络和发现附近的蓝牙设备</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>需要访问您的位置以便连接到WiFi网络和发现附近的蓝牙设备</string>
	<key>NSMediaLibraryUsageDescription</key>
	<string>需要访问媒体库以便选择或保存音频文件</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>需要您的同意，我们才能使用麦克风进行录音</string>
	<key>UIAppFonts</key>
	<array>
		<string>AntDesign.ttf</string>
		<string>AlibabaPuHuiTi-3-55-Regular.ttf</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>arm64</string>
	</array>
	<key>UIStatusBarStyle</key>
	<string>UIStatusBarStyleLightContent</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>UIAppFonts</key>
	<array>
		<string>AntDesign.ttf</string>
		<string>AlibabaPuHuiTi-3-55-Regular.ttf</string>
	</array>
</dict>
</plist>
