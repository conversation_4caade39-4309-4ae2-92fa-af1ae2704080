/**
 * Sample React Native App
 * https://github.com/facebook/react-native
 *
 * @format
 */

import React, {useEffect} from 'react';
import {SafeAreaProvider, SafeAreaView} from 'react-native-safe-area-context';
import RootNavigator from './app/navigation/RootNavigator';
import JotaiProvider from './app/providers/JotaiProvider';
import Logger from './app/services/LoggerService';
import {Settings, StatusBar} from 'react-native';
import UpdatePrompt from './app/components/UpdatePrompt';
import KeepAwake from '@sayem314/react-native-keep-awake';

import {enableScreens} from 'react-native-screens';
enableScreens(); // 启用原生屏幕优化

function App(): React.JSX.Element {
  useEffect(() => {
    // 应用启动时记录日志
    Logger.info('应用启动');
    Logger.debug('调试信息: React Native版本', {version: '0.79.2'});

    // 捕获未处理的JS异常
    const errorHandler = (error: Error) => {
      Logger.error('未捕获的JS异常', error);
    };

    // 添加全局错误处理器
    const RNErrorUtils = global as any;
    if (RNErrorUtils.ErrorUtils) {
      RNErrorUtils.ErrorUtils.setGlobalHandler(errorHandler);
    }

    return () => {
      // 应用关闭时记录日志
      Logger.info('应用关闭');
    };
  }, []);

  return (
    <JotaiProvider>
      <SafeAreaProvider>
        <StatusBar hidden translucent backgroundColor="transparent" />
        <RootNavigator />
      </SafeAreaProvider>
      <KeepAwake />
      <UpdatePrompt
        checkOnMount={false}
        autoCheck={false}
        checkInterval={3600000}></UpdatePrompt>
    </JotaiProvider>
  );
}

export default App;
