#!/usr/bin/env node

const path = require('path');
const {spawn} = require('child_process');

// 定义当前目录
const mockDir = path.resolve(__dirname);

console.log('启动Mock HTTP服务器...');
console.log('工作目录:', mockDir);

// 在mock目录中执行npm start命令
const npmProcess = spawn('npm', ['start'], {
  cwd: mockDir,
  stdio: 'inherit',
  shell: true,
});

// 处理退出事件
npmProcess.on('close', code => {
  console.log(`Mock HTTP服务器已退出，退出码：${code}`);
});

// 处理SIGINT信号（Ctrl+C）
process.on('SIGINT', () => {
  console.log('接收到中断信号，正在关闭Mock HTTP服务器...');
  npmProcess.kill('SIGINT');
});
