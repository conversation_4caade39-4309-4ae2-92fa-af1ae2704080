import express, {
  Request,
  Response,
  NextFunction,
  RequestHandler,
} from 'express';
import cors from 'cors';
import bodyParser from 'body-parser';
import {
  BaseResponse,
  VerifyCodeRequest,
  VerifyCodeResponse,
  VerifyCodeAuthRequest,
  TokenResponse,
  UserInfo,
  DollBindRequest,
  DollInfosResponse,
  DollStateRequest,
  DollStateResponse,
  RoleRequest,
  RolesResponse,
  RoleChangeRequest,
  RoleDeleteRequest,
  RoleDeleteResponse,
  RoleGenRequest,
  RoleGenResponse,
  RoleUpsertRequest,
  RoleUpsertResponse,
  DollMsgRequest,
  ParentMsgRequest,
  MsgResponse,
  MsgSummaryRequest,
  UrlResponse,
  Timbre,
  TimbreListResponse,
  TimbreDeleteRequest,
  TimbreDeleteResponse,
  TimbreRecordRequest,
  TimbreRecordResponse,
  ParentChatRequest,
  ChatMessage,
} from './types';
import {
  dollInfos,
  dollRoles,
  dollStates,
  dollMessages,
  parentMessages,
  chatSummaries,
  verifyCodes,
  userTokens,
  userInfos,
  generateVerifyCode,
  generateToken,
  generateExpiredTime,
  timbres,
  settings,
  getPagedMessages,
} from '../data';

// 创建Express应用
const app = express();
const PORT = 3001;

// 中间件
app.use(cors());
app.use(bodyParser.json());

// 请求响应日志中间件
const loggingMiddleware: RequestHandler = (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  const start = Date.now();

  // 记录请求信息
  console.log('\n=== Request ===');
  console.log(`${new Date().toISOString()} ${req.method} ${req.path}`);
  console.log('Headers:', req.headers);
  console.log('Body:', JSON.stringify(req.body));

  // 保存原始的 res.json 方法
  const originalJson = res.json;

  // 重写 res.json 方法
  res.json = function (body) {
    // 记录响应信息
    console.log('\n=== Response ===');
    console.log(`Status: ${res.statusCode}`);
    console.log('Body:', JSON.stringify(body));
    console.log(`Time: ${Date.now() - start}ms`);
    console.log('================\n');

    // 调用原始的 json 方法
    return originalJson.call(this, body);
  };

  next();
};

// 应用日志中间件
app.use(loggingMiddleware);

// 响应结构生成器
const createResponse = <T>(
  data: T,
  code = 0,
  message = 'success',
): BaseResponse<T> => {
  return {code, message, data};
};

// 认证中间件
const authMiddleware: RequestHandler = (
  req: Request,
  res: Response,
  next: NextFunction,
): void => {
  // 登录相关接口不需要认证
  if (req.path.includes('/login/')) {
    next();
    return;
  }

  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    res.status(401).json(createResponse({}, 401, '未授权访问'));
    return;
  }

  const token = authHeader.split(' ')[1];
  if (!userTokens[token]) {
    res.status(401).json(createResponse({}, 401, '无效的token'));
    return;
  }

  next();
};

// 应用认证中间件
app.use(authMiddleware);

// 1. 获取验证码
app.post('/app/login/get-verifycode', ((
  req: Request<{}, {}, VerifyCodeRequest>,
  res: Response,
) => {
  const {area, phone} = req.body;
  const verifycode = generateVerifyCode();

  // 保存验证码
  const key = `${area}-${phone}`;
  verifyCodes[key] = verifycode;

  const data: VerifyCodeResponse = {verifycode};
  res.json(createResponse(data));
}) as RequestHandler);

// 2. 验证码认证
app.post('/app/login/verify-verifycode', ((
  req: Request<{}, {}, VerifyCodeAuthRequest>,
  res: Response,
) => {
  const {area, phone, verifycode} = req.body;
  const key = `${area}-${phone}`;

  // 验证验证码
  if (verifyCodes[key] !== verifycode) {
    return res.json(createResponse({} as TokenResponse, 1, '验证码错误'));
  }

  // 生成token
  const token = generateToken();
  const expiredTime = generateExpiredTime();

  // 保存用户信息
  userTokens[token] = `${area}-${phone}`;
  userInfos[token] = {area, phone, token, expiredTime};

  // 删除已使用的验证码
  delete verifyCodes[key];

  const data: TokenResponse = {token, expiredTime};
  res.json(createResponse(data));
}) as RequestHandler);

// 3. 刷新token
app.post('/app/refresh-token', ((req: Request, res: Response) => {
  const authHeader = req.headers.authorization;
  const oldToken = authHeader?.split(' ')[1];

  if (!oldToken || !userInfos[oldToken]) {
    return res.json(createResponse({} as TokenResponse, 1, 'token已过期'));
  }

  // 获取用户信息
  const userInfo = userInfos[oldToken];

  // 生成新token
  const newToken = generateToken();
  const expiredTime = generateExpiredTime();

  // 更新token
  userTokens[newToken] = userTokens[oldToken];
  delete userTokens[oldToken];

  // 更新用户信息
  userInfos[newToken] = {...userInfo, token: newToken, expiredTime};
  delete userInfos[oldToken];

  const data: TokenResponse = {token: newToken, expiredTime};
  res.json(createResponse(data));
}) as RequestHandler);

// 4. 绑定小玩偶
app.post('/app/doll/bind', ((
  req: Request<{}, {}, DollBindRequest>,
  res: Response,
) => {
  const {dollId} = req.body;

  // 检查是否存在该玩偶
  const doll = dollInfos.find(d => d.dollId === dollId);
  if (!doll) {
    return res.json(createResponse({}, 1, '玩偶不存在'));
  }

  res.json(createResponse({}));
}) as RequestHandler);

// 5. 获取小玩偶设备详情列表
app.post('/app/doll/infos', ((req: Request, res: Response) => {
  res.json(createResponse({dollInfos}));
}) as RequestHandler);

// 6. 获取玩偶硬件状态
app.post('/app/doll/get-state', ((
  req: Request<{}, {}, DollStateRequest>,
  res: Response,
) => {
  const {dollId} = req.body;

  // 检查是否存在该玩偶
  if (!dollStates[dollId]) {
    return res.json(createResponse({} as DollStateResponse, 1, '玩偶不存在'));
  }

  res.json(createResponse(dollStates[dollId]));
}) as RequestHandler);

// 7. 获取小玩偶设备聊天内容
app.post('/app/msg/get-doll', ((
  req: Request<{}, {}, DollMsgRequest>,
  res: Response,
) => {
  const {dollId, limit, timestamp, op} = req.body;

  // 检查是否存在该玩偶的消息
  if (!dollMessages[dollId]) {
    return res.json(createResponse({totalSize: 0, msgs: []} as MsgResponse));
  }

  const response = getPagedMessages(
    dollMessages[dollId],
    timestamp || Date.now(),
    limit,
    op || 'before',
  );
  res.json(createResponse(response));
}) as RequestHandler);

// 8. 获取家长聊天信息
app.post('/app/msg/get-parent', ((
  req: Request<{}, {}, ParentMsgRequest>,
  res: Response,
) => {
  const {limit, timestamp} = req.body;
  const response = getPagedMessages(
    parentMessages,
    timestamp || Date.now(),
    limit,
    'before',
  );
  res.json(createResponse(response));
}) as RequestHandler);

// 9. 获取聊天内容总结
app.post('/app/msg/get-summary', ((
  req: Request<{}, {}, MsgSummaryRequest>,
  res: Response,
) => {
  const {dollId} = req.body;

  // 检查是否存在该玩偶的总结
  const summary = chatSummaries[dollId] || {
    mainContent: [],
    emotion: '',
    AIAddiction: [],
    negativeContent: [],
    statistics: '',
  };

  res.json(createResponse(summary));
}) as RequestHandler);

// 10. 获取小玩偶角色
app.post('/app/role/get-role', ((
  req: Request<{}, {}, RoleRequest>,
  res: Response,
) => {
  const {dollId} = req.body;

  // 检查是否存在该玩偶的角色
  if (!dollRoles[dollId]) {
    return res.json(createResponse({roles: []}));
  }

  res.json(createResponse({roles: dollRoles[dollId]}));
}) as RequestHandler);

// 11. 角色切换
app.post('/app/role/change', ((
  req: Request<{}, {}, RoleChangeRequest>,
  res: Response,
) => {
  const {dollId, newRoleId} = req.body;

  // 检查是否存在该玩偶
  const dollIndex = dollInfos.findIndex(d => d.dollId === dollId);
  if (dollIndex === -1) {
    return res.json(createResponse({}, 1, '玩偶不存在'));
  }

  // 检查是否存在该角色
  const roleExists = dollRoles[dollId]?.some(r => r.roleId === newRoleId);
  if (!roleExists) {
    return res.json(createResponse({}, 1, '角色不存在'));
  }

  // 更新角色
  dollInfos[dollIndex].usedRoleId = newRoleId;

  res.json(createResponse({}));
}) as RequestHandler);

// 12. 角色删除
app.post('/app/role/delete', ((
  req: Request<{}, {}, RoleDeleteRequest>,
  res: Response,
) => {
  const {dollId, roleId} = req.body;

  // 检查是否存在该玩偶
  const dollIndex = dollInfos.findIndex(d => d.dollId === dollId);
  if (dollIndex === -1) {
    return res.json(createResponse({usedRoleId: ''}, 1, '玩偶不存在'));
  }

  // 检查是否存在该角色
  const roleIndex = dollRoles[dollId]?.findIndex(r => r.roleId === roleId);
  if (roleIndex === -1 || roleIndex === undefined) {
    return res.json(createResponse({usedRoleId: ''}, 1, '角色不存在'));
  }

  // 删除角色
  dollRoles[dollId].splice(roleIndex, 1);

  // 如果删除的是正在使用的角色，则切换到默认角色
  if (dollInfos[dollIndex].usedRoleId === roleId) {
    dollInfos[dollIndex].usedRoleId = dollInfos[dollIndex].originalRoleId;
    return res.json(
      createResponse({usedRoleId: dollInfos[dollIndex].originalRoleId}),
    );
  }

  res.json(createResponse({usedRoleId: dollInfos[dollIndex].usedRoleId}));
}) as RequestHandler);

// 13. 自定义角色一键生成
app.post('/app/role/gen', ((
  req: Request<{}, {}, RoleGenRequest>,
  res: Response,
) => {
  const {dollId} = req.body;

  // 检查是否存在该玩偶
  const doll = dollInfos.find(d => d.dollId === dollId);
  if (!doll) {
    return res.json(createResponse({} as RoleGenResponse, 1, '玩偶不存在'));
  }

  res.json(
    createResponse({
      roleContent: `我是一个友善的${doll.dollName}，喜欢和小朋友们玩耍和交流。`,
      timbreId: 't_1',
      timbreName: '可爱女声',
    }),
  );
}) as RequestHandler);

// 14. 自定义角色创建/修改
app.post('/app/role/upsert', ((
  req: Request<{}, {}, RoleUpsertRequest>,
  res: Response,
) => {
  const {dollId, roleId, roleContent, timbreId} = req.body;

  // 检查是否存在该玩偶
  if (!dollRoles[dollId]) {
    return res.json(createResponse({roleId: ''}, 1, '玩偶不存在'));
  }

  // 获取音色名称
  const timbre = timbres.find(t => t.timbreId === timbreId);
  if (!timbre) {
    return res.json(createResponse({roleId: ''}, 1, '音色不存在'));
  }

  let newRoleId = roleId;

  // 如果roleId为空，创建新角色
  if (!roleId) {
    newRoleId = `r_custom_${Date.now()}`;
    dollRoles[dollId].push({
      roleId: newRoleId,
      roleContent,
      timbreId,
      timbreName: timbre.timbreName,
    });
  } else {
    // 修改现有角色
    const roleIndex = dollRoles[dollId].findIndex(r => r.roleId === roleId);
    if (roleIndex === -1) {
      return res.json(createResponse({roleId: ''}, 1, '角色不存在'));
    }

    dollRoles[dollId][roleIndex] = {
      roleId,
      roleContent,
      timbreId,
      timbreName: timbre.timbreName,
    };
  }

  res.json(createResponse({roleId: newRoleId}));
}) as RequestHandler);

// 15. 家长助手文字聊天（SSE实现）
app.get('/app/msg/chat-parent-sse', ((
  req: Request<{}, {}, ParentChatRequest>,
  res: Response,
) => {
  // const {queryType, query}: ParentChatRequest = req.query;
  const queryType = req.query.queryType as string;
  const query = req.query.query as string;
  console.log('sse queryType', queryType);
  console.log('query', query);

  console.info('httpServer', 'chat-parent-sse method called');
  // 设置SSE响应头
  // res.setHeader('Content-Type', 'text/event-stream');``
  // res.setHeader('Cache-Control', 'no-cache');
  // res.setHeader('Connection', 'keep-alive');

  res.writeHead(200, {
    'Content-Type': 'text/event-stream',
    'Cache-Control': 'no-cache',
    Connection: 'keep-alive',
  });
  res.flushHeaders();
  res.write(':\n\n');

  // 模拟流式响应
  let answer = '';
  const responses = [
    '正在思考...',
    '让我为您分析一下...',
    '根据孩子的情况，',
    '我的建议是...',
    '希望这些建议对您有帮助。',
  ];

  const id = Date.now();
  const timestamp = Date.now();
  let currentIndex = 0;
  const interval = setInterval(() => {
    if (currentIndex < responses.length) {
      answer += responses[currentIndex];

      const msg = {
        id,
        query,
        answer,
        shield: '',
        timestamp,
        isDone: currentIndex === responses.length - 1,
      };

      res.write(`data: ${JSON.stringify(createResponse(msg))}\n\n`);

      currentIndex++;
    } else {
      console.log('sse流式响应结束');
      clearInterval(interval);
      res.write('retry: 0\n');
      res.end();
    }
  }, 1000);

  // 客户端断开连接时清理
  req.on('close', () => {
    console.log('sse客户端断开连接');
    clearInterval(interval);
  });
}) as RequestHandler);

// 16. 获得订阅url
app.post('/app/setting/subscribe', ((req: Request, res: Response) => {
  res.json(createResponse({url: settings.subscribeUrl}));
}) as RequestHandler);

// 17. 获得知识库url
app.post('/app/setting/repository', ((req: Request, res: Response) => {
  res.json(createResponse({url: settings.repositoryUrl}));
}) as RequestHandler);

// 18. 退出登录
app.post('/app/setting/quit-account', ((req: Request, res: Response) => {
  const authHeader = req.headers.authorization;
  const token = authHeader?.split(' ')[1];

  if (token && userInfos[token]) {
    delete userTokens[token];
    delete userInfos[token];
  }

  res.json(createResponse({}));
}) as RequestHandler);

// 19. 注销登录
app.post('/app/setting/delete-account', ((req: Request, res: Response) => {
  const authHeader = req.headers.authorization;
  const token = authHeader?.split(' ')[1];

  if (token && userInfos[token]) {
    delete userTokens[token];
    delete userInfos[token];
  }

  res.json(createResponse({}));
}) as RequestHandler);

// 20. 音色列表
app.post('/app/timbre/list', ((req: Request, res: Response) => {
  res.json(
    createResponse({
      recordLimitNum: 5,
      list: timbres,
    }),
  );
}) as RequestHandler);

// 21. 删除音色
app.post('/app/timbre/delete', ((
  req: Request<{}, {}, TimbreDeleteRequest>,
  res: Response,
) => {
  const {timbreId} = req.body;

  // 只能删除自定义音色
  const timbreIndex = timbres.findIndex(
    t => t.timbreId === timbreId && t.tag === 'my',
  );
  if (timbreIndex === -1) {
    return res.json(createResponse({timbreId: ''}, 1, '音色不存在或无法删除'));
  }

  // 删除音色
  timbres.splice(timbreIndex, 1);

  res.json(createResponse({timbreId}));
}) as RequestHandler);

// 22. 音色录制
app.post('/app/timbre/record', ((
  req: Request<{}, {}, TimbreRecordRequest>,
  res: Response,
) => {
  const {voice} = req.body;

  // 检查是否达到录音上限
  const myTimbres = timbres.filter(t => t.tag === 'my');
  if (myTimbres.length >= 5) {
    return res.json(
      createResponse({} as TimbreRecordResponse, 1, '已达到录音上限'),
    );
  }

  // 创建新音色
  const newTimbreId = `t_custom_${Date.now()}`;
  const newTimbre: Timbre = {
    timbreId: newTimbreId,
    timbreName: `我的录音${myTimbres.length + 1}`,
    timbreSampleUrl: `https://example.com/samples/${newTimbreId}.mp3`,
    createTime: new Date().toISOString(),
    style: '自然',
    isCustom: true,
    tag: 'my',
  };

  // 添加到音色列表
  timbres.push(newTimbre);

  res.json(createResponse({newTimbre}));
}) as RequestHandler);

// 启动服务器
export const startServer = () => {
  app.listen(PORT, () => {
    console.log(`Mock HTTP服务器运行在 http://localhost:${PORT}`);
  });
};

export default app;
