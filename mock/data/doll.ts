import {DollInfo, DollRole, DollStateResponse} from '../server/types';

// 玩偶信息
export const dollInfos: DollInfo[] = [
  {
    dollId: 'd_1',
    dollType: 'rabbit',
    dollName: '小兔子玩偶',
    dollDesc: '陪伴智能机器人',
    battery: 80,
    volume: 60,
    isOnline: true,
    state: 'Idle',
    usedRoleId: 'r_1',
    originalRoleId: 'r_1',
    unreads: 2,
  },
  {
    dollId: 'd_2',
    dollType: 'blue',
    dollName: '小熊玩偶',
    dollDesc: '智能伴侣机器人',
    battery: 45,
    volume: 70,
    isOnline: false,
    state: 'Idle',
    usedRoleId: 'r_3',
    originalRoleId: 'r_3',
    unreads: 0,
  },
  {
    dollId: 'd_3',
    dollType: 'pink',
    dollName: '小猫玩偶',
    dollDesc: '智能互动玩偶',
    battery: 95,
    volume: 50,
    isOnline: true,
    state: 'PlayMusic',
    usedRoleId: 'r_2',
    originalRoleId: 'r_2',
    unreads: 1,
  },
];

// 玩偶角色信息
export const dollRoles: Record<string, DollRole[]> = {
  d_1: [
    {
      roleId: 'r_1',
      roleContent:
        '我是一个活泼可爱的小兔子，喜欢讲故事和唱歌。我总是很开心，希望能够陪伴孩子成长。',
      timbreId: 't_1',
      timbreName: '可爱女声',
    },
    {
      roleId: 'r_2',
      roleContent:
        '我是一个知识渊博的兔子教授，可以回答很多科学知识问题，帮助孩子学习新知识。',
      timbreId: 't_2',
      timbreName: '成熟男声',
    },
  ],
  d_2: [
    {
      roleId: 'r_3',
      roleContent:
        '我是一个温暖友善的小熊，喜欢给孩子讲睡前故事，让孩子安心入睡。',
      timbreId: 't_3',
      timbreName: '温柔女声',
    },
  ],
  d_3: [
    {
      roleId: 'r_2',
      roleContent:
        '我是一个活泼好动的小猫，喜欢玩游戏和捉迷藏，能够给孩子带来欢乐。',
      timbreId: 't_4',
      timbreName: '活泼童声',
    },
  ],
};

// 玩偶状态
export const dollStates: Record<string, DollStateResponse> = {
  d_1: {
    battery: 80,
    volume: 60,
    isOnline: true,
    state: 'Idle',
    unreads: 2,
  },
  d_2: {
    battery: 45,
    volume: 70,
    isOnline: false,
    state: 'Idle',
    unreads: 0,
  },
  d_3: {
    battery: 95,
    volume: 50,
    isOnline: true,
    state: 'PlayMusic',
    unreads: 1,
  },
};

// 已绑定的玩偶
export const boundDolls: Set<string> = new Set(['d_1', 'd_2', 'd_3']);

// 生成新角色
export const generateRole = (dollId: string): DollRole => {
  const roleId = `r_${Date.now()}`;
  const timbreId = `t_${Math.floor(Math.random() * 4) + 1}`;
  const timbreName = ['可爱女声', '成熟男声', '温柔女声', '活泼童声'][
    Math.floor(Math.random() * 4)
  ];

  return {
    roleId,
    roleContent: `我是${dollInfos.find(d => d.dollId === dollId)?.dollName || '玩偶'}的新角色，我会陪伴你成长。`,
    timbreId,
    timbreName,
  };
};
