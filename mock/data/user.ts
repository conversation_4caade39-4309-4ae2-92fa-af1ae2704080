import {UserInfo} from '../server/types';

// 验证码缓存
export const verifyCodes: Record<string, string> = {};

// 保存用户生成的token和信息
export const userTokens: Record<string, string> = {};
export const userInfos: Record<string, UserInfo> = {};

// 生成随机验证码
export const generateVerifyCode = (): string => {
  return Math.floor(1000 + Math.random() * 9000).toString();
};

// 生成随机token
export const generateToken = (): string => {
  return (
    Math.random().toString(36).substring(2, 15) +
    Math.random().toString(36).substring(2, 15)
  );
};

// 生成过期时间（7天）
export const generateExpiredTime = (): number => {
  return Math.floor(Date.now() / 1000) + 604800; // 当前时间 + 7天（秒）
};

// 刷新token
export const refreshToken = (
  oldToken: string,
): {token: string; expiredTime: number} => {
  const newToken = generateToken();
  const expiredTime = generateExpiredTime();

  // 更新token映射
  userTokens[newToken] = userTokens[oldToken];
  delete userTokens[oldToken];

  return {
    token: newToken,
    expiredTime,
  };
};
