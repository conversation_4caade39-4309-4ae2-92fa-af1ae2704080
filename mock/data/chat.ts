import {ChatMessage, MsgSummaryResponse} from '../server/types';

// 玩偶聊天消息
export const dollMessages: Record<string, ChatMessage[]> = {
  d_1: [
    {
      id: 1,
      query: '你好，小兔子',
      answer: '你好呀！我是小兔子，很高兴认识你！',
      shield: '',
      timestamp: Date.now() - 3600000 * 2, // 2小时前
    },
    {
      id: 2,
      query: '你能给我讲个故事吗？',
      answer: '当然可以！从前有一只小兔子，它住在森林里，每天都很开心地玩耍...',
      shield: '',
      timestamp: Date.now() - 3600000 * 1.5, // 1.5小时前
    },
    {
      id: 3,
      query: '你知道奥特曼吗？',
      answer: '我当然知道！奥特曼是来自M78星云的超级英雄，保护地球和平。',
      shield: '',
      timestamp: Date.now() - 3600000, // 1小时前
    },
    {
      id: 4,
      query: '你叫什么名字？',
      answer: '我叫小兔子，是你的好朋友！',
      shield: '',
      timestamp: Date.now() - 1800000, // 30分钟前
    },
  ],
  d_2: [
    {
      id: 1,
      query: '小熊你好',
      answer: '你好呀！我是小熊，很高兴见到你！',
      shield: '',
      timestamp: Date.now() - 86400000, // 1天前
    },
    {
      id: 2,
      query: '今天天气怎么样？',
      answer: '今天天气很好，阳光明媚，非常适合出去玩耍哦！',
      shield: '',
      timestamp: Date.now() - 86400000 + 3600000, // 1天前 + 1小时
    },
  ],
  d_3: [
    {
      id: 1,
      query: '小猫，你会唱歌吗？',
      answer: '会呀！我最喜欢唱《小星星》了，要我唱给你听吗？',
      shield: '',
      timestamp: Date.now() - 3600000 * 5, // 5小时前
    },
  ],
};

// 家长助手聊天消息
export const parentMessages: ChatMessage[] = [
  {
    id: 1,
    query: '今天孩子说了什么？',
    answer: '今天孩子问了很多关于动物的问题，特别对兔子感兴趣。',
    shield: '',
    timestamp: Date.now() - 86400000 * 2, // 2天前
  },
  {
    id: 2,
    query: '孩子今天还好吗？',
    answer: '孩子今天情绪很稳定，玩得很开心，还学习了一些新单词。',
    shield: '',
    timestamp: Date.now() - 86400000, // 1天前
  },
  {
    id: 3,
    query: '孩子最近喜欢什么？',
    answer: '最近孩子对宇宙和星球很感兴趣，经常问关于太阳系的问题。',
    shield: '',
    timestamp: Date.now() - 3600000 * 10, // 10小时前
  },
  {
    id: 4,
    query: '有什么育儿建议？',
    answer: '建议多陪孩子一起探索大自然，这有助于培养观察力和好奇心。',
    shield: '',
    timestamp: Date.now() - 3600000 * 5, // 5小时前
  },
];

// 聊天内容总结
export const chatSummaries: Record<string, MsgSummaryResponse> = {
  d_1: {
    mainContent: [
      '孩子对动物和故事很感兴趣',
      '喜欢听关于小动物的故事',
      '最近开始对奥特曼等超级英雄产生兴趣',
    ],
    emotion: '积极',
    AIAddiction: ['喜欢和玩偶互动', '经常询问问题'],
    negativeContent: [],
    statistics: '平均每天互动3次，每次约15分钟',
  },
  d_2: {
    mainContent: [
      '孩子善于观察周围环境',
      '会询问天气等日常事物',
      '表现出对周围世界的好奇心',
    ],
    emotion: '好奇',
    AIAddiction: ['喜欢询问天气', '经常分享日常'],
    negativeContent: [],
    statistics: '平均每天互动2次，每次约10分钟',
  },
  d_3: {
    mainContent: [
      '孩子对音乐表现出浓厚兴趣',
      '喜欢听歌和唱歌',
      '有助于培养艺术感知能力',
    ],
    emotion: '愉悦',
    AIAddiction: ['喜欢听音乐', '经常唱歌'],
    negativeContent: [],
    statistics: '平均每天互动4次，每次约20分钟',
  },
};

// 获取分页消息
export const getPagedMessages = (
  messages: ChatMessage[],
  timestamp: number,
  limit: number,
  op: 'before' | 'after' = 'before',
): {totalSize: number; msgs: ChatMessage[]} => {
  // 按时间戳排序
  const sortedMessages = [...messages].sort((a, b) =>
    op === 'before' ? b.timestamp - a.timestamp : a.timestamp - b.timestamp,
  );

  let filteredMessages;
  if (timestamp === 0) {
    // 首次加载，返回最新的消息
    filteredMessages = sortedMessages.slice(0, limit);
  } else {
    // 根据op获取指定时间戳之前或之后的消息
    if (op === 'before') {
      filteredMessages = sortedMessages
        .filter(msg => msg.timestamp < timestamp)
        .slice(0, limit);
    } else {
      filteredMessages = sortedMessages
        .filter(msg => msg.timestamp > timestamp)
        .slice(0, limit);
    }
  }

  return {
    totalSize: messages.length,
    msgs: filteredMessages,
  };
};
