import {Timbre} from '../server/types';

// 音色数据
export const timbres: Timbre[] = [
  {
    timbreId: 't_1',
    timbreName: '可爱女声',
    timbreSampleUrl: 'https://example.com/samples/t_1.mp3',
    createTime: new Date().toISOString(),
    style: '温柔',
    isCustom: false,
    tag: 'reco',
  },
  {
    timbreId: 't_2',
    timbreName: '成熟男声',
    timbreSampleUrl: 'https://example.com/samples/t_2.mp3',
    createTime: new Date(Date.now() - 86400000).toISOString(), // 1天前
    style: '沉稳',
    isCustom: false,
    tag: 'reco',
  },
  {
    timbreId: 't_3',
    timbreName: '温柔女声',
    timbreSampleUrl: 'https://example.com/samples/t_3.mp3',
    createTime: new Date(Date.now() - 86400000 * 2).toISOString(), // 2天前
    style: '柔和',
    isCustom: false,
    tag: 'reco',
  },
  {
    timbreId: 't_4',
    timbreName: '活泼童声',
    timbreSampleUrl: 'https://example.com/samples/t_4.mp3',
    createTime: new Date(Date.now() - 86400000 * 3).toISOString(), // 3天前
    style: '欢快',
    isCustom: false,
    tag: 'reco',
  },
  {
    timbreId: 't_custom_1',
    timbreName: '我的录音1',
    timbreSampleUrl: 'https://example.com/samples/t_custom_1.mp3',
    createTime: new Date(Date.now() - 3600000).toISOString(), // 1小时前
    style: '个性',
    isCustom: true,
    tag: 'my',
  },
  {
    timbreId: 't_custom_2',
    timbreName: '我的录音2',
    timbreSampleUrl: 'https://example.com/samples/t_custom_2.mp3',
    createTime: new Date(Date.now() - 7200000).toISOString(), // 2小时前
    style: '自然',
    isCustom: true,
    tag: 'my',
  },
];
