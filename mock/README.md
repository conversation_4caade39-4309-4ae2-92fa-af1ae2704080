# Mock HTTP服务器

这是一个用于模拟后端API的HTTP服务器，为客户端提供模拟数据。该服务器作为独立的npm项目存在于主项目的mock目录中。

## 目录结构

```
mock/
  ├── data/             # Mock数据
  │   ├── chat.ts       # 聊天相关数据
  │   ├── doll.ts       # 玩偶相关数据
  │   ├── timbre.ts     # 音色相关数据
  │   ├── user.ts       # 用户相关数据
  │   ├── setting.ts    # 设置相关数据
  │   └── index.ts      # 数据导出
  ├── server/           # 服务器代码
  │   └── httpServer.ts # HTTP服务器实现
  ├── types.ts          # 类型定义
  ├── tsconfig.json     # TypeScript配置
  ├── package.json      # Mock服务器项目配置
  ├── index.ts          # 服务器入口
  ├── start.js          # 启动脚本
  └── README.md         # 说明文档
```

## 安装与启动服务器

### 首次使用时安装依赖

```bash
cd mock
npm install
```

### 启动服务器

从主项目根目录启动：

```bash
# 在主项目根目录下运行
yarn mock
# 或者
npm run mock
```

直接在mock目录中启动：

```bash
# 在mock目录下运行
npm start
# 或者使用nodemon自动重启（开发模式）
npm run dev
```

服务器默认在 http://localhost:3001 运行。

## API 列表

1. 获取验证码 `/app/login/get-verifycode`
2. 验证码认证 `/app/login/verify-verifycode`
3. 刷新token `/app/refresh-token`
4. 绑定小玩偶 `/app/doll/bind`
5. 获取小玩偶设备详情列表 `/app/doll/infos`
6. 获取玩偶硬件状态 `/app/doll/get-state`
7. 获取小玩偶设备聊天内容 `/app/msg/get-doll`
8. 获取家长聊天信息 `/app/msg/get-parent`
9. 获取聊天内容总结 `/app/msg/get-summary`
10. 获取小玩偶角色 `/app/role/get-role`
11. 角色切换 `/app/role/change`
12. 角色删除 `/app/role/delete`
13. 自定义角色一键生成 `/app/role/gen`
14. 自定义角色创建/修改 `/app/role/upsert`
15. 家长助手文字聊天 `/app/assistant/chat` (SSE)
16. 获得订阅url `/app/setting/subscribe`
17. 获得知识库url `/app/setting/repository`
18. 退出登录 `/app/setting/quit-account`
19. 注销登录 `/app/setting/delete-account`
20. 音色列表 `/app/timbre/list`
21. 删除音色 `/app/timbre/delete`
22. 音色录制 `/app/timbre/record`

所有API都使用POST方法，除了验证码相关的API外，其他API需要在请求头中携带认证信息：

```
Authorization: Bearer {token}
```

## 客户端配置

在客户端中，可以将API地址配置为mock服务器地址：

```typescript
// app/net/api/config.ts
export interface ApiConfig {
  baseURL: string;
  timeout: number;
}

// 生产API配置
const prodApiConfig: ApiConfig = {
  baseURL: 'https://api.example.com', // 请替换为实际的API地址
  timeout: 10000, // 超时时间，单位毫秒
};

// 开发API配置（本地Mock服务器）
const devApiConfig: ApiConfig = {
  baseURL: 'http://localhost:3001', // 本地Mock服务器地址
  timeout: 10000,
};

// 根据环境选择配置
const isDev = process.env.NODE_ENV !== 'production';
const apiConfig: ApiConfig = isDev ? devApiConfig : prodApiConfig;

export default apiConfig;
```
