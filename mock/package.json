{"name": "mock-server", "version": "1.0.0", "description": "玩偶APP的Mock HTTP服务器", "main": "index.js", "scripts": {"start": "ts-node index.ts", "dev": "nodemon --exec ts-node index.ts"}, "keywords": ["mock", "http", "server", "api"], "author": "", "license": "ISC", "dependencies": {"body-parser": "^2.2.0", "cors": "^2.8.5", "express": "^5.1.0"}, "devDependencies": {"@types/body-parser": "^1.19.5", "@types/cors": "^2.8.17", "@types/express": "^5.0.1", "@types/node": "^22.15.17", "nodemon": "^3.1.0", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}