
获取验证码
app/login/get-verifycode

{
    "area":"+86",
    "phone":"18310001000"
}

{
    "code": 0,
    "message": "success",
    "data": {
        "verifycode":"1234"
    }
}
验证码认证
app/login/verify-verifycode

{
    "area":"+86",
    "phone":"18310001000",
    "verifycode":"1234"
}

{
    "code": 0,
    "message": "success",
    "data": {
        "token":"123",
        "expiredTime":604800
    }
}
注意：之后所有协议 header 中增加 Authorization: Bear {token}
刷新token
app/refresh-token
{}
{
    "code": 0,
    "message": "success",
    "data": {
        "token":"234",
        "expiredTime":604800
    }
}
绑定小玩偶
app/doll/bind
{
    "dollId":"d_1"
}
{
    "code": 0,
    "message": "success",
    "data": {}
}
获取小玩偶设备 详情列表
app/doll/infos
{}
{
    "code": 0,
    "message": "success",
    "data": 
    {
        "dollInfos":[
            {
                "dollId":"d_1",
                "dollType":"rabbit", // rabbit 兔子
                "dollName":"小兔子玩偶", // 玩偶名字
                "dollDesc":"陪伴智能机器人", // 玩偶描述
                "battery"：10,     // 电量
                "volume": 20,     // 音量
                "isOnline": 0,      // 是否在线，0 离线，1 在线
                "state":"PlayMusic", //  PlayMusic, TellStory, Idle
                "usedRoleId":"r_2",  // 正在使用的角色
                "originalRoleId":"r_1" // 默认角色
            }
        ]
    }
}
存储聊天内容
获取小玩偶设备聊天内容
app/msg/get-doll
{
    "dollId":"1",
    "pageNum":1,
    "pageSize":20
}
{
    "code": 0,
    "message": "success",
    "data": 
    {
        "totalSize":129,
        "msgs":[
            {
                "query":"你知道奥特曼么",
                "answer":"我当然知道，我可是无所不能的"
                "timestamp":1746602868 // unix timestamp
            },
            {
                "query":"你知道奥特曼么!",
                "answer":"我当然知道，我可是无所不能的!"
                "timestamp":1746602878 // unix timestamp
            }
        ]
    }
}
获取家长聊天信息
app/msg/get-parent
{
    "pageNum":1,
    "pageSize":20
}
{
    "code": 0,
    "message": "success",
    "data": {
        "totalSize":129,
        "msgs":[
            {
                "query":"今天孩子说了什么",
                "answer":"今天孩子不开心",
                "timestamp":1746602868 // unix timestamp
            },
            {
                "query":"孩子今天还好么",
                "answer":"今天孩子不开心",
                "timestamp":1746602878 // unix timestamp
            }
        ]
    }
}
获取聊天内容总结
app/msg/get-summary
{
    "dollId":"d_1"
}
{
    "code": 0,
    "message": "success",
    "data": {
        "summary":""
    }
}
获取小玩偶角色
app/role/get-role
{
    "dollId":"d_1",
}
{
    "code": 0,
    "message": "success",
    "data": {
        "roles":[
        {
            "roleId":"r_1",
            "roleContent":"",
            "timbreId": "",
            "timbreName": "", // 音色名称
        }
    ]
    }
}
角色切换
app/role/change
{
    "dollId":"d_1",
    "newRoleId":"r_2"
}
{
    "code": 0,
    "message": "success",
    "data": {}
}
角色删除
app/role/delete
如果删除了正在使用的，就给一个默认的。如果删除的是没有正在使用的，返回的就是现在正在使用的
{
    "dollId":"d_1",
    "roleId":"r_2"
}
{
    "code": 0,
    "message": "success",
    "data": {
        "usedRoleId":"r_1"
    }
}
自定义角色 一键生成
app/role/gen
{
    "dollId":"d_1",
}
{
    "code": 0,
    "message": "success",
    "data": {
        "roleContent":"",
        "timbreId": "",
        "timbreName": "", // 音色名称
    }
}
自定义角色创建、修改
只能创建一个，如果修改就返回原始 rollId，如果创建就给一个新的 rollId
app/role/upsert
{
    "dollId":"d_1",
    "roleId":"", // 传空
    "roleContent":"",
    "timbreId": "",
}
{
    "code": 0,
    "message": "success",
    "data": {
        "roleId":"r_3"
    }
}
玩偶硬件状态（音量，电量，是否在线）(在需要的界面每 10 秒获取）
app/doll/get-state
{
    "dollId":"d_1"
}
{
    "code": 0,
    "message": "success",
    "data": {
        "battery":10,   
        "volume":20, 
        "isOnline":0,
        "state":"PlayMusic"
    }
}
家长助手文字聊天(chat, http sse)
app/assistant/chat
{
    "queryType":"text", // text, voice
    "query":"",
}
{
    "code": 0,
    "message": "success",
    "data": {
        "answer":"",
        "done":true
    }
}
获得订阅url
app/setting/subscribe
{}
{
    "code": 0,
    "message": "success",
    "data": {
        "url":"http://"
    }
}
获得知识库url
app/setting/repository
{}
{
    "code": 0,
    "message": "success",
    "data": {
        "url":"http://"
    }
}
 退出登录
app/setting/quit-account
删除 token
{}
{
    "code": 0,
    "message": "success",
    "data": {}
}
注销登录
app/setting/delete-account
{}
{
    "code": 0,
    "message": "success",
    "data": {}
}
音色列表
app/timbre/list
{
}
{
    "code": 0,
    "message": "success",
    "data": {
        "recordLimitNum":3,
        "list":[
            {
                "timbreId": "",
                "timbreName":"",
                "createTime":"", // 创建时间
                "style":"", // 风格
                "tag":"my" // my, reco
            }
        ]
    }
}
删除音色
app/timbre/delete
{
    "timbreId":"",
}
{
    "code": 0,
    "message": "success",
    "data": {
        "timbreId":""
    }
}
音色录制
app/timbre/record
{
    "voice": "", // 一段语音数据base64,最长20s
}
{
    "code": 0,
    "message": "success",
    "data": {
        "newTimbre": {
            "timbreId": "",
            "timbreName":"",
            "createTime":"", // 
            "style":"", 
            "tag":"my" // my, reco
        }
    }
}

