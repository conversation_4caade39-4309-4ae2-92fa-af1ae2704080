# JL协议修复说明

## 问题分析

通过对比 `docs/BLE配网说明.md` 和当前实现 `app/services/DollWiFiService.ts`，发现了以下协议差异：

### 原实现问题

1. **缺少JL协议头**：直接发送JSON字符串，没有"JL"头部标识
2. **缺少命令字节**：没有一级命令(0x10)和二级命令(0x01)
3. **缺少CRC16校验**：没有数据完整性校验
4. **缺少结束符**：没有0xFF结束标识
5. **JSON字段错误**：使用`pass`字段而非文档要求的`pwd`字段
6. **缺少分包发送**：没有考虑BLE特征值长度限制

## 修复内容

### 1. 协议格式修正

按照文档规范实现完整的JL协议格式：

```
| 字节 | 含义         | 示例值/说明                |
|------|--------------|---------------------------|
| 0-1  | 固定头 "JL"  | 0x4A, 0x4C                |
| 2-3  | 长度         | 高字节,低字节（数据长度）  |
| 4    | 一级命令     | 0x10                      |
| 5    | 二级命令     | 0x01                      |
| 6~N  | JSON数据     | {"ssid":"xxx","pwd":"yyy"} |
| N+1  | CRC16高字节  |                           |
| N+2  | CRC16低字节  |                           |
| N+3  | 0xFF         | 结束符                    |
```

### 2. 新增方法

#### `buildJLPacket(jsonData: Uint8Array): Uint8Array`

- 构建符合JL协议规范的数据包
- 包含完整的头部、长度、命令、数据、CRC16和结束符

#### `calculateCRC16(data: Uint8Array): number`

- 实现MODBUS标准的CRC16校验算法
- 确保数据传输完整性

#### `parseJLResponse(data: Uint8Array): string | null`

- 解析JL协议格式的响应数据
- 验证头部、长度、CRC16和结束符

#### `isCompleteJLResponse(data: Uint8Array): boolean`

- 检查接收到的数据是否为完整的JL协议响应

### 3. 分包发送支持

修改 `sendData` 方法，支持BLE特征值长度限制：

- 单包最大20字节（BLE标准MTU 23字节减去3字节协议头）
- 自动分包发送长数据
- 分包间隔50ms避免发送过快

### 4. JSON字段修正

- 将WiFi密码字段从 `pass` 改为 `pwd`
- 符合硬件端解析要求

### 5. 响应解析优化

- 优先解析JL协议格式响应
- 兼容普通文本响应
- 支持JSON和文本两种响应格式

## 测试验证

创建了 `scripts/testJLProtocol.js` 测试脚本，验证了：

1. ✅ WiFi配置数据包组包/解包
2. ✅ 状态查询数据包组包/解包
3. ✅ 中文SSID支持
4. ✅ 长密码支持
5. ✅ CRC16校验正确性

## 兼容性

- 保持向后兼容，支持解析非JL协议格式的响应
- Hook层接口无变化，不影响上层调用
- 添加了详细的日志记录便于调试

## 文件变更

### 修改文件

- `app/services/DollWiFiService.ts` - 主要协议实现修复

### 新增文件

- `scripts/testJLProtocol.js` - 协议测试脚本
- `docs/JL协议修复说明.md` - 本文档

## 使用示例

```typescript
// 原来的调用方式保持不变
const config = {
  ssid: 'MyWiFi',
  password: 'mypassword',
};

await dollWiFiService.configureWiFi(config);
```

现在会自动按照JL协议格式发送：

```
JL + 长度 + 0x10 + 0x01 + {"ssid":"MyWiFi","pwd":"mypassword"} + CRC16 + 0xFF
```

## 注意事项

1. 确保硬件端支持JL协议解析
2. 如果设备端CRC算法有差异，可能需要调整
3. 分包发送间隔可根据实际情况调整
4. 建议在实际设备上测试验证协议兼容性
