83 个结果 - 19 文件

app/components/AddDollButtonComponent.tsx:
  26            <ScalableImage
  27:             source={require('../assets/img/group_260_3x.png')}
  28              width={safeWnd.width * 0.4}

app/components/AppBackgroudImg.tsx:
  4  const bgs = {
  5:   bg_splash: require('../assets/img/bg_splash.png'),
  6:   bg_login: require('../assets/img/bg_login.png'),
  7:   bg_doll: require('../assets/img/bg_doll.png'),
  8:   bg_profile: require('../assets/img/bg_profile.png'),
  9:   bg_chat: require('../assets/img/bg_chat.png'),
  10  };

app/components/BatteryIconText.tsx:
  14    full: {
  15:     icon: require('../assets/img/battery_full.png'),
  16:     iconCharge: require('../assets/img/battery_full_charge.png'),
  17      num: 90,

  20    green: {
  21:     icon: require('../assets/img/battery_green.png'),
  22:     iconCharge: require('../assets/img/battery_green_charge.png'),
  23      num: 60,

  26    yellow: {
  27:     icon: require('../assets/img/battery_yellow.png'),
  28:     iconCharge: require('../assets/img/battery_yellow_charge.png'),
  29      num: 30,

  32    red: {
  33:     icon: require('../assets/img/battery_red.png'),
  34:     iconCharge: require('../assets/img/battery_red_charge.png'),
  35      num: 0,

app/components/Dialog.tsx:
  51                <AutoFitImage
  52:                 source={require('../assets/img/mask_group_3x_12_.png')}
  53                />

app/components/DollDiscoveryModal.tsx:
  245              <AutoFitImage
  246:               source={require('../assets/img/modal_bg_3.png')}
  247                // style={styles.backgroundImage}

app/components/InputSwitchComponent.tsx:
  14  // 资源
  15: const iconSend = require('../assets/img/mask_group_3x_6_.png');
  16  

app/components/KnowledgeLinkModal.tsx:
  50                <AutoFitImage
  51:                 source={require('../assets/img/group_342_3x.png')}
  52                />

app/components/RecordBottomSheet.tsx:
  177          <ImageBackground
  178:           source={require('../assets/img/mask_group_3x_11_.png')}
  179            style={styles.content}>

app/screens/AddDollScreen.tsx:
  182                    step.completed
  183:                     ? require('../assets/img/lujing_4684_3x.png')
  184:                     : require('../assets/img/lujing_4686_3x_1_.png')
  185                  }

app/screens/AllDollsScreen.tsx:
  110                <ScalableImage
  111:                 source={require('../assets/img/btn_profile.png')}
  112                  width={24}

app/screens/ContentSummaryScreen.tsx:
  18  const titleImgs = [
  19:   require('../assets/img/group_535_3x.png'),
  20:   require('../assets/img/group_536_3x.png'),
  21:   require('../assets/img/group_537_3x.png'),
  22:   require('../assets/img/group_538_3x.png'),
  23:   require('../assets/img/group_539_3x.png'),
  24  ];
  25  
  26: const bgHeaderImg = require('../assets/img/summary_bg_header.png');
  27: const bgContentImg = require('../assets/img/summary_bg_content.png');
  28  

app/screens/DollConnectWifiScreen.tsx:
  132        case 'strong':
  133:         return require('../assets/img/group_258_3x.png');
  134        case 'medium':
  135:         return require('../assets/img/group_257_3x.png');
  136        case 'weak':
  137:         return require('../assets/img/group_257_3x.png');
  138        default:
  139:         return require('../assets/img/group_257_3x.png');
  140      }

  219                <Image
  220:                 source={require('../assets/img/tuceng_2_3x.png')}
  221                  style={styles.routerImage}

app/screens/LoginRegisterScreen.tsx:
  139          <ScalableImage
  140:           source={require('../assets/img/group_260_3x.png')}
  141            width={width * 0.4}

app/screens/ProfileScreen.tsx:
  33      title: '订购',
  34:     icon: require('../assets/img/mask_group_3x.png'),
  35      action: 'showOrderLink',

  39      title: '设置',
  40:     icon: require('../assets/img/lujing_4812_3x.png'),
  41      screen: 'Settings',

  45      title: '常见问题',
  46:     icon: require('../assets/img/mask_group_3x_1_.png'),
  47    },

  50      title: '使用说明',
  51:     icon: require('../assets/img/mask_group_3x_2_.png'),
  52    },

app/screens/RoleSwitchScreen.tsx:
  130    // const getAvatarSource = () => {
  131:   //   return require('../assets/img/group_260_3x.png');
  132    // };

app/screens/SettingsScreen.tsx:
  40      id: 'version',
  41:     icon: require('../assets/img/mask_group_3x_4_.png'),
  42      title: '当前版本',

  47      id: 'parentKnowledge',
  48:     icon: require('../assets/img/mask_group_3x_5_.png'),
  49      title: '家长个性化知识库导入',

app/screens/SplashScreen.tsx:
   99          <ScalableImage
  100:           source={require('../assets/img/group_260_3x.png')}
  101            width={width * 0.4}

app/services/dollService.ts:
   6  const dollBgMap: Record<DollType, ImageSourcePropType> = {
   7:   rabbit: require('../assets/img/doll_list_item_bg.png'),
   8:   blue: require('../assets/img/doll_list_item_bg.png'),
   9:   purple: require('../assets/img/doll_list_item_bg.png'),
  10:   pink: require('../assets/img/doll_list_item_bg.png'),
  11:   yellow: require('../assets/img/doll_list_item_bg.png'),
  12  };
  13  const dollImgMap: Record<DollType, ImageSourcePropType> = {
  14:   rabbit: require('../assets/img/avatar_rabbit.png'),
  15:   blue: require('../assets/img/avatar_blue.png'),
  16:   purple: require('../assets/img/avatar_purple.png'),
  17:   pink: require('../assets/img/avatar_pink.png'),
  18:   yellow: require('../assets/img/avatar_yellow.png'),
  19  };
  20  const dollChatBoardMap: Record<DollType, ImageSourcePropType> = {
  21:   rabbit: require('../assets/img/chat_bg_rabbit.png'),
  22:   blue: require('../assets/img/chat_bg_blue.png'),
  23:   purple: require('../assets/img/chat_bg_purple.png'),
  24:   pink: require('../assets/img/chat_bg_pink.png'),
  25:   yellow: require('../assets/img/chat_bg_yellow.png'),
  26  };

  28  const dollSummaryBoardMap: Record<DollType, ImageSourcePropType> = {
  29:   rabbit: require('../assets/img/summary_board_rabbit.png'),
  30:   blue: require('../assets/img/summary_board_blue.png'),
  31:   purple: require('../assets/img/summary_board_purple.png'),
  32:   pink: require('../assets/img/summary_board_pink.png'),
  33:   yellow: require('../assets/img/summary_board_yellow.png'),
  34  };

  39    rabbit: {
  40:     p: require('../assets/img/circle_avatar_rabbit_p.png'),
  41:     b: require('../assets/img/circle_avatar_rabbit_b.png'),
  42    },
  43    blue: {
  44:     p: require('../assets/img/circle_avatar_blue_p.png'),
  45:     b: require('../assets/img/circle_avatar_blue_b.png'),
  46    },
  47    purple: {
  48:     p: require('../assets/img/circle_avatar_purple_p.png'),
  49:     b: require('../assets/img/circle_avatar_purple_b.png'),
  50    },
  51    pink: {
  52:     p: require('../assets/img/circle_avatar_pink_p.png'),
  53:     b: require('../assets/img/circle_avatar_pink_b.png'),
  54    },
  55    yellow: {
  56:     p: require('../assets/img/circle_avatar_yellow_p.png'),
  57:     b: require('../assets/img/circle_avatar_yellow_b.png'),
  58    },

app/services/timbreService.ts:
   5      return [
   6:       require('../assets/img/voice_btn_icon_2.png'),
   7:       require('../assets/img/voice_btn_icon_0.png'),
   8:       require('../assets/img/voice_btn_icon_1.png'),
   9      ];

  12      return {
  13:       first: require('../assets/img/voice_playing_icon_0.png'),
  14        left: [
  15:         require('../assets/img/voice_playing_icon_1_left.png'),
  16:         require('../assets/img/voice_playing_icon_2_left.png'),
  17:         require('../assets/img/voice_playing_icon_3_left.png'),
  18        ],
  19        right: [
  20:         require('../assets/img/voice_playing_icon_1_right.png'),
  21:         require('../assets/img/voice_playing_icon_2_right.png'),
  22:         require('../assets/img/voice_playing_icon_3_right.png'),
  23        ],
