本项目为客户端.
根据protocol,实现http模块完成请求.实现协议中所有接口.新建app/net目录,在net目录下实现. 
根据协议中的数据字段,定义实现: 用户信息、玩偶设备信息、玩偶角色信息、聊天记录等数据结构
实现用户登录状态管理及存储
除了获取验证码,和验证码认证这两个协议,其他协议需要 header 中增加 Authorization: Bear {token},其中 {token}为验证码验证协议返回的token
家长助手文字聊天(app/assistant/chat)协议需要HTTP SSE实现,请求后,需要持续接收服务器响应,直到服务器返回data中 done=true
封装易用的接口,定义好可配置的部分配置.
实现httpService,在httpService中暴露网络接口
重要: 严格按照描述协议定义实现,不要做额外的协议
重要: 严格阅读protocol文件的所有协议,实现每一个协议
