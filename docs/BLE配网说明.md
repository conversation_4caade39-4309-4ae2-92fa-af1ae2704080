# BLE 配网说明

## 一、整体流程

1. **硬件端（你的设备）**

   - 作为 BLE 外设（Peripheral）广播，等待 App 连接。
   - 建立 GATT 服务，定义好用于接收 WiFi 账号和密码的特征（如 ae81 handle）。
   - 接收到数据后，解析出 WiFi 账号和密码，进行联网。

2. **App 端**
   - 作为 BLE 主机（Central）扫描并连接你的设备。
   - 通过 GATT 写操作，将 WiFi 账号和密码写入指定特征（如 ae81）。
   - 等待设备回传配网结果（如 notify/indicate）。

---

## 二、协议细节（以你代码为例）

### 1. GATT 特征说明

- **写入特征**：`ae81`（handle 0x0006）
- **通知特征**：`ae82`（handle 0x0008）

### 2. 数据包格式（参考 `le_net_cfg_qyai.c`）

#### App 发送数据格式（配网请求）

通常格式如下（JL协议）：

| 字节 | 含义        | 示例值/说明                 |
| ---- | ----------- | --------------------------- |
| 0-1  | 固定头 "JL" | 0x4A, 0x4C                  |
| 2-3  | 长度        | 高字节,低字节（数据长度）   |
| 4    | 一级命令    | 0x10                        |
| 5    | 二级命令    | 0x01                        |
| 6~N  | JSON数据    | {"ssid":"xxx","pass":"yyy"} |
| N+1  | CRC16高字节 |                             |
| N+2  | CRC16低字节 |                             |
| N+3  | 0xFF        | 结束符                      |

CRC16-CCITT算法
**举例：**

```json
JL + 长度 + 0x10 + 0x01 + {"ssid":"yourssid","pwd":"yourpwd"} + CRC16 + 0xFF
```

#### App 端伪代码（Android/iOS）

```java
// 1. 组包
String json = "{\"ssid\":\"yourssid\",\"pwd\":\"yourpwd\"}";
byte[] jsonBytes = json.getBytes("utf-8");
int len = jsonBytes.length + 7; // 头+命令+json+crc+ff
ByteBuffer buf = ByteBuffer.allocate(len);
buf.put((byte)'J');
buf.put((byte)'L');
buf.putShort((short)jsonBytes.length);
buf.put((byte)0x10);
buf.put((byte)0x01);
buf.put(jsonBytes);
// 计算CRC16（从长度到json结尾），假设有crc16方法
short crc = crc16(buf.array(), 2, buf.position()-2);
buf.putShort(crc);
buf.put((byte)0xFF);

// 2. 通过BLE写入特征ae81
bleWrite(handle_ae81, buf.array());
```

> CRC16算法要和设备端一致，通常是标准的MODBUS/IBM等。

---

### 3. 硬件端解码

- 在 `le_net_cfg_qyai.c` 的 `att_write_callback` 里，已经有完整的解包逻辑：
  - 检查头部 "JL"
  - 解析长度、命令
  - 取出 JSON 数据
  - 校验 CRC16
  - 解析 JSON，提取 ssid 和 pwd
  - 调用 `bt_net_config_set_ssid_pwd` 进行联网

**核心代码片段：**

```c
if (buffer[0] == 'J' && buffer[1] == 'L' && buffer[4] == 0x10 && buffer[5] == 0x01) {
    user_data_size = (buffer[2] << 8) + buffer[3];
    memcpy(user_buf, buffer, buffer_size);
    check_net_info_if_recv_complete(); // 这里会解析json
}
```

---

## 三、App端开发建议

- **扫描设备**，连接后发现 GATT 服务，找到 `ae81` 特征。
- **分包写入**（如数据较长，需分多次写入，设备端有拼包逻辑）。
- **等待设备 notify/indicate**（`ae82`），获取配网结果。

---

## 四、硬件端开发建议

- 你只需保证 `le_net_cfg_qyai.c` 里的 `att_write_callback` 和 `check_net_info_if_recv_complete` 能正确解析 App 发送的数据即可。
- 若要兼容不同App或协议，可在 `att_write_callback` 里扩展协议判断和解包逻辑。

---

## 五、总结

- **App端**：组包（JL协议+json+crc16+ff），写入 `ae81` 特征。
- **硬件端**：`att_write_callback` 自动解包、校验、解析、联网。

如需具体App端代码（Android/iOS/Flutter等）或硬件端解包细节，请告知你的开发环境，我可以给出详细示例！
