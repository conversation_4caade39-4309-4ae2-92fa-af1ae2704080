import React, {useState, useEffect, useRef} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Modal,
  Animated,
  Easing,
} from 'react-native';
import {colors} from '../theme/theme';
import {timbreService} from '../services/timbreService';
import {Timbre} from '../net';
import {MyText} from '../custom/CustomFont';

// 音频波形组件
export const AudioWaveform: React.FC<{isRecording: boolean}> = ({
  isRecording,
}) => {
  const animationValues = useRef<Animated.Value[]>([]);
  const [bars] = useState<number[]>(Array(20).fill(0));

  // 初始化动画值
  useEffect(() => {
    animationValues.current = bars.map(() => new Animated.Value(0));
  }, [bars]);

  // 控制动画效果
  useEffect(() => {
    if (isRecording) {
      const animations = animationValues.current.map((value, index) => {
        // 随机波动高度
        const toValue = Math.random() * 0.8 + 0.2;
        // 随机动画持续时间
        const duration = Math.random() * 400 + 200;
        // 随机延迟
        const delay = index * 20;

        return Animated.sequence([
          Animated.timing(value, {
            toValue,
            duration,
            delay,
            easing: Easing.ease,
            useNativeDriver: false,
          }),
          Animated.timing(value, {
            toValue: Math.random() * 0.5 + 0.2,
            duration: duration * 0.8,
            easing: Easing.ease,
            useNativeDriver: false,
          }),
        ]);
      });

      const loop = Animated.loop(Animated.parallel(animations));

      loop.start();

      return () => loop.stop();
    } else {
      // 停止录音时，将所有条置为最小高度
      animationValues.current.forEach(value => {
        Animated.timing(value, {
          toValue: 0.1,
          duration: 300,
          useNativeDriver: false,
        }).start();
      });
    }
  }, [isRecording, bars]);

  return (
    <View style={styles.waveformContainer}>
      {bars.map((_, index) => (
        <Animated.View
          key={index}
          style={[
            styles.waveformBar,
            {
              height: animationValues.current[index]?.interpolate({
                inputRange: [0, 1],
                outputRange: [5, 40], // 最小5px, 最大40px
              }),
              backgroundColor: isRecording ? colors.primary : '#D0D0D0',
            },
          ]}
        />
      ))}
    </View>
  );
};

// 录音弹窗组件
export const RecordingModal: React.FC<{
  visible: boolean;
  text: string;
  isRecording: boolean;
  recordingProgress: number;
  onStart: () => void;
  onStop: () => void;
  onClose: () => void;
}> = ({
  visible,
  text,
  isRecording,
  recordingProgress,
  onStart,
  onStop,
  onClose,
}) => {
  const [recordingTime, setRecordingTime] = useState(0);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // 管理录音计时器
  useEffect(() => {
    if (isRecording) {
      timerRef.current = setInterval(() => {
        setRecordingTime(prev => prev + 1);
      }, 1000);
    } else {
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    }

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [isRecording]);

  // 当弹窗关闭时重置录音时间
  useEffect(() => {
    if (!visible) {
      setRecordingTime(0);
    }
  }, [visible]);

  // 格式化录音时间
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}>
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <MyText style={styles.modalTitle}>录制音色</MyText>
          <MyText style={styles.modalSubtitle}>请朗读下面的文字：</MyText>

          <View style={styles.textContainer}>
            <MyText style={styles.recordingText}>{text}</MyText>
          </View>

          <View style={styles.waveformWrapper}>
            <AudioWaveform isRecording={isRecording} />
            <MyText style={styles.timeText}>{formatTime(recordingTime)}</MyText>
          </View>

          <View style={styles.recordButtonContainer}>
            {isRecording ? (
              <TouchableOpacity
                style={[styles.recordButton, styles.stopButton]}
                onPress={onStop}>
                <MyText style={styles.recordButtonText}>完成录制</MyText>
              </TouchableOpacity>
            ) : (
              <TouchableOpacity style={styles.recordButton} onPress={onStart}>
                <MyText style={styles.recordButtonText}>按住录制</MyText>
              </TouchableOpacity>
            )}
          </View>

          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <MyText style={styles.closeButtonText}>取消</MyText>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

// 音色项组件
export const TimbreItem: React.FC<{
  timbre: Timbre;
  onSelect: (timbre: Timbre) => void;
  onDelete?: (timbre: Timbre) => void;
  onPlay: (timbre: Timbre) => void;
  isSelected: boolean;
}> = ({timbre, onSelect, onDelete, onPlay, isSelected}) => {
  return (
    <View style={[styles.timbreItem, isSelected && styles.selectedTimbreItem]}>
      <TouchableOpacity
        style={styles.timbreInfo}
        onPress={() => onSelect(timbre)}>
        <View style={styles.timbreDetails}>
          <MyText style={styles.timbreName}>{timbre.timbreName}</MyText>
          <MyText style={styles.timbreDescription}>{timbre.style}</MyText>
        </View>
      </TouchableOpacity>

      <View style={styles.timbreActions}>
        <TouchableOpacity
          style={styles.playButton}
          onPress={() => onPlay(timbre)}>
          <MyText style={styles.actionButtonText}>▶</MyText>
        </TouchableOpacity>

        {timbre.isCustom && onDelete && (
          <TouchableOpacity
            style={styles.deleteButton}
            onPress={() => onDelete(timbre)}>
            <MyText style={styles.deleteButtonText}>×</MyText>
          </TouchableOpacity>
        )}

        {isSelected && (
          <View style={styles.checkmark}>
            <MyText style={styles.checkmarkText}>✓</MyText>
          </View>
        )}
      </View>
    </View>
  );
};

// 页面头部组件
export const HeaderComponent: React.FC<{
  title: string;
  onBack: () => void;
}> = ({title, onBack}) => {
  return (
    <View style={styles.header}>
      <TouchableOpacity onPress={onBack} style={styles.backButton}>
        <MyText style={styles.backButtonText}>←</MyText>
      </TouchableOpacity>
      <MyText style={styles.headerTitle}>{title}</MyText>
      <View style={styles.placeholder} />
    </View>
  );
};

// 标签页组件
export const TabComponent: React.FC<{
  activeTab: 'all' | 'record';
  onChangeTab: (tab: 'all' | 'record') => void;
}> = ({activeTab, onChangeTab}) => {
  return (
    <View style={styles.tabContainer}>
      <TouchableOpacity
        style={[styles.tabButton, activeTab === 'all' && styles.activeTab]}
        onPress={() => onChangeTab('all')}>
        <MyText
          style={[styles.tabText, activeTab === 'all' && styles.activeTabText]}>
          全部音色
        </MyText>
      </TouchableOpacity>
      <TouchableOpacity
        style={[styles.tabButton, activeTab === 'record' && styles.activeTab]}
        onPress={() => onChangeTab('record')}>
        <MyText
          style={[
            styles.tabText,
            activeTab === 'record' && styles.activeTabText,
          ]}>
          录制音色
        </MyText>
      </TouchableOpacity>
    </View>
  );
};

// 底部确认按钮组件
export const FooterComponent: React.FC<{
  onConfirm: () => void;
  disabled: boolean;
}> = ({onConfirm, disabled}) => {
  return (
    <View style={styles.footer}>
      <TouchableOpacity
        style={styles.confirmButton}
        onPress={onConfirm}
        disabled={disabled}>
        <MyText style={styles.confirmButtonText}>确定</MyText>
      </TouchableOpacity>
    </View>
  );
};

// 加载中组件
export const LoadingComponent: React.FC<{
  message: string;
}> = ({message}) => {
  return (
    <View style={styles.loadingContainer}>
      <ActivityIndicator size="large" color={colors.primary} />
      <MyText style={styles.loadingText}>{message}</MyText>
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: 12,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.divider,
    backgroundColor: '#fff',
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  backButtonText: {
    fontSize: 24,
    color: colors.text,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text,
  },
  placeholder: {
    width: 40,
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: colors.divider,
  },
  tabButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: colors.primary,
  },
  tabText: {
    fontSize: 14,
    color: colors.textSecondary,
  },
  activeTabText: {
    color: colors.primary,
    fontWeight: 'bold',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: colors.textSecondary,
  },
  timbreItem: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    position: 'relative',
  },
  selectedTimbreItem: {
    borderWidth: 1,
    borderColor: colors.primary,
  },
  timbreInfo: {
    flex: 1,
    flexDirection: 'row',
    padding: 16,
  },
  timbreIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  timbreIconText: {
    fontSize: 20,
  },
  timbreDetails: {
    flex: 1,
    justifyContent: 'center',
  },
  timbreName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: 4,
  },
  timbreDescription: {
    fontSize: 12,
    color: colors.textSecondary,
    marginBottom: 4,
  },
  timbreActions: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingRight: 12,
  },
  playButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: colors.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  actionButtonText: {
    color: colors.primary,
    fontSize: 16,
  },
  deleteButton: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  deleteButtonText: {
    fontSize: 16,
    color: colors.textSecondary,
    fontWeight: 'bold',
  },
  checkmark: {
    position: 'absolute',
    top: 12,
    right: 12,
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkmarkText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: colors.divider,
    backgroundColor: '#fff',
  },
  confirmButton: {
    backgroundColor: colors.primary,
    borderRadius: 25,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
  },
  confirmButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  // 录音相关样式
  recordButton: {
    backgroundColor: colors.primary,
    borderRadius: 24,
    paddingHorizontal: 32,
    paddingVertical: 12,
  },
  stopButton: {
    backgroundColor: '#ff6b6b',
  },
  recordButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  // 录音弹窗相关样式
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: 8,
  },
  modalSubtitle: {
    fontSize: 14,
    color: colors.textSecondary,
    marginBottom: 16,
  },
  textContainer: {
    width: '100%',
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    padding: 16,
    marginBottom: 24,
  },
  recordingText: {
    fontSize: 16,
    color: colors.text,
    lineHeight: 24,
    textAlign: 'center',
  },
  waveformWrapper: {
    width: '100%',
    alignItems: 'center',
    marginBottom: 24,
  },
  waveformContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 50,
    width: '100%',
    marginBottom: 8,
  },
  waveformBar: {
    width: 4,
    marginHorizontal: 2,
    borderRadius: 2,
    backgroundColor: colors.primary,
  },
  timeText: {
    fontSize: 14,
    color: colors.textSecondary,
  },
  recordButtonContainer: {
    marginBottom: 16,
  },
  closeButton: {
    paddingVertical: 8,
  },
  closeButtonText: {
    color: colors.textSecondary,
    fontSize: 14,
  },
});
