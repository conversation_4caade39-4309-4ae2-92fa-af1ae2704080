import Markdown from 'react-native-markdown-display';
import {typography} from '../theme/theme';
import {MyText} from '../custom/CustomFont';
import {StyleSheet, TextStyle, View, ViewStyle} from 'react-native';
import {useCallback, useEffect, useState} from 'react';

// 聊天item类型
export type ChatItemType =
  | 'doll_query'
  | 'doll_answer'
  | 'parent_query'
  | 'parent_answer'
  | 'shield'
  | 'time';

// item组件
interface ChatItemProps {
  type: ChatItemType;
  text: string;
  it?: AsyncIterable<string>;
}
export const ChatItem: React.FC<ChatItemProps> = ({type, text, it}) => {
  const [content, setContent] = useState<string>(text);
  const fetchContent = useCallback(async () => {
    if (it) {
      for await (const chunk of it) {
        setContent(chunk);
      }
    }
  }, [it]);
  useEffect(() => {
    if (it) {
      fetchContent();
    }
  }, [it]);

  if (type === 'time') {
    return (
      <View style={styles.timeItem}>
        <MyText style={styles.timeText}>{content}</MyText>
      </View>
    );
  }
  let containerStyle: ViewStyle = styles.parentQueryItem;
  let textStyle: TextStyle = styles.parentQueryText;
  switch (type) {
    case 'doll_query':
      containerStyle = styles.dollQueryItem;
      textStyle = styles.dollQueryText;
      break;
    case 'doll_answer':
      containerStyle = styles.dollAnswerItem;
      textStyle = styles.dollAnswerText;
      break;
    case 'parent_query':
      containerStyle = styles.parentQueryItem;
      textStyle = styles.parentQueryText;
      break;
    case 'parent_answer':
      containerStyle = styles.parentAnswerItem;
      textStyle = styles.parentAnswerText;
      break;
    case 'shield':
      containerStyle = styles.shieldItem;
      textStyle = styles.shieldText;
      break;
  }
  return (
    <View style={containerStyle}>
      {type === 'doll_query' || type === 'parent_query' ? (
        <MyText style={textStyle}>{content}</MyText>
      ) : (
        <Markdown
          style={{
            body: {
              fontFamily: typography.fontFamily.regular,
              color: '#6b6b6b',
              fontSize: 15,
            },
            text: {
              fontFamily: typography.fontFamily.regular,
            },
          }}>
          {content}
        </Markdown>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  timeItem: {
    alignItems: 'center',
    marginVertical: 8,
  },
  timeText: {
    color: '#A0A0A0',
    fontSize: 12,
  },
  dollQueryItem: {
    alignSelf: 'flex-start',
    backgroundColor: '#D5EFFE',
    borderRadius: 8,
    marginVertical: 4,
    marginLeft: 18,
    marginRight: 10,
    padding: 10,
  },
  dollQueryText: {
    color: '#09275F',
    fontSize: 15,
  },
  dollAnswerItem: {
    alignSelf: 'flex-start',
    backgroundColor: 'transparent',
    // borderRadius: 0,
    marginVertical: 4,
    marginLeft: 10,
    marginRight: 10,
    padding: 10,
  },
  dollAnswerText: {
    color: '#6b6b6b',
    fontSize: 15,
  },
  parentQueryItem: {
    alignSelf: 'flex-start',
    backgroundColor: '#E1E1FE',
    borderRadius: 8,
    marginVertical: 4,
    marginLeft: 18,
    marginRight: 10,
    padding: 10,
  },
  parentQueryText: {
    color: '#7336F7',
    fontSize: 15,
  },
  parentAnswerItem: {
    alignSelf: 'flex-start',
    // backgroundColor: '#F8F8F8',
    // borderRadius: 8,
    marginVertical: 4,
    marginLeft: 10,
    marginRight: 10,
    padding: 10,
  },
  parentAnswerText: {
    color: '#6b6b6b',
    fontSize: 15,
  },
  shieldItem: {
    alignSelf: 'center',
    backgroundColor: 'transparent',
    marginVertical: 4,
    marginLeft: 10,
    marginRight: 10,
    padding: 10,
  },
  shieldText: {
    color: '#12265B',
    fontSize: 15,
    fontStyle: 'italic',
  },
});
