import React, {useEffect, useState} from 'react';
import {
  Image,
  View,
  ActivityIndicator,
  StyleSheet,
  LayoutChangeEvent,
} from 'react-native';

interface ScalableImageProps {
  source: any; // 支持require本地图片或uri网络图片
  width?: number | 'auto';
  height?: number | 'auto';
  style?: object;
  resizeMode?: 'cover' | 'contain' | 'stretch' | 'repeat' | 'center';
}

type LayoutSize = {width: number; height: number};

const ScalableImage = ({
  source,
  width,
  height,
  style,
  resizeMode = 'stretch',
}: ScalableImageProps) => {
  const [dimensions, setDimensions] = useState({
    calcWidth: 0,
    calcHeight: 0,
    aspectRatio: 0,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);
  const [layoutSize, setLayoutSize] = useState<LayoutSize | null>(null);

  useEffect(() => {
    const calculateDimensions = (layout?: LayoutSize) => {
      if (typeof source === 'number') {
        const {width: imgWidth, height: imgHeight} =
          Image.resolveAssetSource(source);
        computeAspectRatio(imgWidth, imgHeight, layout);
      } else if (source?.uri) {
        Image.getSize(
          source.uri,
          (imgWidth, imgHeight) =>
            computeAspectRatio(imgWidth, imgHeight, layout),
          () => setError(true),
        );
      }
    };

    const computeAspectRatio = (
      imgWidth: number,
      imgHeight: number,
      layout?: LayoutSize,
    ) => {
      const aspectRatio = imgWidth / imgHeight;
      let calcWidth = width;
      let calcHeight = height;

      // 处理'auto'情况
      if (width === 'auto' && layout) {
        calcWidth = layout.width;
      }
      if (height === 'auto' && layout) {
        calcHeight = layout.height;
      }

      // 只传一个时，按比例推算另一个
      if (
        typeof calcWidth === 'number' &&
        (height === undefined || height === 'auto')
      ) {
        calcHeight = calcWidth / aspectRatio;
      } else if (
        typeof calcHeight === 'number' &&
        (width === undefined || width === 'auto')
      ) {
        calcWidth = calcHeight * aspectRatio;
      }

      setDimensions({
        calcWidth: typeof calcWidth === 'number' ? calcWidth : 0,
        calcHeight: typeof calcHeight === 'number' ? calcHeight : 0,
        aspectRatio,
      });
      setLoading(false);
    };

    if ((width === 'auto' || height === 'auto') && !layoutSize) {
      // 等待onLayout
      return;
    }
    calculateDimensions(layoutSize || undefined);
  }, [source, width, height, layoutSize]);

  const handleLayout = (e: LayoutChangeEvent) => {
    const {width: layoutWidth, height: layoutHeight} = e.nativeEvent.layout;
    setLayoutSize({width: layoutWidth, height: layoutHeight});
  };

  if (error) return <View style={[styles.errorContainer, style]} />;
  if (loading) return <ActivityIndicator style={style} />;

  return (
    <View
      style={[
        {
          width: width === 'auto' ? undefined : dimensions.calcWidth,
          height: height === 'auto' ? undefined : dimensions.calcHeight,
        },
        style,
      ]}
      onLayout={
        width === 'auto' || height === 'auto' ? handleLayout : undefined
      }>
      <Image
        source={source}
        style={{
          width: '100%',
          height: '100%',
          aspectRatio: dimensions.aspectRatio,
        }}
        resizeMode={resizeMode}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  errorContainer: {
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default ScalableImage;
