import {Image, StyleProp, StyleSheet, View, ViewStyle} from 'react-native';
import {MyText} from '../custom/CustomFont';
import {useCallback, useMemo, useRef} from 'react';
import ScalableImage from './ScalableImage';

const BatteryConfig: {
  [key: string]: {
    icon: any;
    iconCharge: any;
    num: number;
    color: string;
  };
} = {
  full: {
    icon: require('../assets/img/battery_full.png'),
    iconCharge: require('../assets/img/battery_full_charge.png'),
    num: 90,
    color: '#00983f',
  },
  green: {
    icon: require('../assets/img/battery_green.png'),
    iconCharge: require('../assets/img/battery_green_charge.png'),
    num: 60,
    color: '#00983f',
  },
  yellow: {
    icon: require('../assets/img/battery_yellow.png'),
    iconCharge: require('../assets/img/battery_yellow_charge.png'),
    num: 30,
    color: '#ff9f2c',
  },
  red: {
    icon: require('../assets/img/battery_red.png'),
    iconCharge: require('../assets/img/battery_red_charge.png'),
    num: 0,
    color: '#f44040',
  },
};

export default function BatteryIconText({
  battery,
  isCharging,
  style,
}: {
  battery: number;
  isCharging: boolean;
  style?: StyleProp<ViewStyle>;
}) {
  const orderedConfigs = useMemo(() => {
    return Object.values(BatteryConfig).sort((a, b) => b.num - a.num);
  }, []);
  const config = orderedConfigs.find(item => battery >= item.num);
  return (
    <View style={[styles.container, style]}>
      <ScalableImage
        source={isCharging ? config?.iconCharge : config?.icon}
        style={styles.icon}
        height={12}
      />
      <MyText style={{color: config?.color}}>{battery}%</MyText>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  icon: {
    marginTop: 2,
    marginRight: 5,
  },
});
