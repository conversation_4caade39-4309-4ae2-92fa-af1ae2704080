import React from 'react';
import {Image, ImageProps, StyleSheet} from 'react-native';

interface AutoFitImageProps extends Omit<ImageProps, 'style'> {
  style?: ImageProps['style'];
}

const AutoFitImage: React.FC<AutoFitImageProps> = ({style, ...props}) => {
  return (
    <Image resizeMode="stretch" {...props} style={[styles.autoFit, style]} />
  );
};

const styles = StyleSheet.create({
  autoFit: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    width: '100%',
    height: '100%',
  },
});

export default AutoFitImage;
