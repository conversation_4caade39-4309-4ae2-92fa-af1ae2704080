import React, {useEffect, useState} from 'react';
import {View, Text, Image, StyleSheet, TouchableOpacity} from 'react-native';
import type {DollInfo} from '../net/types';
import httpService from '../net';
import useAppNavigation from '../hooks/useAppNavigation';
import {dollService} from '../services/dollService';
import {ImageSourcePropType} from 'react-native/Libraries/Image/Image';
import ScalableImage from './ScalableImage';
import {useFocusEffect} from '@react-navigation/native';
import {useSetDollInfos} from '../hooks/useAppHooks';
import {MyText} from '../custom/CustomFont';
import BatteryIconText from './BatteryIconText';
interface Props {
  item: DollInfo;
}

const DollItem: React.FC<Props> = ({item}) => {
  const navigation = useAppNavigation();
  const [doll, setDoll] = useState<DollInfo>(item);
  const setDollInfos = useSetDollInfos();

  const [bgSource, setBgSource] = useState<ImageSourcePropType | undefined>(
    undefined,
  );
  const [dollImg, setDollImg] = useState<ImageSourcePropType | undefined>(
    undefined,
  );

  function handleDollStateUpdate() {
    httpService.doll.getDollState({dollId: item.dollId}).then(res => {
      doll.battery = res.data.battery;
      doll.volume = res.data.volume;
      doll.isOnline = res.data.isOnline;
      doll.state = res.data.state;
      doll.unreads = res.data.unreads;
      setDoll(doll);
      setDollInfos(prev => {
        return prev.map(d => (d.dollId === doll.dollId ? doll : d));
      });
    });
  }
  function handleDollClick() {
    navigation.navigate('ChatContent', {doll: item});
  }
  useFocusEffect(() => {
    const interval = setInterval(() => {
      handleDollStateUpdate();
    }, 10 * 1000);
    return () => {
      clearInterval(interval);
    };
  });
  useEffect(() => {
    setBgSource(dollService.getDollBgBoard(doll.dollType));
    setDollImg(dollService.getDollAvatar(doll.dollType));
  }, [doll]);
  const stateInfo = dollService.getDollItemViewInfo(doll);

  return (
    <TouchableOpacity
      style={styles.container}
      onPress={handleDollClick}
      activeOpacity={0.8}>
      <Image source={bgSource} style={styles.bg} resizeMode="stretch" />
      <View style={styles.content}>
        <View style={styles.left}>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              paddingBottom: 5,
            }}>
            <MyText style={styles.name}>{doll.dollName}</MyText>
            <View style={[styles.row, {marginLeft: 10}]}>
              <BatteryIconText
                battery={doll.battery}
                isCharging={doll.isCharging}
              />
            </View>
          </View>

          <MyText style={styles.desc}>{doll.dollDesc}</MyText>
          <View style={styles.stateRow}>
            <View
              style={[
                styles.stateBtn,
                {backgroundColor: stateInfo.bgColor, marginTop: 10},
              ]}>
              <MyText style={[styles.stateText, {color: stateInfo.textColor}]}>
                {stateInfo.text}
              </MyText>
              {doll.unreads > 0 && (
                <View style={styles.unreadDot}>
                  <MyText style={styles.unreadText}>{doll.unreads}</MyText>
                </View>
              )}
            </View>
          </View>
        </View>
        <View
          style={{
            width: 80,
            height: 0,
            // height: 60,
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          <ScalableImage source={dollImg} style={styles.dollImg} width={60} />
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: 0,
    marginVertical: 8,
    borderRadius: 16,
    overflow: 'hidden',
    backgroundColor: '#F6FAFF',
    elevation: 2,
    height: 126,
  },
  bg: {
    ...StyleSheet.absoluteFillObject,
    width: '100%',
    height: '100%',
    zIndex: 0,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    zIndex: 1,
  },
  left: {
    flex: 1,
  },
  name: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  batteryIcon: {
    width: 18,
    height: 18,
    marginRight: 4,
  },
  batteryText: {
    fontSize: 14,
  },
  desc: {
    height: 18,
    fontSize: 13,
    color: '#888',
    // marginBottom: 8,
    overflow: 'hidden',
    textAlignVertical: 'top',
  },
  stateRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  stateBtn: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 4,
    minWidth: 80,
    justifyContent: 'center',
  },
  stateText: {
    fontSize: 14,
    fontWeight: 'bold',
    marginTop: -1,
  },
  unreadDot: {
    backgroundColor: '#FF3B30',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 6,
    paddingHorizontal: 4,
  },
  unreadText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  dollImg: {
    // width: 90,
    // height: 90,
    marginLeft: 8,
  },
});

export default DollItem;
