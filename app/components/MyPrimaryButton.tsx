import {StyleProp, StyleSheet, TextStyle, ViewStyle} from 'react-native';
import {TouchableOpacity} from 'react-native';
import {MyText} from '../custom/CustomFont';
import {borderRadius, colors} from '../theme/theme';

export default function MyPrimaryButton({
  children,
  onPress,
  style,
  disabled,
}: {
  children?: React.ReactNode;
  onPress: () => void;
  style?: StyleProp<ViewStyle>;
  disabled?: boolean;
}) {
  return (
    <TouchableOpacity
      style={[styles.addBtn, style, disabled && {opacity: 0.5}]}
      onPress={onPress}>
      {children}
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  addBtn: {
    borderWidth: 1,
    borderColor: colors.butPurpleBorder,
    borderRadius: borderRadius.md,
    backgroundColor: colors.butPurpleBg,
    alignItems: 'center',
    justifyContent: 'center',
    height: 48,
    flexDirection: 'row',
    // width: '80%',
  },
});
