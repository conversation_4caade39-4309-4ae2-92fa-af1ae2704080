import React, {useEffect, useRef, useState} from 'react';
import {
  Image,
  View,
  StyleSheet,
  ImageSourcePropType,
  StyleProp,
  ViewStyle,
  ImageStyle,
} from 'react-native';

interface FrameAnimationProps {
  frames: ImageSourcePropType[]; // 图片序列
  frameRate?: number; // 帧率，默认12
  style?: StyleProp<ViewStyle>;
  imageStyle?: StyleProp<ImageStyle>;
  playing?: boolean; // 是否播放
  loop?: boolean; // 是否循环
  onEnd?: () => void; // 非循环时播放结束回调
}

const FrameAnimation: React.FC<FrameAnimationProps> = ({
  frames,
  frameRate = 12,
  style,
  imageStyle,
  playing = true,
  loop = true,
  onEnd,
}) => {
  const [current, setCurrent] = useState(0);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const framesCount = frames?.length || 0;

  // 控制播放
  useEffect(() => {
    if (!frames || frames.length === 0) return;
    if (!playing) {
      timerRef.current && clearInterval(timerRef.current);
      return;
    }
    timerRef.current && clearInterval(timerRef.current);
    timerRef.current = setInterval(() => {
      setCurrent(prev => {
        if (prev + 1 >= framesCount) {
          if (loop) {
            return 0;
          } else {
            timerRef.current && clearInterval(timerRef.current);
            onEnd && onEnd();
            return prev; // 停在最后一帧
          }
        }
        return prev + 1;
      });
    }, 1000 / frameRate);
    return () => {
      timerRef.current && clearInterval(timerRef.current);
    };
  }, [frames, frameRate, playing, loop, onEnd]);

  // 切换图片源时重置current
  useEffect(() => {
    setCurrent(0);
  }, [frames]);

  if (!frames || frames.length === 0) return null;

  return (
    <View style={style}>
      <Image
        source={frames[current]}
        style={[{width: '100%', height: '100%'}, imageStyle]}
        resizeMode="contain"
      />
    </View>
  );
};

export default FrameAnimation;
