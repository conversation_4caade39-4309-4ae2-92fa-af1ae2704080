import React, {useEffect, useState} from 'react';
import {Alert, Platform} from 'react-native';
import PermissionManager, {PermissionType} from '../services/PermissionManager';
import {RESULTS, PermissionStatus, Permission} from 'react-native-permissions';
import Logger from '../services/LoggerService';

interface PermissionCheckerProps {
  // 需要检查的权限类型列表
  permissions?: PermissionType[];
  // 是否自动请求权限
  autoRequest?: boolean;
  // 自定义权限提示文本
  permissionMessages?: {
    [key in PermissionType]?: string;
  };
}

/**
 * 隐形权限检查器组件
 * 用于在组件挂载时检查并提醒用户授权必要的权限
 */
const PermissionChecker: React.FC<PermissionCheckerProps> = ({
  permissions = [
    PermissionType.BLUETOOTH,
    PermissionType.LOCATION,
    PermissionType.MICROPHONE,
  ],
  autoRequest = true,
  permissionMessages,
}) => {
  const [checkedPermissions, setCheckedPermissions] = useState<
    Record<PermissionType, PermissionStatus>
  >({} as Record<PermissionType, PermissionStatus>);
  const [permissionsChecked, setPermissionsChecked] = useState(false);

  // 默认权限说明
  const defaultMessages: Record<PermissionType, string> = {
    [PermissionType.BLUETOOTH]: '需要蓝牙权限来连接设备',
    [PermissionType.LOCATION]: '需要位置权限来发现附近的设备和WiFi网络',
    [PermissionType.MICROPHONE]: '需要麦克风权限来进行语音录制',
    [PermissionType.CAMERA]: '需要相机权限来扫描二维码或拍摄照片',
    [PermissionType.PHOTO_LIBRARY]: '需要存储权限来保存数据和媒体文件',
    [PermissionType.CONTACTS]: '需要通讯录权限来分享设备信息',
    [PermissionType.MEDIA_LIBRARY]: '需要媒体库权限来访问音频文件',
  };

  // 合并自定义消息
  const messages = {...defaultMessages, ...permissionMessages};

  // 检查权限状态
  const checkPermissions = async () => {
    try {
      const results =
        await PermissionManager.checkMultiplePermissions(permissions);
      setCheckedPermissions(results);
      setPermissionsChecked(true);
      Logger.info('权限检查结果', results);

      // 返回需要请求的权限
      return Object.entries(results)
        .filter(([_, status]) => status !== RESULTS.GRANTED)
        .map(([type]) => type as PermissionType);
    } catch (error) {
      Logger.error('权限检查失败', error);
      return permissions;
    }
  };

  // 处理权限请求结果
  const handlePermissionResult = async (
    type: PermissionType,
    result: Record<Permission, PermissionStatus>,
  ) => {
    // 获取权限状态
    const status = Object.values(result)[0];
    if (!status) return;

    if (status === RESULTS.BLOCKED) {
      Alert.alert(
        '权限未开启',
        `${messages[type]}\n请前往设置页面手动开启权限。`,
        [
          {text: '取消', style: 'cancel'},
          {
            text: '前往设置',
            onPress: () => PermissionManager.openSettings(),
          },
        ],
      );
    } else if (status === RESULTS.DENIED) {
      Alert.alert('需要权限', messages[type], [
        {text: '暂不授权', style: 'cancel'},
        {
          text: '授权',
          onPress: async () => {
            const result = await PermissionManager.requestPermission(type);
            await handlePermissionResult(type, result);
          },
        },
      ]);
    }
  };

  // 请求缺失的权限
  const requestMissingPermissions = async (
    missingPermissions: PermissionType[],
  ) => {
    if (missingPermissions.length === 0) return;

    // iOS 上直接请求权限
    if (Platform.OS === 'ios') {
      for (const permission of missingPermissions) {
        const result = await PermissionManager.requestPermission(permission);
        await handlePermissionResult(permission, result);
      }
      return;
    }

    // Android 上先显示权限说明
    const permissionTexts = missingPermissions
      .map(p => `• ${messages[p]}`)
      .join('\n');

    Alert.alert(
      '需要授权权限',
      `为了完整使用应用功能，请授予以下权限:\n\n${permissionTexts}`,
      [
        {
          text: '稍后再说',
          style: 'cancel',
        },
        {
          text: '授予权限',
          onPress: async () => {
            for (const permission of missingPermissions) {
              const result =
                await PermissionManager.requestPermission(permission);
              await handlePermissionResult(permission, result);
            }
          },
        },
      ],
    );
  };

  // 组件挂载时检查权限
  useEffect(() => {
    const initPermissions = async () => {
      const missingPermissions = await checkPermissions();
      if (autoRequest && missingPermissions.length > 0) {
        setTimeout(() => {
          requestMissingPermissions(missingPermissions);
        }, 1000);
      }
    };

    initPermissions();
  }, []);

  // 这是一个隐形组件，不渲染任何UI
  return <></>;
};

export default PermissionChecker;
