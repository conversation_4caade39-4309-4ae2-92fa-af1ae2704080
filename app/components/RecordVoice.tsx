import React, {useState, useRef, useEffect} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  PanResponder,
  Animated,
  Platform,
  PermissionsAndroid,
} from 'react-native';
import AudioRecorderPlayer, {
  AudioEncoderAndroidType,
  AudioSourceAndroidType,
  AVEncoderAudioQualityIOSType,
  AVEncodingOption,
  RecordBackType,
} from 'react-native-audio-recorder-player';
import {colors} from '../theme/theme';
import {MyText} from '../custom/CustomFont';

// 录音状态常量
const RECORD_IDLE = 0; // 空闲状态
const RECORDING = 1; // 正在录音
const RECORD_CANCEL = 2; // 取消录音状态

type Props = {
  onFinishRecord?: (uri: string) => void;
  buttonStyle?: object;
  containerStyle?: object;
};

const RecordVoice: React.FC<Props> = ({
  onFinishRecord,
  buttonStyle,
  containerStyle,
}) => {
  // 状态管理
  const [recordState, setRecordState] = useState(RECORD_IDLE);
  const [recording, setRecording] = useState(false);
  const [currentTime, setCurrentTime] = useState('00:00');
  const [volume, setVolume] = useState(0);
  const [filePath, setFilePath] = useState('');

  // 动画值
  const volumeAnim = useRef(new Animated.Value(1)).current;

  // 录音器实例
  const audioRecorderPlayer = useRef(new AudioRecorderPlayer()).current;

  // 清理函数
  useEffect(() => {
    return () => {
      if (recording) {
        stopRecord();
      }
    };
  }, [recording]);

  // 请求Android权限
  const requestPermission = async () => {
    if (Platform.OS !== 'android') return true;

    try {
      const grants = await PermissionsAndroid.requestMultiple([
        PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
        PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
      ]);

      return (
        grants[PermissionsAndroid.PERMISSIONS.RECORD_AUDIO] ===
          PermissionsAndroid.RESULTS.GRANTED &&
        grants[PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE] ===
          PermissionsAndroid.RESULTS.GRANTED
      );
    } catch (err) {
      console.error('权限请求失败:', err);
      return false;
    }
  };

  // 开始录音
  const startRecord = async () => {
    try {
      if (Platform.OS === 'android') {
        const granted = await requestPermission();
        if (!granted) {
          console.log('需要录音权限');
          return;
        }
      }

      // 设置音频参数
      const audioSet = {
        AudioEncoderAndroid: AudioEncoderAndroidType.AAC,
        AudioSourceAndroid: AudioSourceAndroidType.MIC,
        AVEncoderAudioQualityKeyIOS: AVEncoderAudioQualityIOSType.high,
        AVNumberOfChannelsKeyIOS: 2,
        AVFormatIDKeyIOS: AVEncodingOption.aac,
      };

      // 生成文件路径 - 使用临时文件名
      const fileName = `voice_record_${Date.now()}`;
      const path =
        Platform.OS === 'ios' ? `${fileName}.m4a` : `${fileName}.mp3`;

      setFilePath(path);

      // 开始录音
      const uri = await audioRecorderPlayer.startRecorder(path, audioSet);

      // 添加音量监听器
      audioRecorderPlayer.addRecordBackListener((e: RecordBackType) => {
        setVolume(e.currentMetering || 0);
        setCurrentTime(
          audioRecorderPlayer.mmssss(Math.floor(e.currentPosition)),
        );

        // 根据音量大小制作波形动画
        Animated.timing(volumeAnim, {
          toValue: Math.max(1, Math.min(2, 1 + (e.currentMetering || 0) / 120)),
          duration: 100,
          useNativeDriver: true,
        }).start();
      });

      console.log(`开始录音: ${uri}`);
      setRecording(true);
    } catch (error) {
      console.error('录音失败:', error);
      setRecording(false);
    }
  };

  // 停止录音
  const stopRecord = async () => {
    try {
      if (!recording) return;

      const result = await audioRecorderPlayer.stopRecorder();
      audioRecorderPlayer.removeRecordBackListener();
      setRecording(false);
      setCurrentTime('00:00');
      setVolume(0);
      volumeAnim.setValue(1);

      console.log('录音结束:', result);
      return result;
    } catch (error) {
      console.error('停止录音失败:', error);
      setRecording(false);
      setCurrentTime('00:00');
      setVolume(0);
      volumeAnim.setValue(1);
    }
  };

  // 取消录音
  const cancelRecord = async () => {
    try {
      if (!recording) return;

      await audioRecorderPlayer.stopRecorder();
      audioRecorderPlayer.removeRecordBackListener();
      setRecording(false);
      setCurrentTime('00:00');
      setVolume(0);
      volumeAnim.setValue(1);

      console.log('录音已取消');
    } catch (error) {
      console.error('取消录音失败:', error);
      setRecording(false);
      setCurrentTime('00:00');
      setVolume(0);
      volumeAnim.setValue(1);
    }
  };

  // 手势响应器
  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onPanResponderGrant: () => {
        // 按下时开始录音
        setRecordState(RECORDING);
        startRecord();
      },
      onPanResponderMove: (_, gesture) => {
        // 上滑一定距离触发取消录音状态
        if (gesture.dy < -50) {
          setRecordState(RECORD_CANCEL);
        } else {
          setRecordState(RECORDING);
        }
      },
      onPanResponderRelease: async (_, gesture) => {
        // 松开手指结束录音
        if (recordState === RECORDING && recording) {
          const uri = await stopRecord();
          if (uri && onFinishRecord) {
            onFinishRecord(uri);
          }
        } else if (recordState === RECORD_CANCEL) {
          // 在取消状态下松开，取消录音
          cancelRecord();
        }
        setRecordState(RECORD_IDLE);
      },
      onPanResponderTerminate: () => {
        // 手势被打断时取消录音
        cancelRecord();
        setRecordState(RECORD_IDLE);
      },
    }),
  ).current;

  // 渲染录音按钮
  const renderButton = () => {
    if (!recording) {
      return (
        <TouchableOpacity
          activeOpacity={0.7}
          style={[styles.recordButton, buttonStyle]}
          {...panResponder.panHandlers}>
          <MyText style={styles.buttonText}>按住说话</MyText>
        </TouchableOpacity>
      );
    }

    return (
      <Animated.View
        style={[
          styles.recordingButton,
          recordState === RECORD_CANCEL
            ? styles.cancelingButton
            : styles.activeButton,
          {transform: [{scale: volumeAnim}]},
          buttonStyle,
        ]}
        {...panResponder.panHandlers}>
        <MyText style={styles.recordingText}>{currentTime}</MyText>
      </Animated.View>
    );
  };

  return (
    <View style={[styles.container, containerStyle]}>
      {recordState === RECORD_CANCEL && (
        <View style={styles.cancelTipContainer}>
          <MyText style={styles.cancelTip}>松手取消录音</MyText>
        </View>
      )}

      {recording && recordState === RECORDING && (
        <MyText style={styles.recordingTip}>
          上滑取消录音 • 音量: {Math.round(volume)}
        </MyText>
      )}

      {renderButton()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  recordButton: {
    width: 150,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.divider,
  },
  buttonText: {
    fontSize: 16,
    color: colors.text,
  },
  recordingButton: {
    width: 150,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
  },
  activeButton: {
    borderColor: '#7B61FF',
    backgroundColor: '#F1EDFF',
  },
  cancelingButton: {
    borderColor: '#FF6B81',
    backgroundColor: '#FFE6EA',
  },
  recordingText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  recordingTip: {
    position: 'absolute',
    top: -25,
    fontSize: 14,
    color: '#7B61FF',
  },
  cancelTipContainer: {
    position: 'absolute',
    top: -40,
    backgroundColor: '#FFE6EA',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 16,
  },
  cancelTip: {
    color: '#FF6B81',
    fontWeight: 'bold',
  },
});

export default RecordVoice;
