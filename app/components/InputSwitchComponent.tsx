import React, {useRef, useState} from 'react';
import {
  View,
  TouchableOpacity,
  Image,
  StyleSheet,
  Keyboard,
  Animated,
  TextInput,
} from 'react-native';
import {colors} from '../theme/theme';
import {MyTextInput} from '../custom/CustomFont';

// 资源
const iconSend = require('../assets/img/mask_group_3x_6_.png');

// 录音状态
const RECORD_IDLE = 0;
const RECORDING = 1;
const RECORD_CANCEL = 2;

type InputMode = 'text' | 'voice';

type Props = {
  onTextSend?: (text: string) => void;
  onVoiceSend?: (voice: any) => void;
};

const InputSwitchComponent: React.FC<Props> = ({onTextSend, onVoiceSend}) => {
  const [mode, setMode] = useState<InputMode>('text');
  const [text, setText] = useState('');
  const [recordState, setRecordState] = useState(RECORD_IDLE);
  const [recording, setRecording] = useState(false);
  const [waveAnim] = useState(new Animated.Value(1));
  const inputRef = useRef<TextInput>(null);

  // 切换输入模式
  const handleSwitch = () => {
    if (mode === 'text') {
      Keyboard.dismiss();
      setMode('voice');
    } else {
      setMode('text');
    }
  };

  // 发送文字
  const handleSend = () => {
    if (text.trim()) {
      onTextSend?.(text.trim());
      setText('');
    }
  };

  // 文字输入UI
  const renderTextInput = () => (
    <View style={{flexDirection: 'row', alignItems: 'center'}}>
      <View style={styles.textInputContainer}>
        <MyTextInput
          ref={inputRef}
          style={styles.textInput}
          placeholder="发消息"
          placeholderTextColor="#BDBDBD"
          value={text}
          onChangeText={setText}
          returnKeyType="send"
          onSubmitEditing={handleSend}
        />
      </View>
      <TouchableOpacity
        style={styles.sendBtn}
        onPress={handleSend}
        activeOpacity={0.7}>
        <Image source={iconSend} style={styles.sendIcon} />
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={styles.container}>
      {/* 暂时没有语音聊天 */}
      {/* <TouchableOpacity
        style={styles.switchBtn}
        onPress={handleSwitch}
        activeOpacity={0.7}>
        <Image
          source={mode === 'text' ? iconVoice : iconKeyboard}
          style={styles.switchIcon}
        />
      </TouchableOpacity> */}
      <View style={styles.inputArea}>{renderTextInput()}</View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    // backgroundColor: '#F6FAFF',
    // paddingBottom: 24,
  },
  switchBtn: {
    width: 42,
    height: 42,
    borderRadius: 12,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 4,
    shadowColor: '#E6E6E6',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  sendBtn: {
    width: 42,
    height: 42,
    borderRadius: 12,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 4,
    shadowColor: '#E6E6E6',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  switchIcon: {
    width: 24,
    height: 24,
    resizeMode: 'contain',
  },
  inputArea: {
    flex: 1,
    height: 44,
  },
  textInputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 13,
    paddingHorizontal: 16,
    height: 44,
    shadowColor: '#E6E6E6',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
    borderWidth: 1,
    borderColor: colors.divider,
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    color: '#222',
    paddingVertical: 0,
  },
  sendIcon: {
    width: 22,
    height: 22,
    resizeMode: 'contain',
  },
  voiceInputBtn: {
    backgroundColor: '#fff',
    borderRadius: 10,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    borderWidth: 1,
    borderColor: colors.divider,
  },
  voiceInputText: {
    color: colors.text,
    fontSize: 16,
  },
  waveContainer: {
    flex: 1,
    height: 44,
    borderRadius: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 0,
    marginVertical: 0,
    borderWidth: 2,
  },
  waveNormal: {
    borderColor: '#7B61FF',
    backgroundColor: '#F1EDFF',
  },
  waveCancel: {
    borderColor: '#FF6B81',
    backgroundColor: '#FFE6EA',
  },
  waveTip: {
    position: 'absolute',
    top: -22,
    width: '100%',
    textAlign: 'center',
    fontSize: 13,
  },
  waveTipNormal: {
    color: '#7B61FF',
  },
  waveTipCancel: {
    color: '#FF6B81',
  },
  waveImg: {
    width: 180,
    height: 24,
    resizeMode: 'contain',
  },
});

export default InputSwitchComponent;
