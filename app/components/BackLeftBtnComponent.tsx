import {StyleSheet, TouchableOpacity, View, ViewStyle} from 'react-native';
import Icon from 'react-native-vector-icons/AntDesign';
import {colors} from '../theme/theme';
import {useNavigation} from '@react-navigation/native';

const BackLeftBtnComponent = (props: {
  style?: ViewStyle;
  onPress?: () => void;
}) => {
  const navigation = useNavigation();
  const {style, onPress} = props;
  return (
    <TouchableOpacity
      style={[styles.container, style]}
      onPress={() => {
        if (onPress) {
          onPress();
        } else {
          navigation.goBack();
        }
      }}>
      <Icon name="left" size={24} color={colors.text} />
    </TouchableOpacity>
  );
};
const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    left: 10,
  },
});
export default BackLeftBtnComponent;
