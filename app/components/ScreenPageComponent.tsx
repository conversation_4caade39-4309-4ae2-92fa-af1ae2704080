import {StyleProp, View, ViewStyle} from 'react-native';
import AppBackgroundImg, {BgType} from './AppBackgroudImg';
import {SafeAreaView} from 'react-native-safe-area-context';
import CustomKeyboardAvoidingView from './CustomKeyboardAvoidingView';

export default function ScreenPageComponent({
  children,
  style,
  bgType,
  keyboardAvoiding = false,
}: {
  children: React.ReactNode;
  style?: StyleProp<ViewStyle>;
  bgType: BgType;
  keyboardAvoiding?: boolean;
}) {
  return (
    <View style={[{flex: 1}, style]}>
      <AppBackgroundImg type={bgType} />
      <SafeAreaView style={{flex: 1}}>
        {keyboardAvoiding ? (
          <CustomKeyboardAvoidingView>{children}</CustomKeyboardAvoidingView>
        ) : (
          children
        )}
      </SafeAreaView>
    </View>
  );
}
