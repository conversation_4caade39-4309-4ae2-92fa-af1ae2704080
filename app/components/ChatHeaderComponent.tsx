import React from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import type {DollInfo} from '../net/types';
import {dollService} from '../services/dollService';
import {useNavigation} from '@react-navigation/native';
import {useSafeWndValue} from '../hooks/useSafeWnd';
import ScalableImage from './ScalableImage';
import AutoFitImage from './AutoFitImage';
import {MyText} from '../custom/CustomFont';
import BatteryIconText from './BatteryIconText';
import {colors} from '../theme/theme';
interface ChatHeaderProps {
  doll: DollInfo;
  mode: 'summary' | 'status';
}

const width = Dimensions.get('window').width;
const ChatHeaderComponent: React.FC<ChatHeaderProps> = ({doll, mode}) => {
  const navigation = useNavigation();
  const [avatar, setAvatar] = React.useState<any>(null);
  React.useEffect(() => {
    // dollService.getDollAvatar(doll.dollType).then(setAvatar);
  }, [doll.dollType]);
  function handleSummaryPress() {
    navigation.navigate('ContentSummary', {dollId: doll.dollId});
  }

  const stateInfo = dollService.getDollItemViewInfo(doll);

  return (
    <View style={styles.wrapper}>
      {/* <View style={styles.bgGradient} /> */}
      <View style={styles.contentRow}>
        {mode === 'summary' ? (
          <View style={styles.summaryBoard}>
            <TouchableOpacity
              style={{
                flex: 1,
                width: '100%',
                height: '100%',
                margin: 0,
                padding: 0,
              }}
              onPress={handleSummaryPress}
              activeOpacity={0.7}>
              <AutoFitImage
                source={dollService.getDollSummaryBoard(doll.dollType)}
              />
            </TouchableOpacity>
          </View>
        ) : (
          <View style={styles.statusBoard}>
            <AutoFitImage
              source={dollService.getDollChatBoard(doll.dollType)}
            />
            <View style={styles.statusContent}>
              <View style={styles.statusRow}>
                <MyText
                  style={[styles.stateText, {color: stateInfo.textColor}]}>
                  {stateInfo.text}
                </MyText>
                {doll.isOnline && (
                  <MyText style={styles.onlineDot}>
                    {' '}
                    ● <MyText style={styles.onlineText}>在线</MyText>
                  </MyText>
                )}
              </View>
              <View style={styles.progressRow}>
                <View style={styles.progressBarWrap}>
                  <View
                    style={[styles.progressBar, {width: `${doll.volume}%`}]}
                  />
                </View>
                <MyText style={styles.progressLabel}>音量{doll.volume}%</MyText>
                <BatteryIconText
                  battery={doll.battery}
                  isCharging={doll.isCharging}
                />
              </View>
            </View>
          </View>
        )}
        {/* {avatar && <Image source={avatar} style={styles.avatar} />} */}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    margin: 10,
    // borderRadius: 18,
    // borderWidth: 1.5,
    // borderColor: '#B6AFFF',
    // overflow: 'hidden',
    backgroundColor: 'transparent',
  },

  contentRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 0,
    zIndex: 1,
  },
  summaryBoard: {
    width: width * 0.9,
    height: width * 0.22,
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    margin: 0,
    padding: 0,
  },
  statusBoard: {
    justifyContent: 'flex-end',
    width: width * 0.9,
    height: width * 0.3,
    flex: 1,
    margin: 0,
    padding: 0,
  },
  statusContent: {
    margin: 10,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#223366',
    marginBottom: 8,
    // fontFamily: 'PingFangSC-Heavy', // 如需自定义字体可调整
  },
  summaryLink: {
    fontSize: 15,
    color: '#1CA5F8',
    marginTop: 2,
  },
  statusRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  stateText: {
    fontSize: 13,
    fontWeight: 'bold',
  },
  onlineDot: {
    fontSize: 8,
    color: '#2AC98D',
    marginTop: 2,
    marginLeft: 8,
    // marginBottom: 4,
  },
  onlineText: {
    color: '#2AC98D',
    fontSize: 12,
    fontWeight: 'normal',
  },
  progressRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 2,
  },
  progressBarWrap: {
    width: 140,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#E6E6F6',
    marginRight: 8,
    overflow: 'hidden',
  },
  progressBar: {
    height: 8,
    borderRadius: 4,
    backgroundColor: '#7B61FF',
  },
  progressLabel: {
    fontSize: 12,
    color: colors.primary,
    // marginHorizontal: 6,
    marginRight: 20,
  },
  avatar: {
    width: 90,
    height: 90,
    marginLeft: 8,
    resizeMode: 'contain',
  },
});

export default ChatHeaderComponent;
