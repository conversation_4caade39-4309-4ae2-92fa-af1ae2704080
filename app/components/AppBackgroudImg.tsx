import {ImageBackground} from 'react-native';
import {useSafeWndValue} from '../hooks/useSafeWnd';

const bgs = {
  bg_splash: require('../assets/img/bg_splash.png'),
  bg_login: require('../assets/img/bg_login.png'),
  bg_doll: require('../assets/img/bg_doll.png'),
  bg_profile: require('../assets/img/bg_profile.png'),
  bg_chat: require('../assets/img/bg_chat.png'),
};
export type BgType = keyof typeof bgs;
const AppBackgroundImg: React.FC<{type?: BgType}> = ({type = 'bg_login'}) => {
  const {width, height} = useSafeWndValue();
  return (
    <ImageBackground
      source={bgs[type]}
      style={{position: 'absolute', top: 0, left: 0, width, height}}
      resizeMode="cover"
    />
  );
};

export default AppBackgroundImg;
