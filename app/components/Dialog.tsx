import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  TouchableWithoutFeedback,
  ActivityIndicator,
} from 'react-native';
import {colors} from '../theme/theme';
import AutoFitImage from './AutoFitImage';
import {MyText} from '../custom/CustomFont';

export type DialogProps = {
  visible: boolean;
  title?: string;
  content: string;
  cancelText?: string;
  confirmText?: string;
  onCancel?: () => void;
  onConfirm?: () => void;
  onClose?: () => void;
  isDanger?: boolean;
  isLoading?: boolean;
};

const Dialog: React.FC<DialogProps> = ({
  visible,
  title,
  content,
  cancelText = '取消',
  confirmText = '确定',
  onCancel,
  onConfirm,
  onClose,
  isDanger = false,
  isLoading = false,
}) => {
  return (
    <Modal
      transparent
      visible={visible}
      animationType="fade"
      onRequestClose={onClose || onCancel}>
      <TouchableWithoutFeedback
        onPress={isLoading ? undefined : onClose || onCancel}>
        <View style={styles.overlay}>
          <TouchableWithoutFeedback onPress={undefined}>
            <View style={styles.dialogContainer}>
              <AutoFitImage
                source={require('../assets/img/mask_group_3x_12_.png')}
              />
              {title && <MyText style={styles.title}>{title}</MyText>}
              <MyText style={styles.content}>{content}</MyText>
              <View style={styles.buttonContainer}>
                {onCancel && (
                  <TouchableOpacity
                    style={[styles.button, styles.cancelButton]}
                    onPress={isLoading ? undefined : onCancel}
                    disabled={isLoading}>
                    <MyText
                      style={[
                        styles.cancelButtonText,
                        isLoading && styles.disabledButtonText,
                      ]}>
                      {cancelText}
                    </MyText>
                  </TouchableOpacity>
                )}
                {onConfirm && (
                  <TouchableOpacity
                    style={[styles.button, styles.confirmButton]}
                    onPress={isLoading ? undefined : onConfirm}
                    disabled={isLoading}>
                    {isLoading ? (
                      <ActivityIndicator
                        size="small"
                        color={isDanger ? '#f44336' : colors.primary}
                      />
                    ) : (
                      <MyText
                        style={[
                          styles.confirmButtonText,
                          isDanger && styles.dangerButtonText,
                        ]}>
                        {confirmText}
                      </MyText>
                    )}
                  </TouchableOpacity>
                )}
              </View>
            </View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  dialogContainer: {
    width: '80%',
    backgroundColor: '#fff',
    borderRadius: 8,
    overflow: 'hidden',
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.text,
    marginTop: 20,
    marginBottom: 8,
    textAlign: 'center',
  },
  content: {
    fontSize: 14,
    color: colors.text,
    marginBottom: 20,
    marginHorizontal: 20,
    textAlign: 'center',
    lineHeight: 20,
  },
  buttonContainer: {
    flexDirection: 'row',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  button: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
  },
  cancelButton: {
    borderRightWidth: 1,
    borderRightColor: '#e0e0e0',
  },
  confirmButton: {
    backgroundColor: '#fff',
  },
  cancelButtonText: {
    fontSize: 14,
    color: colors.textSecondary,
  },
  confirmButtonText: {
    fontSize: 14,
    color: colors.primary,
    fontWeight: '500',
  },
  dangerButtonText: {
    color: '#f44336',
  },
  disabledButtonText: {
    opacity: 0.5,
  },
});

export default Dialog;
