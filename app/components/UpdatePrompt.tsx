import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  ActivityIndicator,
  Platform,
} from 'react-native';
import {useAtom} from 'jotai';
import UpdateManager, {
  updateStatusAtom,
  updateInfoAtom,
  UpdateInfo,
} from '../services/UpdateManager';
import {MyText} from '../custom/CustomFont';

interface UpdatePromptProps {
  checkOnMount?: boolean; // 是否在组件挂载时检查更新
  autoCheck?: boolean; // 是否自动检查更新
  checkInterval?: number; // 自动检查间隔(毫秒)
}

const UpdatePrompt = ({
  checkOnMount = true,
  autoCheck = false,
  checkInterval = 3600000, // 默认1小时检查一次
}: UpdatePromptProps) => {
  const [updateStatus] = useAtom(updateStatusAtom);
  const [updateInfo] = useAtom(updateInfoAtom);
  const [visible, setVisible] = useState(false);

  // 检查更新
  const checkForUpdates = async () => {
    const {hasUpdate} = await UpdateManager.checkForUpdates();
    if (hasUpdate) {
      setVisible(true);
    }
  };

  // 处理更新
  const handleUpdate = async () => {
    if (updateInfo) {
      await UpdateManager.downloadUpdate(updateInfo);
    }
  };

  // 关闭弹窗
  const handleClose = () => {
    setVisible(false);
  };

  // 在组件挂载时检查更新
  useEffect(() => {
    if (checkOnMount) {
      checkForUpdates();
    }
  }, []);

  // 设置自动检查更新
  useEffect(() => {
    if (!autoCheck) return;

    const intervalId = setInterval(() => {
      checkForUpdates();
    }, checkInterval);

    return () => clearInterval(intervalId);
  }, [autoCheck, checkInterval]);

  // 根据更新状态显示不同内容
  const getStatusContent = () => {
    switch (updateStatus) {
      case 'checking':
        return (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#007AFF" />
            <MyText style={styles.loadingText}>正在检查更新...</MyText>
          </View>
        );
      case 'downloading':
        return (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#007AFF" />
            <MyText style={styles.loadingText}>
              正在前往{Platform.OS === 'ios' ? 'App Store' : '应用商店'}...
            </MyText>
          </View>
        );
      case 'available':
        if (!updateInfo) return null;
        return (
          <View style={styles.container}>
            <MyText style={styles.title}>发现新版本</MyText>
            <MyText style={styles.version}>
              新版本: {updateInfo.version} (当前:{' '}
              {UpdateManager.getAppVersion().version})
            </MyText>
            <MyText style={styles.notes}>{updateInfo.releaseNotes}</MyText>

            <View style={styles.buttonContainer}>
              {!UpdateManager.isForceUpdateRequired(updateInfo) && (
                <TouchableOpacity
                  style={[styles.button, styles.laterButton]}
                  onPress={handleClose}>
                  <MyText style={styles.laterButtonText}>稍后更新</MyText>
                </TouchableOpacity>
              )}
              <TouchableOpacity
                style={[
                  styles.button,
                  styles.updateButton,
                  UpdateManager.isForceUpdateRequired(updateInfo) &&
                    styles.fullWidthButton,
                ]}
                onPress={handleUpdate}>
                <MyText style={styles.updateButtonText}>立即更新</MyText>
              </TouchableOpacity>
            </View>
          </View>
        );
      case 'error':
        return (
          <View style={styles.container}>
            <MyText style={styles.title}>更新检查失败</MyText>
            <MyText style={styles.errorText}>
              检查更新时发生错误,请稍后再试。
            </MyText>
            <TouchableOpacity
              style={[
                styles.button,
                styles.updateButton,
                styles.fullWidthButton,
              ]}
              onPress={handleClose}>
              <MyText style={styles.updateButtonText}>确定</MyText>
            </TouchableOpacity>
          </View>
        );
      default:
        return null;
    }
  };

  if (!visible) return null;

  return (
    <Modal visible={visible} transparent animationType="fade">
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>{getStatusContent()}</View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 10,
    width: '100%',
    maxWidth: 400,
    overflow: 'hidden',
  },
  container: {
    padding: 20,
  },
  loadingContainer: {
    padding: 30,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: 15,
    fontSize: 16,
    color: '#333',
    textAlign: 'center',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
  },
  version: {
    fontSize: 16,
    marginBottom: 15,
    color: '#666',
    textAlign: 'center',
  },
  notes: {
    fontSize: 15,
    lineHeight: 22,
    marginBottom: 20,
    color: '#333',
    backgroundColor: '#f8f8f8',
    padding: 15,
    borderRadius: 5,
  },
  errorText: {
    fontSize: 15,
    marginBottom: 20,
    color: '#666',
    textAlign: 'center',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  button: {
    flex: 1,
    height: 45,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  updateButton: {
    backgroundColor: '#007AFF',
    marginLeft: 8,
  },
  laterButton: {
    backgroundColor: '#f0f0f0',
    marginRight: 8,
  },
  fullWidthButton: {
    marginLeft: 0,
    marginRight: 0,
  },
  updateButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  laterButtonText: {
    color: '#333',
    fontSize: 16,
  },
});

export default UpdatePrompt;
