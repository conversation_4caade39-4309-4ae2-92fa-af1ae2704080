import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import {FlatList, StyleSheet, View} from 'react-native';
import type {ChatMessage} from '../net/types';
import Logger from '../services/LoggerService';
import {
  ParentChatMessageWithIt,
  useDollMsgHistory,
  useParentMsgHistory,
} from '../hooks/useAppHooks';
import {useFocusEffect, useIsFocused} from '@react-navigation/native';
import {ChatItem, ChatItemType} from './ChatItem';
import {formatTimeChatItem} from '../utils/utils';
import {ChatModel} from '../model/ChatModel';

// 每页消息数量
const PAGE_SIZE = 10;

// content组件
interface ChatContentProps {
  type: 'doll' | 'parent';
  dollId?: string; // dollId 或 parentId
  curTabType: 'doll' | 'parent';
}

interface RenderItemType {
  key: string;
  type: ChatItemType;
  text: string;
  timestamp?: number;
  it?: AsyncIterable<string>;
}
// 处理消息转换为渲染项
const convertMsgsToItems = (
  msgs: ChatMessage[],
  type: 'doll' | 'parent',
): RenderItemType[] => {
  const items: RenderItemType[] = [];

  for (let i = 0; i < msgs.length; i++) {
    const msg = msgs[i];
    const nextOne = msgs[i + 1];
    // answer
    if (msg.answer) {
      items.push({
        key: `a_${msg.id}`,
        type: type === 'doll' ? 'doll_answer' : 'parent_answer',
        text: msg.answer,
        timestamp: msg.id,
        it: (msg as ParentChatMessageWithIt)?.it,
      });
    }
    // shield
    if (msg.shield) {
      items.push({
        key: `s_${msg.id}`,
        type: 'shield',
        text: msg.shield,
        timestamp: msg.id,
      });
    }
    // query
    if (msg.query) {
      items.push({
        key: `q_${msg.id}`,
        type: type === 'doll' ? 'doll_query' : 'parent_query',
        text: msg.query,
        timestamp: msg.id,
      });
    }
    // 时间分割
    if (!nextOne || msg.timestamp - nextOne.timestamp > 10 * 60) {
      items.push({
        key: `time_${msg.id}`,
        type: 'time',
        text: formatTimeChatItem(msg.timestamp),
        timestamp: msg.id,
      });
    }
  }
  return items;
  // return items.reverse();
};

const ChatContentComponent: React.FC<
  ChatContentProps & {
    ref?: any;
  }
> = forwardRef(
  ({type, dollId, curTabType}: ChatContentProps & {ref?: any}, ref) => {
    const [renderItems, setRenderItems] = useState<RenderItemType[]>([]);
    const [loading, setLoading] = useState(false);
    const listRef = useRef<FlatList>(null);
    const timerRef = useRef<NodeJS.Timeout | null>(null);

    // 使用Jotai状态管理
    const [dollMsgHistory, setDollMsgHistory] = useDollMsgHistory();
    const [parentMsgHistory, setParentMsgHistory] = useParentMsgHistory();

    const onChatSSE = useCallback(
      (queryType: 'text' | 'voice', query: string) => {
        if (type === 'parent' && dollId) {
          setTieToChatInput();
        }
      },
      [dollId],
    );
    useImperativeHandle(
      ref,
      () => ({
        onChatSSE,
      }),
      [onChatSSE],
    );

    const fetchNewMessages = async () => {
      try {
        // setLoading(true);
        if (type === 'doll' && dollId && curTabType === 'doll') {
          const res = await ChatModel.getDollMsgPages(
            dollId,
            dollMsgHistory.msgList,
            PAGE_SIZE,
            'after',
          );
          if (res.msgs.length > 0) {
            setDollMsgHistory(prev => ({
              dollId,
              msgList: [...res.msgs, ...prev.msgList],
            }));
          }
        }
      } catch (error) {
        Logger.error(
          'ChatContentComponent',
          'Error in fetchNewMessages:',
          error,
        );
      } finally {
        // setLoading(false);
      }
    };
    const fetchPrevMessages = async () => {
      try {
        setLoading(true);
        if (type === 'doll' && curTabType === 'doll' && dollId) {
          const res = await ChatModel.getDollMsgPages(
            dollId,
            dollMsgHistory.msgList,
            PAGE_SIZE,
            'before',
          );
          setDollMsgHistory(prev => ({
            dollId,
            msgList: [...res.msgs, ...prev.msgList],
          }));
        } else if (type === 'parent' && curTabType === 'parent' && dollId) {
          const res = await ChatModel.getParentMsgPages(
            dollId,
            parentMsgHistory.msgList,
            PAGE_SIZE,
          );
          setParentMsgHistory(prev => ({
            dollId,
            msgList: [...res.msgs, ...prev.msgList],
          }));
        }
      } catch (error) {
        Logger.error(
          'ChatContentComponent',
          'Error in fetchPrevMessages:',
          error,
        );
      } finally {
        setLoading(false);
      }
    };

    const onLoadPrevMessages = () => {
      if (!loading) {
        fetchPrevMessages();
      }
    };

    useEffect(() => {
      // 设置定时刷新 (仅doll类型)
      if (type === 'doll' && curTabType === 'doll' && dollId) {
        timerRef.current = setInterval(() => {
          fetchNewMessages();
        }, 10000); // 每10秒获取一次新消息
      }
      return () => {
        if (timerRef.current) {
          clearInterval(timerRef.current);
          timerRef.current = null;
        }
      };
    }, [type, curTabType, dollId]);

    useEffect(() => {
      if (dollId) {
        if (type === 'doll' && curTabType === 'doll') {
          if (dollMsgHistory.dollId !== dollId) {
            setDollMsgHistory({
              dollId: dollId,
              msgList: [],
            });
          }
          if (dollMsgHistory.msgList.length === 0) {
            fetchPrevMessages();
          }
        } else if (type === 'parent' && curTabType === 'parent') {
          if (parentMsgHistory.dollId !== dollId) {
            setParentMsgHistory({
              dollId: dollId,
              msgList: [],
            });
          }
          if (parentMsgHistory.msgList.length === 0) {
            fetchPrevMessages();
          }
        }
      }
    }, [type, dollId, curTabType]);

    // 监听Jotai状态变化
    useEffect(() => {
      if (
        type === 'doll' &&
        curTabType === 'doll' &&
        dollMsgHistory &&
        dollMsgHistory.dollId === dollId
      ) {
        const items = convertMsgsToItems(dollMsgHistory.msgList, 'doll');
        setRenderItems(items);
      } else if (
        type === 'parent' &&
        curTabType === 'parent' &&
        parentMsgHistory &&
        parentMsgHistory.dollId === dollId
      ) {
        const items = convertMsgsToItems(parentMsgHistory.msgList, 'parent');
        setRenderItems(items);
      }
    }, [dollMsgHistory, parentMsgHistory, dollId, type, curTabType]);

    const onContentSizeChange = () => {};
    const setTieToChatInput = () => {
      listRef.current?.scrollToOffset({
        offset: 0,
        animated: false,
      });
    };

    const onEndReached = (info: {distanceFromEnd: number}) => {
      // if (info.distanceFromEnd < 0) {
      //   return;
      // }
      Logger.info('ChatContentComponent', 'onEndReached');
      onLoadPrevMessages();
      // setTieToEnd(false);
    };

    // 渲染
    return (
      <View style={styles.listContainer}>
        <FlatList
          ref={listRef}
          data={renderItems}
          renderItem={({item}) => (
            <ChatItem type={item.type} text={item.text} it={item.it} />
          )}
          keyExtractor={item => item.key}
          // onStartReached={onStartReached}
          // onStartReachedThreshold={0.2}
          onContentSizeChange={onContentSizeChange}
          // onLayout={onContentSizeChange}
          // onScrollBeginDrag={onScrollBeginDrag}
          onEndReached={onEndReached}
          // onStartReached={onStartReached}
          // refreshing={loading}
          // onRefresh={onLoadPrevMessages}
          // ListHeaderComponent={loading ? <ActivityIndicator /> : null}
          style={styles.list}
          contentContainerStyle={{paddingVertical: 16}}
          inverted // 让新消息在底部
        />
      </View>
    );
  },
);

// 样式
const styles = StyleSheet.create({
  listContainer: {
    flexShrink: 1,
  },
  list: {
    // flex: 1,
  },
});

export default ChatContentComponent;
