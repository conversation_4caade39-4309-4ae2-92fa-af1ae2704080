import React, {useEffect, useState} from 'react';
import {
  View,
  Image,
  StyleSheet,
  StyleProp,
  ViewStyle,
  ImageSourcePropType,
  ImageStyle,
  ActivityIndicator,
  LayoutChangeEvent,
} from 'react-native';

interface NineSliceImageProps {
  /**
   * Source of the image to be sliced
   */
  source: ImageSourcePropType;
  /**
   * Width of the component, if not provided will use layout width
   */
  width?: number;
  /**
   * Height of the component, if not provided will use layout height
   */
  height?: number;
  /**
   * Size of the left border that should not be stretched, 0 means no slicing
   */
  leftWidth?: number;
  /**
   * Size of the right border that should not be stretched, 0 means no slicing
   */
  rightWidth?: number;
  /**
   * Size of the top border that should not be stretched, 0 means no slicing
   */
  topHeight?: number;
  /**
   * Size of the bottom border that should not be stretched, 0 means no slicing
   */
  bottomHeight?: number;
  /**
   * Additional style for the container
   */
  style?: StyleProp<ViewStyle>;
  /**
   * Additional style for the image parts
   */
  imageStyle?: StyleProp<ImageStyle>;
}

/**
 * NineSliceImage component
 *
 * This component implements the 9-grid image slicing technique (also known as 9-patch or 9-slice scaling).
 * It divides an image into 9 parts where:
 * - The corners remain fixed
 * - The edges stretch in one dimension
 * - The center stretches in both dimensions
 */
const NineSliceImage: React.FC<NineSliceImageProps> = ({
  source,
  width: propWidth,
  height: propHeight,
  leftWidth = 0,
  rightWidth = 0,
  topHeight = 0,
  bottomHeight = 0,
  style,
  imageStyle,
}) => {
  const [layout, setLayout] = useState<{width: number; height: number} | null>(
    null,
  );
  const [loading, setLoading] = useState(true);
  const [imgSize, setImgSize] = useState<{
    width: number;
    height: number;
  } | null>(null);
  const [error, setError] = useState(false);

  // 获取图片原始尺寸
  useEffect(() => {
    if (typeof source === 'number') {
      const {width: imgWidth, height: imgHeight} =
        Image.resolveAssetSource(source);
      setImgSize({width: imgWidth, height: imgHeight});
      setLoading(false);
    } else if (
      source &&
      typeof source === 'object' &&
      'uri' in source &&
      source.uri
    ) {
      Image.getSize(
        source.uri,
        (imgWidth, imgHeight) => {
          setImgSize({width: imgWidth, height: imgHeight});
          setLoading(false);
        },
        () => {
          setError(true);
          setLoading(false);
        },
      );
    } else {
      setError(true);
      setLoading(false);
    }
  }, [source]);

  // 计算最终宽高，优先级：props > style > layout
  const handleLayout = (e: LayoutChangeEvent) => {
    if (!propWidth || !propHeight) {
      const {width: layoutWidth, height: layoutHeight} = e.nativeEvent.layout;
      setLayout({width: layoutWidth, height: layoutHeight});
    }
  };

  let width = propWidth;
  let height = propHeight;
  if ((!width || !height) && layout) {
    width = width || layout.width;
    height = height || layout.height;
  }

  // loading 或未获取到尺寸时
  if (loading || !imgSize) {
    return <ActivityIndicator size="small" />;
  }
  if (error || !width || !height) {
    return (
      <View
        style={[{width: propWidth, height: propHeight}, style]}
        onLayout={handleLayout}
      />
    );
  }

  // 计算九宫格各区域宽高
  const centerWidth = Math.max(width - leftWidth - rightWidth, 0);
  const centerHeight = Math.max(height - topHeight - bottomHeight, 0);

  // 判断哪些区域需要渲染
  const showTop = topHeight > 0;
  const showBottom = bottomHeight > 0;
  const showLeft = leftWidth > 0;
  const showRight = rightWidth > 0;

  // 如果四边都为0，直接拉伸整张图片
  if (!showTop && !showBottom && !showLeft && !showRight) {
    return (
      <View style={[{width, height}, style]} onLayout={handleLayout}>
        <Image
          source={source}
          style={[{width: '100%', height: '100%'}, imageStyle]}
          resizeMode="stretch"
        />
      </View>
    );
  }

  return (
    <View
      style={[{width, height, overflow: 'hidden'}, style]}
      onLayout={handleLayout}>
      {/* Top Row */}
      {showTop && (
        <View style={{flexDirection: 'row', height: topHeight}}>
          {showLeft && (
            <Image
              source={source}
              style={[
                {
                  width: leftWidth,
                  height: topHeight,
                },
                imageStyle,
              ]}
              resizeMode="cover"
            />
          )}
          <Image
            source={source}
            style={[
              {
                width: centerWidth,
                height: topHeight,
              },
              imageStyle,
            ]}
            resizeMode="stretch"
          />
          {showRight && (
            <Image
              source={source}
              style={[
                {
                  width: rightWidth,
                  height: topHeight,
                },
                imageStyle,
              ]}
              resizeMode="cover"
            />
          )}
        </View>
      )}

      {/* Middle Row */}
      <View style={{flexDirection: 'row', height: centerHeight}}>
        {showLeft && (
          <Image
            source={source}
            style={[
              {
                width: leftWidth,
                height: centerHeight,
              },
              imageStyle,
            ]}
            resizeMode="stretch"
          />
        )}
        <Image
          source={source}
          style={[
            {
              width: centerWidth,
              height: centerHeight,
            },
            imageStyle,
          ]}
          resizeMode="stretch"
        />
        {showRight && (
          <Image
            source={source}
            style={[
              {
                width: rightWidth,
                height: centerHeight,
              },
              imageStyle,
            ]}
            resizeMode="stretch"
          />
        )}
      </View>

      {/* Bottom Row */}
      {showBottom && (
        <View style={{flexDirection: 'row', height: bottomHeight}}>
          {showLeft && (
            <Image
              source={source}
              style={[
                {
                  width: leftWidth,
                  height: bottomHeight,
                },
                imageStyle,
              ]}
              resizeMode="cover"
            />
          )}
          <Image
            source={source}
            style={[
              {
                width: centerWidth,
                height: bottomHeight,
              },
              imageStyle,
            ]}
            resizeMode="stretch"
          />
          {showRight && (
            <Image
              source={source}
              style={[
                {
                  width: rightWidth,
                  height: bottomHeight,
                },
                imageStyle,
              ]}
              resizeMode="cover"
            />
          )}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  image: {
    overflow: 'hidden',
  },
});

export default NineSliceImage;
