import React from 'react';
import {
  Modal,
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TouchableWithoutFeedback,
  ImageBackground,
  Platform,
  Dimensions,
} from 'react-native';
import Icon from 'react-native-vector-icons/AntDesign';
import Clipboard from '@react-native-clipboard/clipboard';
import {colors, borderRadius} from '../theme/theme';
import {showToast} from '../utils/utils';
import AutoFitImage from './AutoFitImage';
import {MyText} from '../custom/CustomFont';

const {width} = Dimensions.get('window');
interface KnowledgeLinkModalProps {
  visible?: boolean;
  onClose?: () => void;
  content: string;
  link: string;
}

const KnowledgeLinkModal: React.FC<KnowledgeLinkModalProps> = ({
  visible = false,
  onClose = () => {},
  content,
  link,
}) => {
  // 点击内容自动复制
  const handleCopy = () => {
    Clipboard.setString(link);
    showToast('链接已复制');
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}>
      <TouchableWithoutFeedback onPress={onClose}>
        <View style={styles.overlay}>
          <TouchableWithoutFeedback onPress={() => {}}>
            <View style={styles.centered}>
              <AutoFitImage
                source={require('../assets/img/group_342_3x.png')}
              />
              <TouchableOpacity
                style={styles.closeBtn}
                onPress={onClose}
                hitSlop={{top: 10, right: 10, bottom: 10, left: 10}}>
                <Icon name="close" size={22} color="#888" />
              </TouchableOpacity>
              <TouchableOpacity
                style={{marginTop: 80}}
                onPress={handleCopy}
                activeOpacity={0.7}>
                <MyText style={styles.content}>{content}</MyText>
              </TouchableOpacity>
              <View style={styles.linkBox}>
                <MyText selectable style={styles.linkText}>
                  {link}
                </MyText>
              </View>
            </View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  centered: {
    width: width * 0.75,
    alignItems: 'center',
    justifyContent: 'center',
    height: width * 0.54,
  },
  closeBtn: {
    position: 'absolute',
    top: 12,
    right: 12,
    zIndex: 10,
    padding: 4,
  },
  content: {
    fontSize: 16,
    color: colors.textSecondary,
    fontWeight: '400',
    marginBottom: 18,
    textAlign: 'center',
    marginHorizontal: 20,
  },
  linkBox: {
    // backgroundColor: '#fff',
    borderRadius: borderRadius.md,
    padding: 12,
    marginTop: 0,
    width: '100%',
  },
  linkText: {
    fontSize: 14,
    color: colors.blue,
    textAlign: 'center',
  },
});

export default KnowledgeLinkModal;
