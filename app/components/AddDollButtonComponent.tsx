import React from 'react';
import {View, Text, StyleSheet, TouchableOpacity} from 'react-native';
import {colors} from '../theme/theme';
import useAppNavigation from '../hooks/useAppNavigation';
import {useSafeWndValue} from '../hooks/useSafeWnd';
import ScalableImage from './ScalableImage';
import {MyText} from '../custom/CustomFont';
import MyPrimaryButton from './MyPrimaryButton';

const AddDollButtonComponent: React.FC = () => {
  const navigation = useAppNavigation();
  const safeWnd = useSafeWndValue();

  const handleConnectDoll = () => {
    // 跳转到添加设备页面
    navigation.navigate('AddDoll');
  };

  return (
    <View style={styles.container}>
      <View style={styles.logoContainer}>
        <View
          style={{
            marginBottom: 120,
          }}>
          <ScalableImage
            source={require('../assets/img/group_260_3x.png')}
            width={safeWnd.width * 0.4}
          />
        </View>
      </View>
      <MyPrimaryButton onPress={handleConnectDoll}>
        <MyText style={styles.addBtnText}>+ 添加设备</MyText>
      </MyPrimaryButton>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
    alignItems: 'center',
    justifyContent: 'center',
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  logoPlaceholder: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  logoText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  appName: {
    marginTop: 16,
    fontSize: 22,
    fontWeight: 'bold',
    color: colors.text,
  },
  addBtn: {
    marginTop: 24,
    width: 200,
    height: 48,
    backgroundColor: '#f5f3ff',
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: colors.primary,
  },
  addBtnText: {
    color: colors.primary,
    fontSize: 18,
    fontWeight: 'bold',
  },
});

export default AddDollButtonComponent;
