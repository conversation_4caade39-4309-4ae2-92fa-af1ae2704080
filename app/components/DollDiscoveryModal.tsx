import React, {useEffect, useRef, useState} from 'react';
import {
  View,
  Modal,
  StyleSheet,
  Dimensions,
  Animated,
  TouchableOpacity,
  TouchableWithoutFeedback,
  Image,
  PanResponder,
} from 'react-native';
import {MyText} from '../custom/CustomFont';
import {colors, spacing, typography, borderRadius} from '../theme/theme';
import {dollService} from '../services/dollService';
import AutoFitImage from './AutoFitImage';
import MyPrimaryButton from './MyPrimaryButton';
import {useDiscoveredDevices} from '../hooks/useAppHooks';
import Logger from '../services/LoggerService';
import {showToast} from '../utils/utils';
import {useDollConnection} from '../hooks/useDollWiFi';

const {width: screenWidth, height: screenHeight} = Dimensions.get('window');

interface DeviceDiscoveryModalProps {
  visible: boolean;
  onCancel: () => void;
  onStartBinding: (bluetoothId: string) => void;
}

const DollDiscoveryModal: React.FC<DeviceDiscoveryModalProps> = ({
  visible,
  onCancel,
  onStartBinding,
}) => {
  const slideAnim = useRef(new Animated.Value(screenHeight)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;

  const [discoveredDevices] = useDiscoveredDevices();
  const [currentIndex, setCurrentIndex] = useState(0);
  const [bluetoothDevice, setBluetoothDevice] = useState(discoveredDevices[0]);
  const {connect, disconnect} = useDollConnection(bluetoothDevice?.id);

  const dollType = dollService.getDollTypeById(bluetoothDevice?.name || '');

  // 滑动相关的动画值
  const translateX = useRef(new Animated.Value(0)).current;
  const swipeThreshold = screenWidth * 0.3; // 滑动阈值

  // 创建PanResponder处理滑动手势
  const panResponder = useRef(
    PanResponder.create({
      onMoveShouldSetPanResponder: (evt, gestureState) => {
        return (
          Math.abs(gestureState.dx) > Math.abs(gestureState.dy) &&
          Math.abs(gestureState.dx) > 10
        );
      },
      onPanResponderGrant: () => {
        translateX.setOffset((translateX as any)._value);
        translateX.setValue(0);
      },
      onPanResponderMove: (evt, gestureState) => {
        translateX.setValue(gestureState.dx);
      },
      onPanResponderRelease: (evt, gestureState) => {
        translateX.flattenOffset();

        if (gestureState.dx > swipeThreshold && currentIndex > 0) {
          // 向右滑动，切换到上一个设备
          goToPrevious();
        } else if (
          gestureState.dx < -swipeThreshold &&
          currentIndex < discoveredDevices.length - 1
        ) {
          // 向左滑动，切换到下一个设备
          goToNext();
        } else {
          // 回弹到原位置
          Animated.spring(translateX, {
            toValue: 0,
            useNativeDriver: true,
          }).start();
        }
      },
    }),
  ).current;

  // 切换到下一个设备
  const goToNext = () => {
    if (currentIndex < discoveredDevices.length - 1) {
      const newIndex = currentIndex + 1;
      setCurrentIndex(newIndex);
      setBluetoothDevice(discoveredDevices[newIndex]);

      Animated.spring(translateX, {
        toValue: 0,
        useNativeDriver: true,
      }).start();
    }
  };

  // 切换到上一个设备
  const goToPrevious = () => {
    if (currentIndex > 0) {
      const newIndex = currentIndex - 1;
      setCurrentIndex(newIndex);
      setBluetoothDevice(discoveredDevices[newIndex]);

      Animated.spring(translateX, {
        toValue: 0,
        useNativeDriver: true,
      }).start();
    }
  };

  useEffect(() => {
    if (visible) {
      // 显示动画
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      // 隐藏动画
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: screenHeight,
          duration: 250,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 250,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [visible, slideAnim, fadeAnim]);

  const handleBackdropPress = () => {
    onCancel();
  };

  const connectBle = async () => {
    try {
      await connect();
    } catch (error) {
      throw error;
    }
  };

  const handleStartBinding = async () => {
    try {
      // 显示连接状态
      // showToast('正在连接设备...');

      // 连接蓝牙设备
      await connectBle();

      // 等待连接稳定
      await new Promise(resolve => setTimeout(resolve, 1500));
      // 设备已通过 DollBleService 的过滤器验证，无需额外验证

      // 连接成功，进入WiFi配置
      Logger.info('蓝牙连接成功，准备进入WiFi配置', {bluetoothDevice});
      // showToast('设备连接成功');
      onStartBinding(bluetoothDevice.name || '');
    } catch (error) {
      // Logger.error('连接蓝牙失败', {bluetoothDevice, error});
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      showToast(`连接失败: ${errorMessage}`);
      // 不关闭弹窗，让用户可以重试
    }
  };

  useEffect(() => {
    if (discoveredDevices.length === 0) {
      onCancel();
    } else {
      // 如果当前设备不在列表中，重置到第一个设备
      if (
        bluetoothDevice &&
        discoveredDevices.map(d => d.id).indexOf(bluetoothDevice.id) < 0
      ) {
        setCurrentIndex(0);
        setBluetoothDevice(discoveredDevices[0]);
      } else if (!bluetoothDevice && discoveredDevices.length > 0) {
        // 如果没有选中设备但有可用设备，选择第一个
        setCurrentIndex(0);
        setBluetoothDevice(discoveredDevices[0]);
      } else {
        // 更新当前索引以匹配当前选中的设备
        const index = discoveredDevices
          .map(d => d.id)
          .indexOf(bluetoothDevice.id);
        if (index >= 0) {
          setCurrentIndex(index);
        }
      }
    }
  }, [discoveredDevices]);

  // 获取玩偶头像
  const dollAvatar = dollService.getDollAvatar(dollType);

  return (
    <Modal
      transparent
      visible={visible}
      animationType="none"
      statusBarTranslucent
      onRequestClose={onCancel}>
      <TouchableWithoutFeedback onPress={handleBackdropPress}>
        <View style={styles.overlay}>
          <Animated.View
            style={[
              styles.backdrop,
              {
                opacity: fadeAnim,
              },
            ]}
          />
        </View>
      </TouchableWithoutFeedback>

      <Animated.View
        style={[
          styles.modalContainer,
          {
            transform: [{translateY: slideAnim}],
          },
        ]}>
        <TouchableWithoutFeedback>
          <View style={styles.modalContent}>
            <AutoFitImage
              source={require('../assets/img/modal_bg_3.png')}
              // style={styles.backgroundImage}
              resizeMode="stretch"
            />
            <View style={styles.header}>
              <MyText style={styles.title}>发现设备</MyText>
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={onCancel}
                activeOpacity={0.7}>
                <MyText style={styles.cancelText}>取消</MyText>
              </TouchableOpacity>
            </View>

            {/* 显示蓝牙设备信息 */}
            {bluetoothDevice && (
              <View style={styles.deviceInfo}>
                <MyText style={styles.deviceInfoText}>
                  设备ID:{' '}
                  {`${bluetoothDevice.name || ''}[${bluetoothDevice.id}]`}
                </MyText>
              </View>
            )}

            {/* 内容区域 */}
            <View style={styles.content}>
              {/* 玩偶图片容器 */}
              <Animated.View
                style={[
                  styles.dollImageContainer,
                  {
                    transform: [{translateX: translateX}],
                  },
                ]}
                {...panResponder.panHandlers}>
                <View style={styles.dollImageWrapper}>
                  <Image source={dollAvatar} style={styles.dollImage} />
                </View>
              </Animated.View>

              {/* 圆点指示器 - 只在有多个设备时显示 */}
              {discoveredDevices.length > 1 && (
                <View style={styles.dotsContainer}>
                  {discoveredDevices.map((_, index) => (
                    <View
                      key={index}
                      style={[
                        styles.dot,
                        index === currentIndex && styles.activeDot,
                      ]}
                    />
                  ))}
                </View>
              )}

              {/* 页面指示文字 */}
              {discoveredDevices.length > 1 && (
                <View style={styles.pageIndicator}>
                  <MyText style={styles.pageIndicatorText}>
                    {currentIndex + 1} / {discoveredDevices.length}
                  </MyText>
                  <MyText style={styles.swipeHintText}>左右滑动切换设备</MyText>
                </View>
              )}
            </View>

            {/* 底部按钮区域 */}
            <View style={styles.footer}>
              <MyPrimaryButton
                style={styles.bindButton}
                onPress={handleStartBinding}>
                <MyText style={styles.bindButtonText}>开始绑定</MyText>
              </MyPrimaryButton>
            </View>
          </View>
        </TouchableWithoutFeedback>
      </Animated.View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  backdrop: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    justifyContent: 'flex-end',
  },
  modalContent: {
    width: screenWidth,
    height: 560, // 占屏幕高度的75%
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.xl,
    paddingBottom: spacing.md,
  },
  title: {
    fontSize: typography.fontSize.xl,
    fontWeight: '500' as const,
    color: colors.text,
  },
  cancelButton: {
    padding: spacing.xs,
  },
  cancelText: {
    fontSize: typography.fontSize.md,
    color: colors.textSecondary,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
  },
  dollImageContainer: {
    alignItems: 'center',
    marginBottom: spacing.xl,
  },
  dollImageWrapper: {
    width: 200,
    height: 240,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: borderRadius.lg,
    borderWidth: 2,
    borderColor: 'rgba(115, 54, 246, 0.2)',
    borderStyle: 'dashed',
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
  },
  dollImage: {
    width: 160,
    height: 200,
    resizeMode: 'contain',
  },
  dotsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: spacing.sm,
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: colors.disabled,
  },
  activeDot: {
    backgroundColor: colors.primary,
    width: 24,
    borderRadius: 12,
  },
  footer: {
    paddingBottom: spacing.lg * 1.5,
    // alignItems: 'center',
  },
  bindButton: {
    paddingHorizontal: 0,
    marginHorizontal: 30,
    borderRadius: borderRadius.lg,
  },
  bindButtonText: {
    fontSize: typography.fontSize.lg,
    fontWeight: 'bold',
    color: colors.primary,
  },
  deviceInfo: {
    paddingHorizontal: spacing.lg,
    paddingBottom: spacing.sm,
  },
  deviceInfoText: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
    textAlign: 'center',
  },
  pageIndicator: {
    marginTop: spacing.sm,
    alignItems: 'center',
  },
  pageIndicatorText: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
  },
  swipeHintText: {
    fontSize: typography.fontSize.xs,
    color: colors.textSecondary,
    marginTop: spacing.xs,
    opacity: 0.7,
  },
});

export default DollDiscoveryModal;
