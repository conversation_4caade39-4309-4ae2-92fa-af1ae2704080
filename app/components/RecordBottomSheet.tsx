import React, {useEffect, useState, useRef} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import AudioRecorderPlayer from 'react-native-audio-recorder-player';
import RNFS from 'react-native-fs';
import {colors} from '../theme/theme';
import {ImageBackground} from 'react-native';
import {MyText} from '../custom/CustomFont';

const {width: SCREEN_WIDTH} = Dimensions.get('window');

interface RecordBottomSheetProps {
  visible: boolean;
  onClose: () => void;
  onRecordComplete: (filePath: string) => void;
  promptText?: string;
}

const RecordBottomSheet: React.FC<RecordBottomSheetProps> = ({
  visible,
  onClose,
  onRecordComplete,
  promptText = '你说的没错呀，我周末也去看了这部电影可以称得上是五年内最感人的电影了。',
}) => {
  // 状态: idle-等待录音, recording-录音中, completed-录音完成
  const [recordState, setRecordState] = useState<
    'idle' | 'recording' | 'completed'
  >('idle');
  const [recordedFilePath, setRecordedFilePath] = useState<string | null>(null);
  const [recordSeconds, setRecordSeconds] = useState(0);
  const [recordingLevels, setRecordingLevels] = useState<number[]>(
    Array(20).fill(1), // 初始波形高度
  );
  const [titleText, setTitleText] = useState('请朗读');

  const audioRecorder = useRef(new AudioRecorderPlayer());
  const recordPathRef = useRef(
    `${RNFS.CachesDirectoryPath}/record_${Date.now()}.m4a`,
  );

  useEffect(() => {
    // 重置状态
    if (visible) {
      setRecordState('idle');
      setRecordedFilePath(null);
      setRecordSeconds(0);
      // 生成新的录音文件路径
      recordPathRef.current = `${RNFS.CachesDirectoryPath}/record_${Date.now()}.m4a`;
    } else {
      // 停止录音
      stopRecording();
    }

    return () => {
      stopRecording();
    };
  }, [visible]);

  // 模拟录音波形
  useEffect(() => {
    let interval: NodeJS.Timeout | null = null;

    if (recordState === 'recording') {
      interval = setInterval(() => {
        // 生成随机波形高度
        setRecordingLevels(
          Array(20)
            .fill(0)
            .map(() => Math.random() * 10 + 1),
        );
      }, 150);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [recordState]);

  const startRecording = async () => {
    try {
      setRecordState('recording');
      await audioRecorder.current.startRecorder(recordPathRef.current);

      audioRecorder.current.addRecordBackListener(e => {
        setRecordSeconds(e.currentPosition / 1000);
      });

      // 3秒后自动停止录音
      setTimeout(() => {
        stopRecording();
      }, 3000);
    } catch (err) {
      console.error('Failed to start recording', err);
      setRecordState('idle');
    }
  };

  const stopRecording = async () => {
    if (recordState === 'recording') {
      try {
        const result = await audioRecorder.current.stopRecorder();
        audioRecorder.current.removeRecordBackListener();
        setRecordedFilePath(result);
        setRecordState('completed');
      } catch (err) {
        console.error('Failed to stop recording', err);
      }
    }
  };

  const handleSubmit = () => {
    if (recordedFilePath) {
      onRecordComplete(recordedFilePath);
    }
  };

  const renderBottom = () => {
    switch (recordState) {
      case 'recording':
        return (
          <>
            <View style={styles.waveformContainer}>
              {recordingLevels.map((level, index) => (
                <View
                  key={index}
                  style={[
                    styles.waveformBar,
                    {
                      height: level * 2,
                      backgroundColor: index % 4 === 0 ? '#A48AFF' : '#D9D0FF',
                    },
                  ]}
                />
              ))}
            </View>
            <MyText style={styles.recordingStatus}>松手完成</MyText>
          </>
        );

      case 'completed':
        return (
          <>
            <TouchableOpacity style={styles.button} onPress={handleSubmit}>
              <MyText style={styles.buttonText}>按住录制</MyText>
            </TouchableOpacity>
          </>
        );

      default: // idle
        return (
          <>
            <TouchableOpacity
              style={styles.button}
              onPress={startRecording}
              activeOpacity={0.7}>
              <MyText style={styles.buttonText}>按住录制</MyText>
            </TouchableOpacity>
          </>
        );
    }
  };

  return (
    <Modal
      visible={visible}
      transparent
      // animationType="slide"
      onRequestClose={onClose}>
      <View style={styles.container}>
        <ImageBackground
          source={require('../assets/img/mask_group_3x_11_.png')}
          style={styles.content}>
          <View style={styles.titleContainer}>
            <MyText style={styles.title}>{titleText}</MyText>
            <TouchableOpacity style={styles.cancelButton} onPress={onClose}>
              <MyText style={styles.cancelText}>取消</MyText>
            </TouchableOpacity>
          </View>
          <MyText style={styles.promptText}>{promptText}</MyText>
          <View style={styles.bottomContainer}>{renderBottom()}</View>
        </ImageBackground>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
  },
  content: {
    height: SCREEN_WIDTH,
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 24,
    paddingTop: 30,
    paddingBottom: 40,
    alignItems: 'center',
  },
  titleContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    width: SCREEN_WIDTH * 0.8,
    marginBottom: 38,
  },
  bottomContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 18,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.text,
  },
  promptText: {
    fontSize: 16,
    color: '#333',
    textAlign: 'left',
    marginBottom: 32,
    lineHeight: 24,
    paddingHorizontal: 20,
    flex: 1,
  },
  button: {
    backgroundColor: '#E6E0FF',
    paddingVertical: 14,
    paddingHorizontal: 10,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    width: SCREEN_WIDTH * 0.8,
    borderWidth: 1,
    borderColor: colors.textSecondary,
  },
  buttonText: {
    color: colors.primary,
    fontSize: 16,
    fontWeight: '600',
  },
  cancelButton: {
    position: 'absolute',
    right: 0,
    padding: 10,
  },
  cancelText: {
    color: colors.textSecondary,
    fontSize: 16,
  },
  waveformContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 40,
    width: SCREEN_WIDTH * 0.8,
    marginBottom: 24,
    marginTop: 10,
  },
  waveformBar: {
    width: 4,
    borderRadius: 2,
    marginHorizontal: 3,
  },
  recordingStatus: {
    color: '#B6B6D4',
    marginTop: 10,
  },
});

export default RecordBottomSheet;
