import { atom } from 'jotai';

// 用户相关状态
export const userAtom = atom({
  id: '',
  name: '',
  email: '',
  isLoggedIn: false,
});

// 应用设置相关状态
export const appSettingsAtom = atom({
  theme: 'light',
  notifications: true,
  language: 'zh',
});

// 加载状态
export const loadingAtom = atom(false);

// 简单的原子操作示例
export const counterAtom = atom(0);
export const incrementCounterAtom = atom(
  (get) => get(counterAtom),
  (get, set) => set(counterAtom, get(counterAtom) + 1)
); 