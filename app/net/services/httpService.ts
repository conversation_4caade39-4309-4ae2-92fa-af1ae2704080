import HttpClient, {SSEConnection} from '../api/http';
import apiConfig from '../api/config';
import {
  BaseResponse,
  VerifyCodeRequest,
  VerifyCodeResponse,
  VerifyCodeAuthRequest,
  TokenResponse,
  DollBindRequest,
  DollInfosResponse,
  DollStateRequest,
  DollStateResponse,
  RoleRequest,
  RolesResponse,
  RoleChangeRequest,
  RoleDeleteRequest,
  RoleDeleteResponse,
  RoleGenRequest,
  RoleGenResponse,
  RoleUpsertRequest,
  RoleUpsertResponse,
  DollMsgRequest,
  MsgResponse,
  MsgSummaryRequest,
  MsgSummaryResponse,
  ParentChatRequest,
  UrlResponse,
  TimbreListResponse,
  TimbreDeleteRequest,
  TimbreDeleteResponse,
  TimbreRecordRequest,
  TimbreRecordResponse,
  ParentMsgRequest,
  ChatMessage,
  ParentChatResponse,
} from '../types';
import {saveUserInfo, clearUserInfo, getUserInfo} from './authTokenService';

// 创建HTTP客户端实例
const httpClient = new HttpClient(apiConfig);

// 用户认证相关
const loginService = {
  /**
   * 获取验证码
   */
  getVerifyCode: async (
    params: VerifyCodeRequest,
  ): Promise<BaseResponse<VerifyCodeResponse>> => {
    // httpClient.setAuthRequired(false);
    return httpClient.post<BaseResponse<VerifyCodeResponse>>(
      'app/login/get-verifycode',
      params,
    );
  },

  /**
   * 验证码认证
   */
  verifyCode: async (
    params: VerifyCodeAuthRequest,
  ): Promise<BaseResponse<TokenResponse>> => {
    // httpClient.setAuthRequired(false);
    const response = await httpClient.post<BaseResponse<TokenResponse>>(
      'app/login/verify-verifycode',
      params,
    );

    // 保存用户信息
    if (response.code === 0) {
      await saveUserInfo({
        area: params.area,
        phone: params.phone,
        token: response.data.token,
        expiredTime: response.data.expiredTime,
      });
    }

    return response;
  },

  /**
   * 刷新token
   */
  refreshToken: async (): Promise<BaseResponse<TokenResponse>> => {
    const response = await httpClient.post<BaseResponse<TokenResponse>>(
      'app/refresh-token',
      {},
    );

    // 更新用户token
    if (response.code === 0) {
      const userInfo = await getUserInfo();
      if (userInfo) {
        await saveUserInfo({
          ...userInfo,
          token: response.data.token,
          expiredTime: response.data.expiredTime,
        });
      }
    }

    return response;
  },
};

// 玩偶相关服务
const dollService = {
  /**
   * 绑定小玩偶
   */
  bindDoll: async (params: DollBindRequest): Promise<BaseResponse<{}>> => {
    return httpClient.post<BaseResponse<{}>>('app/doll/bind', params);
  },

  /**
   * 获取小玩偶设备详情列表
   */
  getDollInfos: async (): Promise<BaseResponse<DollInfosResponse>> => {
    return httpClient.post<BaseResponse<DollInfosResponse>>(
      'app/doll/infos',
      {},
    );
  },

  /**
   * 获取玩偶硬件状态
   */
  getDollState: async (
    params: DollStateRequest,
  ): Promise<BaseResponse<DollStateResponse>> => {
    return httpClient.post<BaseResponse<DollStateResponse>>(
      'app/doll/get-state',
      params,
    );
  },
};

// 角色相关服务
const roleService = {
  /**
   * 获取小玩偶角色
   */
  getRoles: async (
    params: RoleRequest,
  ): Promise<BaseResponse<RolesResponse>> => {
    return httpClient.post<BaseResponse<RolesResponse>>(
      'app/role/get-role',
      params,
    );
  },

  /**
   * 角色切换
   */
  changeRole: async (params: RoleChangeRequest): Promise<BaseResponse<{}>> => {
    return httpClient.post<BaseResponse<{}>>('app/role/change', params);
  },

  /**
   * 角色删除
   */
  deleteRole: async (
    params: RoleDeleteRequest,
  ): Promise<BaseResponse<RoleDeleteResponse>> => {
    return httpClient.post<BaseResponse<RoleDeleteResponse>>(
      'app/role/delete',
      params,
    );
  },

  /**
   * 自定义角色一键生成
   */
  genRole: async (
    params: RoleGenRequest,
  ): Promise<BaseResponse<RoleGenResponse>> => {
    return httpClient.post<BaseResponse<RoleGenResponse>>(
      'app/role/gen',
      params,
    );
  },

  /**
   * 自定义角色创建/修改
   */
  upsertRole: async (
    params: RoleUpsertRequest,
  ): Promise<BaseResponse<RoleUpsertResponse>> => {
    return httpClient.post<BaseResponse<RoleUpsertResponse>>(
      'app/role/upsert',
      params,
    );
  },
};

// 消息相关服务
const msgService = {
  /**
   * 获取小玩偶设备聊天内容
   */
  getDollMsg: async (
    params: DollMsgRequest,
  ): Promise<BaseResponse<MsgResponse>> => {
    return httpClient.post<BaseResponse<MsgResponse>>(
      'app/msg/get-doll',
      params,
    );
  },

  /**
   * 获取家长聊天信息
   */
  getParentMsg: async (
    params: ParentMsgRequest,
  ): Promise<BaseResponse<MsgResponse>> => {
    return httpClient.post<BaseResponse<MsgResponse>>(
      'app/msg/get-parent',
      params,
    );
  },

  /**
   * 家长助手文字聊天（SSE实现）
   */
  chatParentSSE: async (
    params: ParentChatRequest,
  ): Promise<SSEConnection<BaseResponse<ParentChatResponse>>> => {
    const keys = Object.keys(params);
    const query = keys
      .map(
        key =>
          `${key}=${encodeURIComponent(params[key as keyof ParentChatRequest] as string)}`,
      )
      .join('&');
    const url = `app/msg/chat-parent-sse?${query}`;
    return httpClient.sse<BaseResponse<ParentChatResponse>>(url, {
      method: 'GET',
    });
  },
  /**
   * 获取聊天内容总结
   */
  getMsgSummary: async (
    params: MsgSummaryRequest,
  ): Promise<BaseResponse<MsgSummaryResponse>> => {
    return httpClient.post<BaseResponse<MsgSummaryResponse>>(
      'app/msg/get-summary',
      params,
    );
  },
};

// 家长助手服务
const assistantService = {};

// 设置相关服务
const settingService = {
  /**
   * 获取订阅URL
   */
  getSubscribeUrl: async (): Promise<BaseResponse<UrlResponse>> => {
    return httpClient.post<BaseResponse<UrlResponse>>(
      'app/setting/subscribe',
      {},
    );
  },

  /**
   * 获取知识库URL
   */
  getRepositoryUrl: async (): Promise<BaseResponse<UrlResponse>> => {
    return httpClient.post<BaseResponse<UrlResponse>>(
      'app/setting/repository',
      {},
    );
  },

  /**
   * 退出登录
   */
  logout: async (): Promise<void> => {
    // const response = await httpClient.post<BaseResponse<{}>>(
    //   'app/setting/quit-account',
    //   {},
    // );
    // if (response.code === 0) {
    //   await clearUserInfo();
    // }
    // return response;
    return clearUserInfo();
  },

  /**
   * 注销登录
   */
  deleteAccount: async (): Promise<BaseResponse<{}>> => {
    const response = await httpClient.post<BaseResponse<{}>>(
      'app/setting/delete-account',
      {},
    );
    if (response.code === 0) {
      await clearUserInfo();
    }
    return response;
  },
};

// 音色相关服务
const timbreService = {
  /**
   * 音色列表
   */
  getTimbreList: async (): Promise<BaseResponse<TimbreListResponse>> => {
    return httpClient.post<BaseResponse<TimbreListResponse>>(
      'app/timbre/list',
      {},
    );
  },

  /**
   * 删除音色
   */
  deleteTimbre: async (
    params: TimbreDeleteRequest,
  ): Promise<BaseResponse<TimbreDeleteResponse>> => {
    return httpClient.post<BaseResponse<TimbreDeleteResponse>>(
      'app/timbre/delete',
      params,
    );
  },

  /**
   * 音色录制
   */
  recordTimbre: async (
    params: TimbreRecordRequest,
  ): Promise<BaseResponse<TimbreRecordResponse>> => {
    return httpClient.post<BaseResponse<TimbreRecordResponse>>(
      'app/timbre/record',
      params,
    );
  },
};

// 导出统一的HTTP服务
const httpService = {
  login: loginService,
  doll: dollService,
  role: roleService,
  msg: msgService,
  assistant: assistantService,
  setting: settingService,
  timbre: timbreService,
};

export default httpService;
