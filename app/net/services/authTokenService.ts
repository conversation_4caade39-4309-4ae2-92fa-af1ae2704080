import {UserInfo} from '../types';
import storageService from '../../services/storageService';

// 用户信息存储键
// const USER_TOKEN_KEY = 'user_token';
const USER_INFO_KEY = 'user_info';

// 内存中的用户信息缓存
let userInfo: UserInfo | null = null;

/**
 * 保存用户信息
 */
export const saveUserInfo = async (info: UserInfo): Promise<void> => {
  if (!info) {
    return;
  }
  userInfo = info;
  userInfo.expiredAt =
    Date.now() + (info.expiredTime || 10 * 24 * 60 * 60) * 1000;
  try {
    // 将用户信息保存到AsyncStorage
    await storageService.setObject<UserInfo>(USER_INFO_KEY, userInfo);
    console.log('用户信息已保存', userInfo);
  } catch (error) {
    console.error('保存用户信息失败:', error);
  }
};

/**
 * 获取用户信息
 */
export const getUserInfo = async (): Promise<UserInfo | null> => {
  if (userInfo) {
    return userInfo;
  }

  try {
    // 从AsyncStorage获取用户信息
    const storedInfo = await storageService.getObject<UserInfo>(USER_INFO_KEY);
    if (storedInfo) {
      userInfo = storedInfo;
      return storedInfo;
    }
    return null;
  } catch (error) {
    console.error('获取用户信息失败:', error);
    return null;
  }
};

/**
 * 获取用户Token
 */
export const getUserToken = async (): Promise<string | null> => {
  const info = await getUserInfo();
  return info?.token || null;
};

/**
 * 清除用户信息（退出登录）
 */
export const clearUserInfo = async (): Promise<void> => {
  userInfo = null;

  try {
    // 从AsyncStorage中清除用户信息
    await storageService.removeItem(USER_INFO_KEY);
    console.log('用户信息已清除');
  } catch (error) {
    console.error('清除用户信息失败:', error);
  }
};

/**
 * 检查token是否过期
 */
export const isTokenValid = async (): Promise<boolean> => {
  const info = await getUserInfo();
  if (!info || !info.token) {
    console.log('token info: ', info);
    return false;
  }

  // const now = Math.floor(Date.now() / 1000); // 当前时间戳（秒）
  // const expiredAt = info.expiredAt; // token过期时间

  // const tokenValid = now < expiredAt;
  // console.log('token expire: ', token
  return true;
};

export default {
  saveUserInfo,
  getUserInfo,
  getUserToken,
  clearUserInfo,
  isTokenValid,
};
