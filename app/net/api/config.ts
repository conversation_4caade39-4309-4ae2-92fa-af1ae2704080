export interface ApiConfig {
  baseURL: string;
  baseURLiOS?: string;
  timeout: number;
}

// // 生产API配置
// const prodApiConfig: ApiConfig = {
//   baseURL: 'https://app.artificial-productivity.com', // 请替换为实际的API地址
//   timeout: 10000, // 超时时间，单位毫秒
// };

// 开发API配置（本地Mock服务器）
const apiConfig: ApiConfig = {
  // baseURL: 'http://192.168.50.46:3001', // 本地Mock服务器地址
  // baseURL: 'https://app.artificial-productivity.com', // 请替换为实际的API地址
  baseURL: 'http://115.190.73.213:8912', //
  // baseURL: 'https://ap.fifthday.bid',
  timeout: 10000,
};

// 根据环境选择配置
const isDev = process.env.NODE_ENV !== 'production';
// const apiConfig: ApiConfig = isDev ? devApiConfig : prodApiConfig;

export default apiConfig;
