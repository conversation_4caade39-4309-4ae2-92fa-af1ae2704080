import axios, {AxiosInstance, AxiosRequestConfig} from 'axios';
import authTokenService, {getUserToken} from '../services/authTokenService';
import {navigate, navigationRef} from '../../navigation/navigationUtils';
import Logger from '../../services/LoggerService';
import {showToast} from '../../utils/utils';
import EventSource from 'react-native-sse';
import {Platform} from 'react-native';
import {ApiConfig} from './config';

export interface SSEConnection<T = any> {
  eventSource: EventSource;
  iterator: AsyncGenerator<T>;
}

class HttpClient {
  private instance: AxiosInstance;
  private isAuthRequired: boolean = true;

  constructor(config: ApiConfig) {
    const baseURL =
      Platform.OS === 'ios'
        ? config.baseURLiOS || config.baseURL
        : config.baseURL;
    this.instance = axios.create({
      baseURL,
      timeout: config.timeout || 10000, // 默认超时时间为10秒
    });

    // 请求拦截器
    this.instance.interceptors.request.use(
      async config => {
        // 除了验证码相关的接口，其他接口都需要带上token
        if (this.isAuthRequired && !config.url?.includes('/login/')) {
          const token = await getUserToken();
          if (token) {
            config.headers.Authorization = `Bearer ${token}`;
          }
        }
        return config;
      },
      error => {
        return Promise.reject(error);
      },
    );

    // 响应拦截器
    this.instance.interceptors.response.use(
      response => {
        return response.data;
      },
      error => {
        // 处理错误
        if (error.response) {
          // 服务器返回错误
          Logger.error('服务器响应错误:', error.response.data);
          if (
            error.response?.status === 401 ||
            error.response?.data?.code === 401
          ) {
            // 401错误码表示未授权，需要重新登录
            authTokenService.clearUserInfo();
            navigationRef.current?.navigate('LoginRegister');
            showToast('登录已过期，请重新登录');
          } else {
            if (error.response?.data?.message) {
              showToast(error.response.data.message);
            } else {
              showToast('网络请求错误');
            }
          }
        } else if (error.request) {
          // 请求发送成功，但没有收到响应
          Logger.error('网络请求错误:', error.request);
          showToast('网络请求错误' + error.message || '');
        } else {
          // 设置请求时发生错误
          Logger.error('请求错误:', error.message);
          showToast('请求错误' + error.message || '');
        }
        return Promise.reject(error);
      },
    );
  }

  // 设置是否需要认证
  setAuthRequired(isRequired: boolean): void {
    this.isAuthRequired = isRequired;
  }

  // GET请求
  async get<T>(
    url: string,
    params?: any,
    config?: AxiosRequestConfig,
  ): Promise<T> {
    const res = await this.instance.get(url, {params, ...config});
    return res as unknown as T;
  }

  // POST请求
  async post<T>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig,
  ): Promise<T> {
    Logger.debug('HttpClient', 'post method called', url, data, config);
    const res = await this.instance.post(url, data, config);
    Logger.debug('HttpClient', 'post method called response:', res);
    return res as unknown as T;
  }

  // SSE连接
  async sse<T = any>(
    url: string,
    options: Record<string, any> = {},
  ): Promise<SSEConnection<T>> {
    // 处理URL，确保它是一个完整的URL
    const fullUrl = url.startsWith('http')
      ? url
      : `${this.instance.defaults.baseURL}/${url}`;

    // 处理认证逻辑，与请求拦截器保持一致
    const headers: Record<string, string> = options.headers || {};
    if (this.isAuthRequired && !url.includes('/login/')) {
      const token = await getUserToken();
      if (token) {
        headers.Authorization = `Bearer ${token}`;
      }
    }

    Logger.info('HttpClient', 'sse method called', fullUrl);
    // 创建EventSource实例
    const eventSource = new EventSource(fullUrl, {
      ...options,
      headers,
    });

    eventSource.addEventListener('open', event => {
      Logger.info('Open SSE connection.');
    });

    // 处理错误，与响应拦截器保持一致
    eventSource.addEventListener('error', event => {
      Logger.error('SSE连接错误:', event);
      // 自定义错误处理
      // EventSource的error事件没有status属性，这里需要通过其他方式判断认证失败
      // 假设我们在连接断开时检查token有效性
      const checkAuthFailure = async () => {
        const token = await getUserToken();
        if (!token) {
          authTokenService.clearUserInfo();
          navigationRef.current?.navigate('LoginRegister');
          showToast('登录已过期，请重新登录');
        } else {
          showToast('SSE连接错误');
        }
      };
      checkAuthFailure();
    });

    let resolveNext: (() => void) | null;
    const waitForNext = () => {
      return new Promise<void>(resolve => {
        resolveNext = resolve;
      });
    };
    // 创建迭代器
    const messageQueue: IteratorResult<T>[] = [];

    const messageHandler = (event: any) => {
      Logger.info('SSE消息:', event);
      try {
        const data = JSON.parse(event.data) as T;
        messageQueue.push({value: data, done: false});
        if (resolveNext) {
          resolveNext();
          resolveNext = null;
        }
      } catch (e) {
        Logger.error('SSE消息解析错误:', e);
      }
    };

    const errorHandler = (event: any) => {
      Logger.error('SSE连接错误:', event);
      messageQueue.push({value: undefined as any, done: true});
      if (resolveNext) {
        resolveNext();
        resolveNext = null;
      }
    };

    eventSource.addEventListener('message', messageHandler);
    eventSource.addEventListener('error', errorHandler);

    const gen = async function* () {
      while (true) {
        if (messageQueue.length === 0) {
          await waitForNext();
        }
        const data = messageQueue.shift()!;
        if (data.done) {
          Logger.info('SSE连接关闭');
          eventSource.removeEventListener('message', messageHandler);
          eventSource.removeEventListener('error', errorHandler);
          eventSource.close();
          break;
        }
        yield data.value;
      }
    };
    const iterator = gen();
    return {iterator, eventSource};
  }
}

// 默认导出实例
export default HttpClient;
