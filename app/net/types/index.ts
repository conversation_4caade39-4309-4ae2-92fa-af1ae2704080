// 基础响应类型
export interface BaseResponse<T> {
  code: number;
  message: string;
  data: T;
}

// 用户相关类型
export interface VerifyCodeRequest {
  area: string;
  phone: string;
}

export interface VerifyCodeResponse {
  verifycode: string;
}

export interface VerifyCodeAuthRequest {
  area: string;
  phone: string;
  verifycode: string;
}

export interface TokenResponse {
  token: string;
  expiredTime: number;
}

// 用户信息类型
export interface UserInfo {
  area: string;
  phone: string;
  token: string;
  expiredTime: number;
  expiredAt?: number;
}

// 玩偶相关类型
export interface DollBindRequest {
  dollId: string;
}

export interface DollRole {
  roleId: string;
  roleContent: string;
  timbreId: string;
  timbreName: string;
}

export interface DollInfo {
  dollId: string;
  dollType: string; // 'rabbit' | 'cat' | 'dog' | 'panda' | 'doll10' | 'doll11'
  dollName: string;
  dollDesc: string;
  battery: number; // 电量 0-100
  isCharging: boolean; // 是否充电
  volume: number; // 音量 0-100
  isOnline: boolean; // 是否在线
  state: string; // : PlayMusic, TellStory,'Charging', Idle
  usedRoleId: string; // 正在使用的角色ID
  originalRoleId: string; // Doll原始角色ID
  unreads: number; // 未读消息数
}

export interface DollInfosResponse {
  dollInfos: DollInfo[];
}

export interface DollStateRequest {
  dollId: string;
}

export interface DollStateResponse {
  battery: number;
  isCharging: boolean;
  volume: number;
  isOnline: boolean;
  state: string;
  unreads: number;
}

// 角色相关类型
export interface RoleRequest {
  dollId: string;
}

export interface RolesResponse {
  roles: DollRole[];
}

export interface RoleChangeRequest {
  dollId: string;
  newRoleId: string;
}

export interface RoleDeleteRequest {
  dollId: string;
  roleId: string;
}

export interface RoleDeleteResponse {
  usedRoleId: string;
}

export interface RoleGenRequest {
  dollId: string;
}

export interface RoleGenResponse {
  roleContent: string;
  timbreId: string;
  timbreName: string;
}

export interface RoleUpsertRequest {
  dollId: string;
  roleId: string;
  roleContent: string;
  timbreId: string;
}

export interface RoleUpsertResponse {
  roleId: string;
}

// 聊天消息相关类型
export interface ChatMessage {
  id: number;
  query: string;
  answer: string;
  shield: string;
  timestamp: number;
}

export interface DollMsgRequest {
  dollId: string;
  limit: number;
  timestamp?: number;
  op?: 'before' | 'after';
}

export interface ParentMsgRequest {
  dollId: string;
  limit: number;
  timestamp?: number;
}

export interface MsgResponse {
  totalSize: number;
  msgs: ChatMessage[];
}

export interface MsgSummaryRequest {
  dollId: string;
}

export interface MsgSummaryResponse {
  mainContent: string[];
  emotion: string;
  AIAddiction: string[];
  negativeContent: string[];
  statistics: string;
}

// 家长助手聊天相关类型
export interface ParentChatRequest {
  dollId: string;
  queryType: string;
  query: string;
}

export interface ParentChatResponse extends ChatMessage {
  isDone: boolean;
}

// 设置相关类型
export interface UrlResponse {
  url: string;
}

// 音色相关类型
export interface Timbre {
  timbreId: string;
  timbreName: string;
  timbreSampleUrl: string; // 音色样本URL
  createTime: string;
  style: string;
  isCustom: boolean;
  tag: string; // 值: my, reco
}

export interface TimbreListResponse {
  recordLimitNum: number;
  list: Timbre[];
}

export interface TimbreDeleteRequest {
  timbreId: string;
}

export interface TimbreDeleteResponse {
  timbreId: string;
}

export interface TimbreRecordRequest {
  voice: string;
}

export interface TimbreRecordResponse {
  newTimbre: Timbre;
}
