import {NavigatorScreenParams} from '@react-navigation/native';
import {DollInfo, DollRole} from '../net';

// Root navigator that can contain stacks, drawers, etc.
export type RootStackParamList = {
  Splash: undefined;
  Modal: {type: string; data?: any};
  LoginRegister: undefined;
  AllDolls: undefined;
  ChatContent: {doll: DollInfo};
  AddDoll: undefined;
  DollConnectWifi: {dollId: string};
  ContentSummary: {dollId: string};
  RoleSwitch: {dollId: string};
  RoleCustom: {dollId: string; dollRole: DollRole | null};
  TimbreRecord: undefined;
  Profile: undefined;
  Settings: undefined;
};

// Combine all navigators for useNavigation hook
export type RootNavigationParamList = RootStackParamList;

// Custom NavigationProp type helpers
declare global {
  namespace ReactNavigation {
    interface RootParamList extends RootNavigationParamList {}
  }
}
