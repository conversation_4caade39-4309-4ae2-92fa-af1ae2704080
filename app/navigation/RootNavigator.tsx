import React from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {NavigationContainer} from '@react-navigation/native';
import {RootStackParamList} from './types';
import {navigationRef} from './navigationUtils';
import SplashScreen from '../screens/SplashScreen';
import LoginRegisterScreen from '../screens/LoginRegisterScreen';
import AllDollsScreen from '../screens/AllDollsScreen';
import ChatContentScreen from '../screens/ChatContentScreen';
import AddDollScreen from '../screens/AddDollScreen';
import DollConnectWifiScreen from '../screens/DollConnectWifiScreen';
import ContentSummaryScreen from '../screens/ContentSummaryScreen';
import RoleSwitchScreen from '../screens/RoleSwitchScreen';
import RoleCustomScreen from '../screens/RoleCustomScreen';
import TimbreRecordScreen from '../screens/TimbreRecordScreen';
import ProfileScreen from '../screens/ProfileScreen';
import SettingsScreen from '../screens/SettingsScreen';

// Placeholder component for Auth screen
import {View, Text} from 'react-native';
import {MyText} from '../custom/CustomFont';

const AuthScreen = () => (
  <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
    <MyText>Auth Screen Placeholder</MyText>
  </View>
);

// Placeholder for Modal
const ModalScreen = () => (
  <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
    <MyText>Modal Placeholder</MyText>
  </View>
);

const Stack = createNativeStackNavigator<RootStackParamList>();

const RootNavigator = () => {
  return (
    <NavigationContainer ref={navigationRef}>
      <Stack.Navigator
        initialRouteName="Splash"
        screenOptions={{
          headerShown: false,
        }}>
        <Stack.Screen name="Splash" component={SplashScreen} />
        <Stack.Screen name="LoginRegister" component={LoginRegisterScreen} />
        <Stack.Screen name="AllDolls" component={AllDollsScreen} />
        <Stack.Screen name="ChatContent" component={ChatContentScreen} />
        <Stack.Screen name="AddDoll" component={AddDollScreen} />
        <Stack.Screen
          name="DollConnectWifi"
          component={DollConnectWifiScreen}
        />
        <Stack.Screen name="ContentSummary" component={ContentSummaryScreen} />
        <Stack.Screen name="RoleSwitch" component={RoleSwitchScreen} />
        <Stack.Screen name="RoleCustom" component={RoleCustomScreen} />
        <Stack.Screen name="TimbreRecord" component={TimbreRecordScreen} />
        <Stack.Screen name="Profile" component={ProfileScreen} />
        <Stack.Screen name="Settings" component={SettingsScreen} />
        <Stack.Group screenOptions={{presentation: 'modal'}}>
          <Stack.Screen name="Modal" component={ModalScreen} />
        </Stack.Group>
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default RootNavigator;
