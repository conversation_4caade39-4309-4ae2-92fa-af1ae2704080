import { createNavigationContainerRef } from '@react-navigation/native';
import { RootNavigationParamList } from './types';

// Create a navigation ref that can be used outside of React components
export const navigationRef = createNavigationContainerRef<RootNavigationParamList>();

// Navigation utilities for use outside of components
export function navigate<RouteName extends keyof RootNavigationParamList>(
  name: RouteName,
  params?: RootNavigationParamList[RouteName]
) {
  if (navigationRef.isReady()) {
    // @ts-ignore - Type issues with react-navigation's typing
    navigationRef.navigate(name, params);
  } else {
    // Log if navigation happens before the container is ready
    console.warn('Navigation attempted before navigator was ready');
  }
}

// Reset navigation state
export function resetNavigation(routes: { name: string; params?: any }[]) {
  if (navigationRef.isReady()) {
    navigationRef.reset({
      index: 0,
      routes,
    });
  }
}

// Go back
export function goBack() {
  if (navigationRef.isReady() && navigationRef.canGoBack()) {
    navigationRef.goBack();
  }
}

// Get current route name
export function getCurrentRouteName(): string | undefined {
  if (navigationRef.isReady()) {
    return navigationRef.getCurrentRoute()?.name;
  }
  return undefined;
}

// Default navigation helper object for exporting
const NavigationService = {
  navigate,
  resetNavigation,
  goBack,
  getCurrentRouteName,
};

export default NavigationService; 