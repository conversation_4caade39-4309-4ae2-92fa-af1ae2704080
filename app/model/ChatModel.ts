import httpService, {ChatMessage} from '../net';
import Logger from '../services/LoggerService';

export interface ChatMsgIterable {
  timestamp: number;
  msgs: ChatMessage[];
  hasMore: boolean;
}
export class ChatModel {
  static async getDollMsgPages(
    dollId: string,
    sortedMsgs: ChatMessage[] | undefined,
    pageSize: number,
    op: 'before' | 'after',
  ): Promise<ChatMsgIterable> {
    let timestamp = 0;
    if (sortedMsgs && sortedMsgs.length > 0) {
      timestamp =
        op === 'after'
          ? sortedMsgs[0].timestamp
          : sortedMsgs[sortedMsgs.length - 1].timestamp;
    }
    const res = await httpService.msg.getDollMsg({
      dollId: dollId,
      limit: pageSize,
      timestamp,
      op,
    });
    if (res.code != 0 || !res.data) {
      throw new Error(res.message || '获取小玩偶消息失败');
    }
    res.data.msgs.sort((a, b) => b.timestamp - a.timestamp);
    if (res.data.msgs.length > 0) {
      timestamp =
        op === 'after'
          ? res.data.msgs[0].timestamp
          : res.data.msgs[res.data.msgs.length - 1].timestamp;
    }
    return {
      timestamp,
      msgs: res.data.msgs,
      hasMore: res.data.msgs.length === pageSize,
    };
  }
  static async getParentMsgPages(
    dollId: string,
    sortedMsgs: ChatMessage[] | undefined,
    pageSize: number,
  ): Promise<ChatMsgIterable> {
    let timestamp = 0;
    if (sortedMsgs && sortedMsgs.length > 0) {
      timestamp = sortedMsgs[sortedMsgs.length - 1].timestamp;
    }
    const res = await httpService.msg.getParentMsg({
      dollId: dollId,
      limit: pageSize,
      timestamp,
    });
    if (res.code != 0 || !res.data) {
      throw new Error(res.message || '获取家长消息失败');
    }
    res.data.msgs.sort((a, b) => b.timestamp - a.timestamp);
    if (res.data.msgs.length > 0) {
      timestamp = res.data.msgs[res.data.msgs.length - 1].timestamp;
    }
    return {
      timestamp,
      msgs: res.data.msgs,
      hasMore: res.data.msgs.length === pageSize,
    };
  }
  static async *generatorGetDollMsgPages(
    dollId: string,
    pageSize: number,
    op: 'before' | 'after',
    timestamp: number,
  ): AsyncIterableIterator<ChatMsgIterable> {
    while (true) {
      const res = await httpService.msg.getDollMsg({
        dollId: dollId,
        limit: pageSize,
        timestamp,
        op,
      });
      if (res.code != 0 || !res.data) {
        throw new Error(res.message || '获取小玩偶消息失败');
      }
      if (res.data.msgs.length > 0) {
        // 按时间戳排序 近的靠前
        res.data.msgs.sort((a, b) => b.timestamp - a.timestamp);
        if (op === 'before') {
          if (!timestamp) {
            timestamp = res.data.msgs[res.data.msgs.length - 1].timestamp;
          } else {
            timestamp = Math.min(
              timestamp,
              res.data.msgs[res.data.msgs.length - 1].timestamp,
            );
          }
        } else {
          if (!timestamp) {
            timestamp = res.data.msgs[0].timestamp;
          } else {
            timestamp = Math.max(timestamp, res.data.msgs[0].timestamp);
          }
        }
      }

      yield {
        timestamp,
        msgs: res.data.msgs,
        hasMore: res.data.msgs.length === pageSize,
      };
      //   if (res.data.msgs.length < pageSize) {
      //     break;
      //   }
    }
  }
  static async *generatorGetParentMsgPages(
    dollId: string,
    pageSize: number,
    timestamp: number,
  ): AsyncIterableIterator<ChatMsgIterable> {
    while (true) {
      const res = await httpService.msg.getParentMsg({
        dollId: dollId,
        limit: pageSize,
        timestamp,
      });
      if (res.code != 0 || !res.data) {
        throw new Error(res.message || '获取家长消息失败');
      }
      if (res.data.msgs.length > 0) {
        // 按时间戳排序 近的靠前
        res.data.msgs.sort((a, b) => b.timestamp - a.timestamp);
        if (!timestamp) {
          timestamp = res.data.msgs[res.data.msgs.length - 1].timestamp;
        } else {
          timestamp = Math.min(
            timestamp,
            res.data.msgs[res.data.msgs.length - 1].timestamp,
          );
        }
      }
      yield {
        timestamp,
        msgs: res.data.msgs,
        hasMore: res.data.msgs.length === pageSize,
      };
      //   if (res.data.msgs.length < pageSize) {
      //     break;
      //   }
    }
  }
  static iteratorDollMsgPages(
    dollId: string,
    pageSize: number,
    op: 'before' | 'after',
    timestamp: number,
  ) {
    return this.generatorGetDollMsgPages(dollId, pageSize, op, timestamp);
  }
  static iteratorParentMsgPages(
    dollId: string,
    pageSize: number,
    timestamp: number,
  ) {
    return this.generatorGetParentMsgPages(dollId, pageSize, timestamp);
  }
}
