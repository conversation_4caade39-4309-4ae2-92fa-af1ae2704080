import AsyncStorage from '@react-native-async-storage/async-storage';

/**
 * 存储服务 - AsyncStorage封装
 */
class StorageService {
  /**
   * 存储字符串数据
   */
  async setString(key: string, value: string): Promise<void> {
    try {
      await AsyncStorage.setItem(key, value);
    } catch (error) {
      console.error('存储数据失败:', error);
      throw error;
    }
  }

  /**
   * 获取字符串数据
   */
  async getString(key: string): Promise<string | null> {
    try {
      return await AsyncStorage.getItem(key);
    } catch (error) {
      console.error('获取数据失败:', error);
      return null;
    }
  }

  /**
   * 存储对象数据
   */
  async setObject<T>(key: string, value: T): Promise<void> {
    try {
      const jsonValue = JSON.stringify(value);
      await AsyncStorage.setItem(key, jsonValue);
    } catch (error) {
      console.error('存储对象数据失败:', error);
      throw error;
    }
  }

  /**
   * 获取对象数据
   */
  async getObject<T>(key: string): Promise<T | null> {
    try {
      const jsonValue = await AsyncStorage.getItem(key);
      return jsonValue != null ? (JSON.parse(jsonValue) as T) : null;
    } catch (error) {
      console.error('获取对象数据失败:', error);
      return null;
    }
  }

  /**
   * 移除数据
   */
  async removeItem(key: string): Promise<void> {
    try {
      await AsyncStorage.removeItem(key);
    } catch (error) {
      console.error('移除数据失败:', error);
      throw error;
    }
  }

  /**
   * 清除所有数据
   */
  async clear(): Promise<void> {
    try {
      await AsyncStorage.clear();
    } catch (error) {
      console.error('清除所有数据失败:', error);
      throw error;
    }
  }

  /**
   * 获取所有已存储的键
   */
  async getAllKeys(): Promise<readonly string[]> {
    try {
      return await AsyncStorage.getAllKeys();
    } catch (error) {
      console.error('获取所有键失败:', error);
      return [];
    }
  }

  /**
   * 批量获取数据
   */
  async multiGet(keys: string[]): Promise<readonly [string, string | null][]> {
    try {
      return await AsyncStorage.multiGet(keys);
    } catch (error) {
      console.error('批量获取数据失败:', error);
      return [];
    }
  }

  /**
   * 批量存储数据
   */
  async multiSet(keyValuePairs: [string, string][]): Promise<void> {
    try {
      await AsyncStorage.multiSet(keyValuePairs);
    } catch (error) {
      console.error('批量存储数据失败:', error);
      throw error;
    }
  }

  /**
   * 批量删除数据
   */
  async multiRemove(keys: string[]): Promise<void> {
    try {
      await AsyncStorage.multiRemove(keys);
    } catch (error) {
      console.error('批量删除数据失败:', error);
      throw error;
    }
  }
}

export default new StorageService();
