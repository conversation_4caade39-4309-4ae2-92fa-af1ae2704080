import {Timbre} from '../net';
// 音色服务接口
class TimbreService {
  getTestTimbreBtnFrames() {
    return [
      require('../assets/img/voice_btn_icon_2.png'),
      require('../assets/img/voice_btn_icon_0.png'),
      require('../assets/img/voice_btn_icon_1.png'),
    ];
  }
  getLongPlayVoiceBtnFrames() {
    return {
      first: require('../assets/img/voice_playing_icon_0.png'),
      left: [
        require('../assets/img/voice_playing_icon_1_left.png'),
        require('../assets/img/voice_playing_icon_2_left.png'),
        require('../assets/img/voice_playing_icon_3_left.png'),
      ],
      right: [
        require('../assets/img/voice_playing_icon_1_right.png'),
        require('../assets/img/voice_playing_icon_2_right.png'),
        require('../assets/img/voice_playing_icon_3_right.png'),
      ],
    };
  }
}

export const timbreService = new TimbreService();
