import {
  <PERSON>leManager,
  Device,
  State,
  ScanMode,
  LogLevel,
  Subscription,
} from 'react-native-ble-plx';
import {Buffer} from 'buffer';
import Logger from './LoggerService';
import {dollService} from './dollService';

// 设备接口
export interface DollDevice {
  id: string;
  name?: string;
  rssi?: number;
}

// WiFi配置接口
export interface WiFiConfig {
  ssid: string;
  password: string;
  security?: number; // 0:开放, 1:WEP, 2:WPA, 3:WPA2
}

// WiFi连接状态
export type WiFiStatus = 'connecting' | 'connected' | 'failed';

// 连接状态
export type ConnectionState =
  | 'disconnected'
  | 'connecting'
  | 'connected'
  | 'error';

export const JLBleRespCmd = {
  0: 'success',
  1: 'connecting',
  2: 'failed',
  3: 'notfound',
  4: 'wrongpass',
};
// 玩偶WiFi配置服务
export class DollWiFiService {
  private bleManager: BleManager;
  private connectedDevice: Device | null = null;
  private connectionState: ConnectionState = 'disconnected';
  private dataSubscription: Subscription | null = null;
  private isInitialized = false;

  // 杰理设备UUID
  private serviceUUID = 'AE80';
  private txCharacteristicUUID = 'AE81';
  private rxCharacteristicUUID = 'AE82';

  constructor() {
    this.bleManager = new BleManager();
  }

  // 初始化
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      this.bleManager.setLogLevel(LogLevel.Verbose);

      const state = await this.bleManager.state();
      if (state !== State.PoweredOn) {
        throw new Error('蓝牙未启用');
      }

      this.isInitialized = true;
      Logger.info('玩偶WiFi服务初始化成功');
    } catch (error) {
      Logger.error('玩偶WiFi服务初始化失败', {error});
      throw error;
    }
  }

  // 扫描玩偶设备
  async scanDevices(
    onDeviceFound: (device: Device) => void,
  ): Promise<DollDevice[]> {
    await this.initialize();

    return new Promise((resolve, reject) => {
      this.bleManager.startDeviceScan(
        null,
        {allowDuplicates: false, scanMode: ScanMode.LowLatency},
        (error, device) => {
          if (error) {
            reject(error);
            return;
          }
          if (device && dollService.testDollBle(device.name || '')) {
            onDeviceFound(device);
          }
        },
      );
    });
  }
  async stopScanDevices(): Promise<void> {
    await this.bleManager.stopDeviceScan();
  }

  // 连接设备
  async connectDevice(deviceId: string): Promise<void> {
    await this.initialize();

    if (
      this.connectionState === 'connected' &&
      this.connectedDevice?.id === deviceId
    ) {
      return;
    }

    try {
      this.setConnectionState('connecting');
      Logger.info(`开始连接设备: ${deviceId}`);

      const device = await this.bleManager.connectToDevice(deviceId);
      const discovered = await device.discoverAllServicesAndCharacteristics();
      Logger.info('发现服务和特征值', {discovered});
      await this.loadServiceUUID(discovered);

      this.connectedDevice = device;
      this.setConnectionState('connected');

      // 启动数据监听
      await this.startDataMonitoring();

      // 监听断开连接
      this.connectedDevice.onDisconnected(() => {
        Logger.info(`设备断开连接: ${deviceId}`);
        this.cleanup();
      });

      Logger.info(`设备连接成功: ${deviceId}`);
    } catch (error) {
      this.setConnectionState('error');
      Logger.error(`设备连接失败: ${deviceId}`, {error});
      throw error;
    }
  }

  // 断开设备连接
  async disconnectDevice(): Promise<void> {
    if (this.connectedDevice) {
      try {
        await this.bleManager.cancelDeviceConnection(this.connectedDevice.id);
      } catch (error) {
        Logger.error('断开设备连接失败', {error});
      }
    }
  }

  // 配置WiFi
  async configureWiFi(config: WiFiConfig): Promise<void> {
    if (!this.connectedDevice || this.connectionState !== 'connected') {
      throw new Error('设备未连接');
    }

    try {
      Logger.info('开始配置WiFi', {ssid: config.ssid});

      // 构建WiFi配置JSON - 使用文档规范的字段名
      const configJson = {
        ssid: config.ssid,
        pass: config.password, // 使用pwd而不是pass
      };

      const jsonString = JSON.stringify(configJson);
      const jsonBytes = new TextEncoder().encode(jsonString);

      // 按照JL协议组包
      const packet = this.buildJLPacket(jsonBytes);

      // 发送配置数据
      await this.sendData(packet);

      // 等待响应
      const response = await this.waitForWifiResponse(30000);
      if (response?.status !== 0) {
        throw new Error(response?.statusText || 'WiFi配置失败');
      }

      Logger.info('WiFi配置结果', {response});
    } catch (error) {
      throw error;
    }
  }

  private async loadServiceUUID(device: Device): Promise<void> {
    for (const serviceUUID of device.serviceUUIDs || []) {
      if (serviceUUID.toUpperCase().includes(this.serviceUUID)) {
        this.serviceUUID = serviceUUID.toUpperCase();
        break;
      }
    }
    const characteristics = await device.characteristicsForService(
      this.serviceUUID,
    );
    for (const characteristic of characteristics) {
      if (
        characteristic.uuid.toUpperCase().includes(this.txCharacteristicUUID)
      ) {
        this.txCharacteristicUUID = characteristic.uuid.toUpperCase();
      } else if (
        characteristic.uuid.toUpperCase().includes(this.rxCharacteristicUUID)
      ) {
        this.rxCharacteristicUUID = characteristic.uuid.toUpperCase();
      }
    }
  }

  // 构建JL协议数据包
  private buildJLPacket(jsonData: Uint8Array): Uint8Array {
    const jsonLength = jsonData.length;
    const totalLength = 2 + 2 + 1 + 1 + jsonLength + 2 + 1; // JL + 长度 + 命令 + JSON + CRC + 结束符

    const writeLength = jsonLength + 2;

    const packet = new Uint8Array(totalLength);
    let offset = 0;

    // JL头部
    packet[offset++] = 0x4a; // 'J'
    packet[offset++] = 0x4c; // 'L'

    // 长度字段 (高字节在前)
    packet[offset++] = (writeLength >> 8) & 0xff; // 高字节
    packet[offset++] = writeLength & 0xff; // 低字节

    // 命令字节
    packet[offset++] = 0x10; // 一级命令
    packet[offset++] = 0x01; // 二级命令

    // JSON数据
    packet.set(jsonData, offset);
    offset += jsonLength;

    // 计算CRC16 (从长度字段到JSON数据结尾)
    const crcData = packet.slice(2, offset);
    const crc16 = this.crc16CCITT(crcData);

    // CRC16 (高字节在前)
    packet[offset++] = (crc16 >> 8) & 0xff; // 高字节
    packet[offset++] = crc16 & 0xff; // 低字节

    // 结束符
    packet[offset++] = 0xff;

    Logger.debug('JL协议数据包构建完成', {
      totalLength,
      jsonLength,
      crc16: crc16.toString(16),
      packet: Array.from(packet)
        .map(b => '0x' + b.toString(16).padStart(2, '0'))
        .join(' '),
    });

    return packet;
  }
  /**
   * CRC16-CCITT算法实现 (多项式: 0x1021, 初始值: 0x0000)
   * 也称为CRC16-XMODEM
   * @param {number[]} data - 字节数组
   * @returns {number} - CRC16值
   */
  private crc16CCITT(data: Uint8Array): number {
    let crc = 0x0000;
    for (let i = 0; i < data.length; i++) {
      crc ^= data[i] << 8;
      for (let j = 0; j < 8; j++) {
        if (crc & 0x8000) {
          crc = (crc << 1) ^ 0x1021;
        } else {
          crc = crc << 1;
        }
      }
    }
    return crc & 0xffff;
  }
  // CRC16计算 (MODBUS标准)
  private calculateCRC16(data: Uint8Array): number {
    let crc = 0xffff;

    for (let i = 0; i < data.length; i++) {
      crc ^= data[i];

      for (let j = 0; j < 8; j++) {
        if (crc & 0x0001) {
          crc = (crc >> 1) ^ 0xa001;
        } else {
          crc = crc >> 1;
        }
      }
    }

    return crc;
  }
  // 获取连接状态
  getConnectionState(): ConnectionState {
    return this.connectionState;
  }
  setConnectionState(state: ConnectionState): void {
    Logger.info('设置连接状态', {state});
    this.connectionState = state;
  }

  // 是否已连接
  isConnected(): boolean {
    return this.connectionState === 'connected';
  }

  // 启动数据监听
  private async startDataMonitoring(): Promise<void> {
    if (!this.connectedDevice) return;

    try {
      this.dataSubscription =
        this.connectedDevice.monitorCharacteristicForService(
          this.serviceUUID,
          this.rxCharacteristicUUID,
          (error, characteristic) => {
            if (error) {
              if (error.message === 'Operation was cancelled') {
                return;
              }
              Logger.error('数据监听错误', {error});
              return;
            }

            if (characteristic?.value) {
              const data = this.base64ToBytes(characteristic.value);
              this.handleReceivedData(new Uint8Array(data));
            }
          },
        );

      Logger.info('数据监听启动成功');
    } catch (error) {
      Logger.error('启动数据监听失败', {error});
      throw error;
    }
  }

  // 发送数据
  private async sendData(data: Uint8Array): Promise<void> {
    if (!this.connectedDevice) {
      throw new Error('设备未连接');
    }

    Logger.info('发送数据', {
      serviceUUID: this.serviceUUID,
      txCharacteristicUUID: this.txCharacteristicUUID,
      data: Array.from(data),
    });

    try {
      // BLE特征值通常有长度限制，需要分包发送
      const maxChunkSize = 20; // BLE标准MTU为23，减去3字节协议头，实际可用20字节

      if (data.length <= maxChunkSize) {
        // 单包发送
        const base64Data = this.bytesToBase64(Array.from(data));
        const result =
          await this.connectedDevice.writeCharacteristicWithoutResponseForService(
            this.serviceUUID,
            this.txCharacteristicUUID,
            base64Data,
          );
        Logger.debug('单包数据发送成功', {length: data.length});
      } else {
        // 分包发送
        Logger.debug('开始分包发送', {
          totalLength: data.length,
          chunkSize: maxChunkSize,
          chunks: Math.ceil(data.length / maxChunkSize),
        });

        for (let offset = 0; offset < data.length; offset += maxChunkSize) {
          const chunk = data.slice(offset, offset + maxChunkSize);
          const base64Data = this.bytesToBase64(Array.from(chunk));

          await this.connectedDevice.writeCharacteristicWithoutResponseForService(
            this.serviceUUID,
            this.txCharacteristicUUID,
            base64Data,
          );

          Logger.debug('分包发送成功', {
            chunkIndex: Math.floor(offset / maxChunkSize) + 1,
            chunkSize: chunk.length,
            offset,
          });

          // 分包间隔，避免发送过快
          if (offset + maxChunkSize < data.length) {
            await new Promise(resolve => setTimeout(resolve, 50));
          }
        }

        Logger.debug('分包发送完成', {totalLength: data.length});
      }
    } catch (error) {
      Logger.error('数据发送失败', {error});
      throw error;
    }
  }

  // 等待设备响应
  private waitForWifiResponse(
    timeout: number,
  ): Promise<{status: number | undefined; statusText: string}> {
    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        onClear();
        reject(new Error('响应超时'));
      }, timeout);

      const onClear = () => {
        clearTimeout(timer);
        this.removeDataHandler(handleData);
      };
      let receivedData: number[] = [];

      const handleData = (data: Uint8Array) => {
        receivedData.push(...Array.from(data));
        try {
          const bytes = new Uint8Array(receivedData);
          if (this.isCompleteJLResponse(bytes)) {
            const jlResponse = this.parseJLResponse(bytes);
            receivedData = [];
            if (jlResponse?.status === 1) {
              return;
            }
            onClear();
            resolve({
              status: jlResponse?.status,
              statusText: jlResponse?.statusText || '',
            });
          }
        } catch (error) {
          onClear();
          reject(error);
        }
        // 检查是否接收到完整的JL协议响应
      };

      this.addDataHandler(handleData);
    });
  }

  // 检查是否为完整的JL协议响应
  private isCompleteJLResponse(data: Uint8Array): boolean {
    if (data.length < 8) return false; // 最小JL包长度

    // 检查JL头部
    if (data[0] !== 0x4a || data[1] !== 0x4c) {
      return false;
    }

    // 获取数据长度
    const dataLength = (data[2] << 8) | data[3];
    const expectedTotalLength = 7 + dataLength; // JL + 长度 + 命令 + 数据 + CRC + 结束符

    if (data.length < expectedTotalLength) {
      return false;
    }

    // 检查结束符
    return data[expectedTotalLength - 1] === 0xff;
  }

  // 解析JL协议响应
  private parseJLResponse(
    data: Uint8Array,
  ): {status: number; statusText: string} | null {
    try {
      if (data.length < 8) return null;

      // 检查JL头部
      if (data[0] !== 0x4a || data[1] !== 0x4c) {
        return null;
      }

      // 获取数据长度
      const dataLength = (data[2] << 8) | data[3];

      // 验证包长度
      const expectedTotalLength = 7 + dataLength;
      if (data.length !== expectedTotalLength) {
        Logger.warn('JL协议响应长度不正确', {
          expected: expectedTotalLength,
          received: data.length,
        });
        return null;
      }

      // 检查结束符
      if (data[expectedTotalLength - 1] !== 0xff) {
        return null;
      }

      // 验证CRC16
      const crcData = data.slice(2, 4 + dataLength);
      const expectedCRC = this.crc16CCITT(crcData);
      const receivedCRC =
        (data[4 + dataLength] << 8) | data[4 + dataLength + 1];

      if (expectedCRC !== receivedCRC) {
        Logger.warn('JL协议响应CRC校验失败', {
          expected: expectedCRC.toString(16),
          received: receivedCRC.toString(16),
        });
        // 继续处理，可能设备端CRC实现有差异
      }

      // 提取JSON数据
      const jsonData = data.slice(6, 4 + dataLength);
      // const text = new TextDecoder().decode(jsonData);
      const text = Buffer.from(jsonData).toString('utf-8');
      Logger.info('解析JL协议响应', {text});
      const jsonResponse = JSON.parse(text);
      return {
        status: jsonResponse.status,
        statusText:
          JLBleRespCmd[jsonResponse.status as keyof typeof JLBleRespCmd] ||
          'unknown',
      };
    } catch (error) {
      Logger.error('解析JL协议响应失败', {error});
      return null;
    }
  }

  // 数据处理器管理
  private dataHandlers: ((data: Uint8Array) => void)[] = [];

  private addDataHandler(handler: (data: Uint8Array) => void): void {
    this.dataHandlers.push(handler);
  }

  private removeDataHandler(handler: (data: Uint8Array) => void): void {
    const index = this.dataHandlers.indexOf(handler);
    if (index !== -1) {
      this.dataHandlers.splice(index, 1);
    }
  }

  private handleReceivedData(data: Uint8Array): void {
    this.dataHandlers.forEach(handler => {
      try {
        Logger.info('数据处理器执行', {data});
        handler(data);
      } catch (error) {
        Logger.error('数据处理器执行失败', {error});
      }
    });
  }

  // 工具方法
  private bytesToBase64(bytes: number[]): string {
    return Buffer.from(bytes).toString('base64');
  }

  private base64ToBytes(base64: string): number[] {
    return Array.from(Buffer.from(base64, 'base64'));
  }

  // 清理资源
  private cleanup(): void {
    if (this.dataSubscription) {
      this.dataSubscription.remove();
      this.dataSubscription = null;
    }

    this.connectedDevice = null;
    this.setConnectionState('disconnected');
    this.dataHandlers = [];
  }

  // 销毁服务
  destroy(): void {
    this.cleanup();
    this.bleManager.destroy();
    this.isInitialized = false;
  }
}

// 导出服务实例
export const dollWiFiService = new DollWiFiService();
