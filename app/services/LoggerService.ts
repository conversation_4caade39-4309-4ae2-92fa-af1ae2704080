import {logger, consoleTransport, fileAsyncTransport} from 'react-native-logs';
import {Platform} from 'react-native';

/**
 * 日志级别定义
 */
export enum LogType {
  DEBUG = 'debug',
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error',
}

// 控制台配置
const consoleConfig = {
  severity: __DEV__ ? 'debug' : 'info',
  transport: consoleTransport,
  transportOptions: {
    color: true,
    hideDate: false,
    dateFormat: 'time',
    printLevel: true,
    printDate: true,
    colors: {
      debug: 'blueBright' as const,
      info: 'greenBright' as const,
      warn: 'yellowBright' as const,
      error: 'redBright' as const,
    },
  },
};

// 在开发环境使用控制台日志
const config = consoleConfig;

// 创建日志实例
const log = logger.createLogger(config);

/**
 * 日志服务类
 */
class LoggerService {
  private logger;

  constructor() {
    this.logger = log;
  }

  /**
   * 记录调试日志
   * @param message 日志消息
   * @param data 附加数据
   */
  debug(message: string, ...data: any[]): void {
    this.logger.debug(message, ...data);
  }

  /**
   * 记录信息日志
   * @param message 日志消息
   * @param data 附加数据
   */
  info(message: string, ...data: any[]): void {
    this.logger.info(message, ...data);
  }

  /**
   * 记录警告日志
   * @param message 日志消息
   * @param data 附加数据
   */
  warn(message: string, ...data: any[]): void {
    this.logger.warn(message, ...data);
  }

  /**
   * 记录错误日志
   * @param message 日志消息
   * @param data 附加数据
   */
  error(message: string, ...data: any[]): void {
    this.logger.error(message, ...data);
  }
}

// 创建单例实例
const Logger = new LoggerService();

export default Logger;

/**
 * 使用示例：
 *
 * 导入Logger:
 * ```
 * import Logger from '../services/LoggerService';
 * ```
 *
 * 记录不同级别的日志:
 * ```
 * // 调试信息
 * Logger.debug('这是调试信息', {someData: 'value'});
 *
 * // 普通信息
 * Logger.info('用户登录成功', {userId: 123});
 *
 * // 警告信息
 * Logger.warn('请求超时，正在重试', {attempt: 3});
 *
 * // 错误信息
 * try {
 *   // 一些可能出错的代码
 * } catch (error) {
 *   Logger.error('操作失败', error);
 * }
 * ```
 */
