import {ImageSourcePropType} from 'react-native/Libraries/Image/Image';
import {DollInfo} from '../net';

export type DollType = 'rabbit' | 'blue' | 'purple' | 'pink' | 'yellow';
// 资源映射
const dollBgMap: Record<DollType, ImageSourcePropType> = {
  rabbit: require('../assets/img/doll_list_item_bg.png'),
  blue: require('../assets/img/doll_list_item_bg.png'),
  purple: require('../assets/img/doll_list_item_bg.png'),
  pink: require('../assets/img/doll_list_item_bg.png'),
  yellow: require('../assets/img/doll_list_item_bg.png'),
};
const dollImgMap: Record<DollType, ImageSourcePropType> = {
  rabbit: require('../assets/img/avatar_rabbit.png'),
  blue: require('../assets/img/avatar_blue.png'),
  purple: require('../assets/img/avatar_purple.png'),
  pink: require('../assets/img/avatar_pink.png'),
  yellow: require('../assets/img/avatar_yellow.png'),
};
const dollChatBoardMap: Record<DollType, ImageSourcePropType> = {
  rabbit: require('../assets/img/chat_bg_rabbit.png'),
  blue: require('../assets/img/chat_bg_blue.png'),
  purple: require('../assets/img/chat_bg_purple.png'),
  pink: require('../assets/img/chat_bg_pink.png'),
  yellow: require('../assets/img/chat_bg_yellow.png'),
};

const dollSummaryBoardMap: Record<DollType, ImageSourcePropType> = {
  rabbit: require('../assets/img/summary_board_rabbit.png'),
  blue: require('../assets/img/summary_board_blue.png'),
  purple: require('../assets/img/summary_board_purple.png'),
  pink: require('../assets/img/summary_board_pink.png'),
  yellow: require('../assets/img/summary_board_yellow.png'),
};
const dollCiclePurpleAvatarMap: Record<
  string,
  {p: ImageSourcePropType; b: ImageSourcePropType}
> = {
  rabbit: {
    p: require('../assets/img/circle_avatar_rabbit_p.png'),
    b: require('../assets/img/circle_avatar_rabbit_b.png'),
  },
  blue: {
    p: require('../assets/img/circle_avatar_blue_p.png'),
    b: require('../assets/img/circle_avatar_blue_b.png'),
  },
  purple: {
    p: require('../assets/img/circle_avatar_purple_p.png'),
    b: require('../assets/img/circle_avatar_purple_b.png'),
  },
  pink: {
    p: require('../assets/img/circle_avatar_pink_p.png'),
    b: require('../assets/img/circle_avatar_pink_b.png'),
  },
  yellow: {
    p: require('../assets/img/circle_avatar_yellow_p.png'),
    b: require('../assets/img/circle_avatar_yellow_b.png'),
  },
};

class DollService {
  getDollBgBoard(dollType: DollType): ImageSourcePropType {
    return dollBgMap[dollType];
  }
  getDollAvatar(dollType?: DollType): ImageSourcePropType {
    if (!dollType) {
      return dollImgMap.rabbit;
    }
    return dollImgMap[dollType];
  }
  getDollChatBoard(dollType: DollType): ImageSourcePropType {
    return dollChatBoardMap[dollType];
  }
  getDollSummaryBoard(dollType: DollType): ImageSourcePropType {
    return dollSummaryBoardMap[dollType];
  }
  getStateText(doll: DollInfo): string {
    if (!doll.isOnline) {
      return '离线';
    }
    switch (doll.state) {
      case 'TellStory':
        return '正在讲故事';
      case 'PlayMusic':
        return '正在唱儿歌';
      case 'Idle':
        return '充电中';
      default:
    }
    return '未知';
  }
  getDollItemViewInfo(item: DollInfo): {
    text: string;
    textColor: string;
    bgColor: string;
  } {
    if (!item.isOnline) {
      return {text: '离线', textColor: '#FF840B', bgColor: '#FFEED4'};
    }
    if (item.state === 'TellStory') {
      return {text: '正在讲故事', textColor: '#0283DF', bgColor: '#BFF5FF'};
    }
    if (item.state === 'PlayMusic') {
      return {text: '正在唱儿歌', textColor: '#FF6969', bgColor: '#FFD9DF'};
    }
    if (item.state === 'Idle') {
      return {text: '充电中', textColor: '#03A66F', bgColor: '#BBFEF9'};
    }
    // 可扩展其他状态
    return {text: '未知', textColor: '#BDBDBD', bgColor: '#F2F2F2'};
  }
  getDollCircleAvatar(dollType: DollType): {
    p: ImageSourcePropType;
    b: ImageSourcePropType;
  } {
    return dollCiclePurpleAvatarMap[dollType];
  }
  getDollBlePrefix(): string {
    return 'JL-';
  }
  testDollBle(name: string): boolean {
    name = name || '';
    return name.startsWith(this.getDollBlePrefix());
  }

  getDollTypeById(dollId: string): DollType {
    if (this.testDollBle(dollId)) {
      const prefix = this.getDollBlePrefix();
      const reg = new RegExp(`${prefix}(\w+)-.*`);
      const match = reg.exec(dollId);
      if (match && match[1]) {
        const type = match[1].toLowerCase() as DollType;
        if (type in dollImgMap) {
          return type;
        }
      }
    }
    return 'rabbit';
  }
}

// 创建服务单例
export const dollService = new DollService();
