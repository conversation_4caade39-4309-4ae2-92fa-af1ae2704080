// WiFi扫描服务
import WifiManager from 'react-native-wifi-reborn';
import {Platform, PermissionsAndroid} from 'react-native';
import Logger from './LoggerService';

// WiFi网络接口
export interface WiFiNetwork {
  SSID: string; // WiFi名称
  BSSID: string; // MAC地址
  capabilities: string; // 安全能力
  frequency: number; // 频率
  level: number; // 信号强度
  timestamp: number; // 时间戳
}

// WiFi网络显示接口
export interface WiFiNetworkDisplay {
  id: string;
  name: string;
  strength: 'strong' | 'medium' | 'weak';
  security: number; // 0:开放, 1:WEP, 2:WPA, 3:WPA2
  frequency: number;
}

// WiFi扫描服务类
export class WiFiScanService {
  private isScanning = false;
  private scanResults: WiFiNetwork[] = [];

  // 请求WiFi权限
  async requestWiFiPermissions(): Promise<boolean> {
    try {
      if (Platform.OS === 'android') {
        const granted = await PermissionsAndroid.requestMultiple([
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
          PermissionsAndroid.PERMISSIONS.ACCESS_COARSE_LOCATION,
        ]);

        const allGranted = Object.values(granted).every(
          permission => permission === PermissionsAndroid.RESULTS.GRANTED,
        );

        if (!allGranted) {
          Logger.warn('WiFi权限未完全授予', {granted});
          return false;
        }
      }

      return true;
    } catch (error) {
      Logger.error('请求WiFi权限失败', {error});
      return false;
    }
  }

  // 扫描WiFi网络
  async scanWiFiNetworks(): Promise<WiFiNetworkDisplay[]> {
    try {
      if (this.isScanning) {
        Logger.warn('WiFi扫描正在进行中');
        return this.formatScanResults(this.scanResults);
      }

      // 检查权限
      const hasPermissions = await this.requestWiFiPermissions();
      if (!hasPermissions) {
        throw new Error('WiFi权限未授予');
      }

      this.isScanning = true;
      Logger.info('开始扫描WiFi网络');

      // 执行WiFi扫描
      const networks = await WifiManager.loadWifiList();
      this.scanResults = networks;

      Logger.info('WiFi扫描完成', {
        networkCount: networks.length,
        networks: networks.map(n => ({
          SSID: n.SSID,
          level: n.level,
          frequency: n.frequency,
        })),
      });

      return this.formatScanResults(networks);
    } catch (error) {
      Logger.error('WiFi扫描失败', {error});
      throw new Error(
        `WiFi扫描失败: ${error instanceof Error ? error.message : String(error)}`,
      );
    } finally {
      this.isScanning = false;
    }
  }

  // 获取当前连接的WiFi信息
  async getCurrentWiFi(): Promise<string | null> {
    try {
      const currentSSID = await WifiManager.getCurrentWifiSSID();
      Logger.debug('当前连接的WiFi', {SSID: currentSSID});
      return currentSSID?.trim();
    } catch (error) {
      Logger.error('获取当前WiFi失败', {error});
      return null;
    }
  }

  // 检查WiFi是否启用
  async isWiFiEnabled(): Promise<boolean> {
    try {
      const isEnabled = await WifiManager.isEnabled();
      Logger.debug('WiFi启用状态', {isEnabled});
      return isEnabled;
    } catch (error) {
      Logger.error('检查WiFi状态失败', {error});
      return false;
    }
  }

  // 格式化扫描结果
  private formatScanResults(networks: WiFiNetwork[]): WiFiNetworkDisplay[] {
    return networks
      .filter(network => network.SSID && network.SSID.trim() !== '') // 过滤空SSID
      .map((network, index) => ({
        id: `${network.BSSID || index}`,
        name: network.SSID,
        strength: this.getSignalStrength(network.level),
        security: this.getSecurityType(network.capabilities),
        frequency: network.frequency || 0,
      }))
      .sort((a, b) => {
        // 按信号强度排序
        const strengthOrder = {strong: 3, medium: 2, weak: 1};
        return strengthOrder[b.strength] - strengthOrder[a.strength];
      })
      .slice(0, 20); // 限制返回前20个网络
  }

  // 获取信号强度等级
  private getSignalStrength(level: number): 'strong' | 'medium' | 'weak' {
    // level通常是负数，值越大信号越强
    if (level >= -50) {
      return 'strong';
    } else if (level >= -70) {
      return 'medium';
    } else {
      return 'weak';
    }
  }

  // 获取安全类型
  private getSecurityType(capabilities: string): number {
    if (!capabilities) {
      return 0; // 开放网络
    }

    const caps = capabilities.toUpperCase();

    if (caps.includes('WPA2')) {
      return 3; // WPA2
    } else if (caps.includes('WPA')) {
      return 2; // WPA
    } else if (caps.includes('WEP')) {
      return 1; // WEP
    } else {
      return 0; // 开放网络
    }
  }

  // 过滤2.4GHz网络
  filter24GHzNetworks(networks: WiFiNetworkDisplay[]): WiFiNetworkDisplay[] {
    return networks.filter(network => {
      // 2.4GHz频段通常在2400-2500MHz范围内
      return network.frequency >= 2400 && network.frequency <= 2500;
    });
  }

  // 获取缓存的扫描结果
  getCachedResults(): WiFiNetworkDisplay[] {
    return this.formatScanResults(this.scanResults);
  }

  // 清除缓存
  clearCache(): void {
    this.scanResults = [];
  }
}

// 导出服务实例
export const wifiScanService = new WiFiScanService();
