import { Platform, Linking } from 'react-native';
import DeviceInfo from 'react-native-device-info';
import { atom, useAtom } from 'jotai';

// 更新信息接口
export interface UpdateInfo {
  version: string;       // 版本号
  buildNumber: string;   // 构建号
  releaseNotes: string;  // 发布说明
  downloadUrl: string;   // 下载链接
  forceUpdate: boolean;  // 是否强制更新
  minVersion: string;    // 最低要求版本
}

// 更新状态类型
export type UpdateStatus = 
  | 'idle'             // 空闲状态
  | 'checking'         // 检查中
  | 'available'        // 有可用更新
  | 'not-available'    // 无可用更新
  | 'downloading'      // 下载中
  | 'downloaded'       // 已下载
  | 'error';           // 错误

// 更新状态原子
export const updateStatusAtom = atom<UpdateStatus>('idle');
// 更新信息原子
export const updateInfoAtom = atom<UpdateInfo | null>(null);
// 更新进度原子 (0-100)
export const updateProgressAtom = atom<number>(0);
// 更新错误原子
export const updateErrorAtom = atom<string | null>(null);

/**
 * 更新管理服务
 * 提供检查更新和下载更新功能
 */
class UpdateManager {
  // 更新服务的API端点
  private apiEndpoint = 'https://your-api-server.com/api/updates';
  
  // Atom setters
  private setUpdateStatus: (status: UpdateStatus) => void;
  private setUpdateInfo: (info: UpdateInfo | null) => void;
  private setUpdateProgress: (progress: number) => void;
  private setUpdateError: (error: string | null) => void;
  
  constructor() {
    // 初始化状态设置器
    this.setUpdateStatus = (status) => {
      const currentValue = updateStatusAtom.init;
      updateStatusAtom.onMount = (setAtom) => {
        setAtom(status);
        return () => setAtom(currentValue);
      };
    };
    
    this.setUpdateInfo = (info) => {
      const currentValue = updateInfoAtom.init;
      updateInfoAtom.onMount = (setAtom) => {
        setAtom(info);
        return () => setAtom(currentValue);
      };
    };
    
    this.setUpdateProgress = (progress) => {
      const currentValue = updateProgressAtom.init;
      updateProgressAtom.onMount = (setAtom) => {
        setAtom(progress);
        return () => setAtom(currentValue);
      };
    };
    
    this.setUpdateError = (error) => {
      const currentValue = updateErrorAtom.init;
      updateErrorAtom.onMount = (setAtom) => {
        setAtom(error);
        return () => setAtom(currentValue);
      };
    };
  }
  
  /**
   * 获取当前应用版本信息
   */
  getAppVersion(): { version: string, buildNumber: string } {
    return {
      version: DeviceInfo.getVersion(),
      buildNumber: DeviceInfo.getBuildNumber(),
    };
  }

  /**
   * 比较版本号
   * @param version1 版本1
   * @param version2 版本2
   * @returns 1:version1大于version2, -1:version1小于version2, 0:相等
   */
  compareVersions(version1: string, version2: string): number {
    const parts1 = version1.split('.').map(Number);
    const parts2 = version2.split('.').map(Number);
    
    for (let i = 0; i < Math.max(parts1.length, parts2.length); i++) {
      const part1 = parts1[i] || 0;
      const part2 = parts2[i] || 0;
      
      if (part1 > part2) return 1;
      if (part1 < part2) return -1;
    }
    
    return 0;
  }

  /**
   * 检查更新
   * 通过API获取最新版本信息并比较当前版本
   */
  async checkForUpdates(): Promise<{ hasUpdate: boolean, updateInfo: UpdateInfo | null }> {
    try {
      // 设置检查状态
      this.setUpdateStatus('checking');
      
      const { version, buildNumber } = this.getAppVersion();
      const platform = Platform.OS;
      
      // 构建请求URL(带上当前版本和平台信息)
      const url = `${this.apiEndpoint}?version=${version}&buildNumber=${buildNumber}&platform=${platform}`;
      
      // 这里模拟API请求,实际应用中应替换为真实API调用
      // const response = await fetch(url);
      // const updateInfo = await response.json();
      
      // 模拟响应数据(用于测试)
      const mockResponse: UpdateInfo = {
        version: '1.1.0',
        buildNumber: (parseInt(buildNumber) + 1).toString(),
        releaseNotes: '1. 修复了一些已知问题\n2. 提升了应用性能\n3. 新增WiFi配置功能',
        downloadUrl: platform === 'ios' 
          ? 'https://apps.apple.com/app/your-app-id'
          : 'https://play.google.com/store/apps/details?id=com.yourapp',
        forceUpdate: false,
        minVersion: '1.0.0'
      };
      
      // 判断是否有可用更新
      const hasUpdate = this.compareVersions(mockResponse.version, version) > 0;
      
      // 更新状态
      this.setUpdateStatus(hasUpdate ? 'available' : 'not-available');
      this.setUpdateInfo(hasUpdate ? mockResponse : null);
      
      return { 
        hasUpdate, 
        updateInfo: hasUpdate ? mockResponse : null 
      };
    } catch (error) {
      console.error('检查更新失败:', error);
      this.setUpdateStatus('error');
      this.setUpdateError(error instanceof Error ? error.message : '未知错误');
      return { hasUpdate: false, updateInfo: null };
    }
  }

  /**
   * 下载更新
   * 根据平台跳转到应用商店或下载APK
   */
  async downloadUpdate(updateInfo: UpdateInfo): Promise<boolean> {
    try {
      if (!updateInfo) {
        throw new Error('没有可用的更新信息');
      }
      
      // 设置下载状态
      this.setUpdateStatus('downloading');
      
      if (Platform.OS === 'ios') {
        // iOS通过跳转到App Store更新
        const supported = await Linking.canOpenURL(updateInfo.downloadUrl);
        if (supported) {
          await Linking.openURL(updateInfo.downloadUrl);
          return true;
        } else {
          throw new Error('无法打开App Store');
        }
      } else {
        // Android平台可以通过Intent跳转到应用商店或下载APK
        // 这里简化为打开Google Play商店链接
        const supported = await Linking.canOpenURL(updateInfo.downloadUrl);
        if (supported) {
          await Linking.openURL(updateInfo.downloadUrl);
          return true;
        } else {
          throw new Error('无法打开应用商店');
        }
      }
    } catch (error) {
      console.error('下载更新失败:', error);
      this.setUpdateStatus('error');
      this.setUpdateError(error instanceof Error ? error.message : '未知错误');
      return false;
    }
  }

  /**
   * 检查是否需要强制更新
   * 如果当前版本低于最低要求版本,则需要强制更新
   */
  isForceUpdateRequired(updateInfo: UpdateInfo): boolean {
    if (!updateInfo) return false;
    
    const { version } = this.getAppVersion();
    return (
      updateInfo.forceUpdate || 
      this.compareVersions(version, updateInfo.minVersion) < 0
    );
  }
}

export default new UpdateManager(); 