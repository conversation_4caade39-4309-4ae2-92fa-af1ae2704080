import {
  PERMISSIONS,
  RESULTS,
  Permission,
  PermissionStatus,
  checkMultiple,
  requestMultiple,
  check,
  request,
  openSettings,
} from 'react-native-permissions';
import {Platform} from 'react-native';
import Logger from './LoggerService';

// 权限类型
export enum PermissionType {
  BLUETOOTH = 'bluetooth',
  LOCATION = 'location',
  MICROPHONE = 'microphone',
  PHOTO_LIBRARY = 'storage',
  CAMERA = 'camera',
  CONTACTS = 'contacts',
  MEDIA_LIBRARY = 'mediaLibrary',
}

// 不同平台权限映射
const permissionMap: Record<PermissionType, Permission[]> = {
  [PermissionType.BLUETOOTH]: [],
  [PermissionType.LOCATION]: [],
  [PermissionType.MICROPHONE]: [],
  [PermissionType.PHOTO_LIBRARY]: [],
  [PermissionType.CAMERA]: [],
  [PermissionType.CONTACTS]: [],
  [PermissionType.MEDIA_LIBRARY]: [],
};

// 初始化Android权限
if (Platform.OS === 'android') {
  const androidVersion = parseInt(String(Platform.Version), 10);

  permissionMap[PermissionType.BLUETOOTH] =
    androidVersion >= 31
      ? [
          PERMISSIONS.ANDROID.BLUETOOTH_SCAN,
          PERMISSIONS.ANDROID.BLUETOOTH_CONNECT,
        ]
      : [PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION];

  permissionMap[PermissionType.LOCATION] = [
    PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION,
    PERMISSIONS.ANDROID.ACCESS_COARSE_LOCATION,
  ];

  permissionMap[PermissionType.MICROPHONE] = [PERMISSIONS.ANDROID.RECORD_AUDIO];

  permissionMap[PermissionType.PHOTO_LIBRARY] =
    androidVersion >= 33
      ? [
          PERMISSIONS.ANDROID.READ_MEDIA_IMAGES,
          PERMISSIONS.ANDROID.READ_MEDIA_VIDEO,
        ]
      : [
          PERMISSIONS.ANDROID.READ_EXTERNAL_STORAGE,
          PERMISSIONS.ANDROID.WRITE_EXTERNAL_STORAGE,
        ];

  permissionMap[PermissionType.CAMERA] = [PERMISSIONS.ANDROID.CAMERA];

  permissionMap[PermissionType.CONTACTS] = [PERMISSIONS.ANDROID.READ_CONTACTS];

  permissionMap[PermissionType.MEDIA_LIBRARY] =
    androidVersion >= 33
      ? [PERMISSIONS.ANDROID.READ_MEDIA_AUDIO]
      : [PERMISSIONS.ANDROID.READ_EXTERNAL_STORAGE];
}

// 初始化iOS权限
if (Platform.OS === 'ios') {
  permissionMap[PermissionType.BLUETOOTH] = [PERMISSIONS.IOS.BLUETOOTH];
  permissionMap[PermissionType.LOCATION] = [
    PERMISSIONS.IOS.LOCATION_WHEN_IN_USE,
    PERMISSIONS.IOS.LOCATION_ALWAYS,
  ];
  permissionMap[PermissionType.MICROPHONE] = [PERMISSIONS.IOS.MICROPHONE];
  permissionMap[PermissionType.PHOTO_LIBRARY] = [PERMISSIONS.IOS.PHOTO_LIBRARY];
  permissionMap[PermissionType.CAMERA] = [PERMISSIONS.IOS.CAMERA];
  permissionMap[PermissionType.CONTACTS] = [PERMISSIONS.IOS.CONTACTS];
  permissionMap[PermissionType.MEDIA_LIBRARY] = [PERMISSIONS.IOS.MEDIA_LIBRARY];
}

class PermissionManager {
  /**
   * 检查单个权限
   * @param type 权限类型
   * @returns 权限状态
   */
  async checkPermission(type: PermissionType): Promise<PermissionStatus> {
    try {
      const permissions = permissionMap[type];
      if (!permissions || permissions.length === 0) {
        Logger.warn('未找到对应平台的权限定义', {permissionType: type});
        return RESULTS.DENIED;
      }

      if (permissions.length === 1) {
        return await check(permissions[0]);
      } else {
        const results = await checkMultiple(permissions);
        // 如果有任一权限被阻止，返回最严格的状态
        if (Object.values(results).includes(RESULTS.BLOCKED)) {
          return RESULTS.BLOCKED;
        }
        if (Object.values(results).includes(RESULTS.DENIED)) {
          return RESULTS.DENIED;
        }
        return RESULTS.GRANTED;
      }
    } catch (error) {
      Logger.error('检查权限失败', {permissionType: type, error});
      return RESULTS.DENIED;
    }
  }

  /**
   * 请求单个权限
   * @param type 权限类型
   * @returns 是否获取到权限
   */
  async requestPermission(
    type: PermissionType,
  ): Promise<Record<Permission, PermissionStatus>> {
    try {
      const permissions = permissionMap[type];
      if (!permissions || permissions.length === 0) {
        Logger.warn('未找到对应平台的权限定义', {permissionType: type});
        return {} as Record<Permission, PermissionStatus>;
      }

      if (permissions.length === 1) {
        const result = await request(permissions[0]);
        return {
          [permissions[0]]: result,
        } as Record<Permission, PermissionStatus>;
      } else {
        const results = await requestMultiple(permissions);
        return results;
      }
    } catch (error) {
      Logger.error('请求权限失败', {permissionType: type, error});
      throw error;
    }
  }

  /**
   * 检查多个权限
   * @param types 权限类型数组
   * @returns 每种权限的状态
   */
  async checkMultiplePermissions(
    types: PermissionType[],
  ): Promise<Record<PermissionType, PermissionStatus>> {
    const results: Record<PermissionType, PermissionStatus> = {} as Record<
      PermissionType,
      PermissionStatus
    >;

    for (const type of types) {
      results[type] = await this.checkPermission(type);
    }

    return results;
  }

  /**
   * 请求多个权限
   * @param types 权限类型数组
   * @returns 每种权限的状态
   */
  async requestMultiplePermissions(
    types: PermissionType[],
  ): Promise<Record<PermissionType, PermissionStatus>> {
    const results: Record<PermissionType, PermissionStatus> = {} as Record<
      PermissionType,
      PermissionStatus
    >;

    for (const type of types) {
      const many = await this.requestPermission(type);
      for (const key in many) {
        results[type] = many[key as Permission];
      }
    }

    return results;
  }

  /**
   * 打开应用设置界面
   * 当权限被永久拒绝时，引导用户去设置中开启
   */
  openSettings(): Promise<boolean> {
    try {
      return openSettings()
        .then(() => true)
        .catch(() => false);
    } catch (error) {
      Logger.error('打开应用设置失败', {error});
      return Promise.resolve(false);
    }
  }
}

export default new PermissionManager();
