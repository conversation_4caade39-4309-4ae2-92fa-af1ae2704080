import {useState, useCallback} from 'react';
import {
  dollWiFiService,
  DollDevice,
  WiFiConfig,
} from '../services/DollWiFiService';
import Logger from '../services/LoggerService';

// WiFi配置状态
export type WiFiConfigStatus =
  | 'idle'
  | 'configuring'
  | 'connecting'
  | 'connected'
  | 'failed';

// 玩偶设备扫描Hook
export function useDollScanner() {
  const [devices, setDevices] = useState<DollDevice[]>([]);
  const [isScanning, setIsScanning] = useState(false);

  const startScan = useCallback(async () => {
    try {
      setDevices([]);

      Logger.info('开始扫描玩偶设备');
      setIsScanning(true);
      await dollWiFiService.scanDevices(device => {
        setDevices(prevDevices => [
          ...prevDevices,
          {
            id: device.id,
            name: device.name || '',
            rssi: device.rssi || 0,
          },
        ]);
      });
    } catch (error) {
      Logger.error('扫描设备失败', {error});
      throw error;
    }
  }, []);
  const stopScan = useCallback(async () => {
    try {
      await dollWiFiService.stopScanDevices();
      setIsScanning(false);
      Logger.info('停止扫描成功');
    } catch (error) {
      Logger.error('停止扫描失败', {error});
      throw error;
    }
  }, []);

  return {
    devices,
    isScanning,
    startScan,
    stopScan,
  };
}

// 玩偶设备连接Hook
export function useDollConnection(deviceId?: string) {
  const connect = useCallback(async () => {
    if (!deviceId) {
      throw new Error('设备ID不能为空');
    }

    try {
      Logger.info('开始连接设备', {deviceId});
      await dollWiFiService.connectDevice(deviceId);
      Logger.info('设备连接成功', {deviceId});
    } catch (error) {
      Logger.error('设备连接失败', {deviceId, error});
      throw error;
    }
  }, [deviceId]);

  const disconnect = useCallback(async () => {
    try {
      await dollWiFiService.disconnectDevice();
    } catch (error) {
      Logger.error('断开连接失败', {error});
      throw error;
    }
  }, []);

  return {
    connect,
    disconnect,
  };
}

// WiFi配置Hook
export function useWiFiConfig() {
  const [status, setStatus] = useState<WiFiConfigStatus>('idle');
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);

  const configureWiFi = useCallback(async (config: WiFiConfig) => {
    try {
      setStatus('configuring');
      setProgress(50);
      setError(null);
      if (!dollWiFiService.isConnected()) {
        throw new Error('设备未连接');
      }

      Logger.info('开始WiFi配置流程', {ssid: config.ssid});

      setStatus('connecting');
      await dollWiFiService.configureWiFi(config);
      setStatus('connected');
      setProgress(100);
    } catch (error) {
      setError(error instanceof Error ? error.message : String(error));
      setStatus('failed');
      throw error;
    }
  }, []);

  // 重置状态
  const reset = useCallback(() => {
    setStatus('idle');
    setProgress(0);
    setError(null);
  }, []);

  // 重试配置
  const retry = useCallback(
    async (config: WiFiConfig) => {
      reset();
      await configureWiFi(config);
    },
    [configureWiFi, reset],
  );

  return {
    status,
    progress,
    error,
    configureWiFi,
    reset,
    retry,
    isLoading: status === 'configuring' || status === 'connecting',
    isSuccess: status === 'connected',
    isError: status === 'failed',
  };
}

// 组合Hook - 提供完整的玩偶WiFi配置功能
export function useDollWiFi(deviceId?: string) {
  const scanner = useDollScanner();
  const wifiConfig = useWiFiConfig();
  const connection = useDollConnection(deviceId);

  // 一键配置WiFi流程
  const configureDeviceWiFi = useCallback(
    async (config: WiFiConfig) => {
      if (!deviceId) {
        throw new Error('设备ID不能为空');
      }

      // 配置WiFi
      await wifiConfig.configureWiFi(config);
    },
    [deviceId, wifiConfig],
  );

  return {
    // 扫描功能
    devices: scanner.devices,
    isScanning: scanner.isScanning,
    startScan: scanner.startScan,
    stopScan: scanner.stopScan,

    // 连接功能
    connect: connection.connect,
    disconnect: connection.disconnect,

    // WiFi配置功能
    wifiStatus: wifiConfig.status,
    wifiProgress: wifiConfig.progress,
    wifiError: wifiConfig.error,
    configureWiFi: wifiConfig.configureWiFi,
    resetWiFi: wifiConfig.reset,
    retryWiFi: wifiConfig.retry,

    // 组合功能
    configureDeviceWiFi,

    // 状态判断
    isWiFiLoading: wifiConfig.isLoading,
    isWiFiSuccess: wifiConfig.isSuccess,
    isWiFiError: wifiConfig.isError,
  };
}
