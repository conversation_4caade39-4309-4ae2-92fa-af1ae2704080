import {useAtom, useAtomValue, useSetAtom} from 'jotai';
import {
  userAtom,
  appSettingsAtom,
  loadingAtom,
  counterAtom,
  incrementCounterAtom,
} from '../atoms';

// 用户相关hooks
export const useUser = () => useAtom(userAtom);
export const useUserValue = () => useAtomValue(userAtom);
export const useSetUser = () => useSetAtom(userAtom);

// 应用设置相关hooks
export const useAppSettings = () => useAtom(appSettingsAtom);
export const useAppSettingsValue = () => useAtomValue(appSettingsAtom);
export const useSetAppSettings = () => useSetAtom(appSettingsAtom);

// 加载状态hooks
export const useLoading = () => useAtom(loadingAtom);
export const useLoadingValue = () => useAtomValue(loadingAtom);
export const useSetLoading = () => useSetAtom(loadingAtom);

// 计数器示例hooks
export const useCounter = () => useAtom(counterAtom);
export const useCounterValue = () => useAtomValue(counterAtom);
export const useIncrementCounter = () => useSetAtom(incrementCounterAtom);
