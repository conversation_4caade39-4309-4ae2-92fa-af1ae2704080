import {atom, useAtom, useAtomValue, useSetAtom} from 'jotai';
import {ChatMessage, DollInfo, DollRole, Timbre} from '../net';
import {unique} from '../utils/utils';
import {DollDevice} from '../services/DollWiFiService';

// export const checkedDollAtom = atom<DollInfo | null>(null);

// export const checkedRoleAtom = atom<DollRole | null>(null);

export interface DollMsgHistory {
  dollId: string;
  msgList: ChatMessage[];
}

export interface ParentChatMessageWithIt extends ChatMessage {
  it?: AsyncIterable<string>;
}

export interface ParentMsgHistory {
  dollId: string;
  msgList: ParentChatMessageWithIt[];
}

const dollInfosAtom = atom<DollInfo[]>([]);
export const useDollInfosValue = () => {
  return useAtomValue(dollInfosAtom);
};

export const useSetDollInfos = () => {
  return useSetAtom(dollInfosAtom);
};
// export const useCheckedDoll = () => {
//   return useAtom(checkedDollAtom);
// };

const checkedTimbreAtom = atom<{id: string; name: string} | null>(null);
export const useCheckedTimbre = () => {
  return useAtom(checkedTimbreAtom);
};

const timbreListAtom = atom<Timbre[]>([]);
export const useTimbreList = () => {
  return useAtom(timbreListAtom);
};

const dollMsgListAtom = atom<DollMsgHistory>({
  dollId: '',
  msgList: [],
});
export const useDollMsgHistory: () => [
  DollMsgHistory,
  (prev: DollMsgHistory | ((prev: DollMsgHistory) => DollMsgHistory)) => void,
] = () => {
  // return useAtom(dollMsgListAtom);
  const [history, setHistory] = useAtom(dollMsgListAtom);
  return [
    history,
    (prev: DollMsgHistory | ((prev: DollMsgHistory) => DollMsgHistory)) => {
      let newHistory: DollMsgHistory;
      if (typeof prev === 'function') {
        newHistory = prev(history);
      } else {
        newHistory = prev;
      }
      newHistory.msgList = unique(newHistory.msgList, item => item.id);
      newHistory.msgList.sort((a, b) => b.timestamp - a.timestamp);
      setHistory(newHistory);
    },
  ];
};

const parentMsgListAtom = atom<ParentMsgHistory>({
  dollId: '',
  msgList: [],
});
export const useParentMsgHistory: () => [
  ParentMsgHistory,
  (
    prev: ParentMsgHistory | ((prev: ParentMsgHistory) => ParentMsgHistory),
  ) => void,
] = () => {
  // return useAtom(parentMsgListAtom);
  const [history, setHistory] = useAtom(parentMsgListAtom);
  return [
    history,
    (
      newHistory:
        | ParentMsgHistory
        | ((prev: ParentMsgHistory) => ParentMsgHistory),
    ) => {
      setHistory(prev => {
        if (typeof newHistory === 'function') {
          newHistory = newHistory(prev);
        }
        newHistory.msgList = unique(newHistory.msgList, item => item.id);
        newHistory.msgList.sort((a, b) => b.timestamp - a.timestamp);
        return newHistory;
      });
    },
  ];
};

const discoveredDevicesAtom = atom<DollDevice[]>([]);
export const useDiscoveredDevices = () => {
  return useAtom(discoveredDevicesAtom);
};
