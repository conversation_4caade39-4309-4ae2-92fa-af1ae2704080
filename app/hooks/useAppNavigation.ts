import { useNavigation, NavigationProp } from '@react-navigation/native';
import { RootNavigationParamList } from '../navigation/types';

/**
 * Custom navigation hook with proper typing
 * 
 * Usage:
 * ```
 * const navigation = useAppNavigation();
 * 
 * // Navigate to a screen with proper type checking for params
 * navigation.navigate('Home');
 * navigation.navigate('DeviceDetails', { deviceId: '123', name: 'Device Name' });
 * ```
 */
export function useAppNavigation() {
  return useNavigation<NavigationProp<RootNavigationParamList>>();
}

export default useAppNavigation; 