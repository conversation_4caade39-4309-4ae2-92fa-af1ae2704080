import {atom, useAtom, useAtomValue, useSetAtom} from 'jotai';
import {Dimensions} from 'react-native';

const {width, height} = Dimensions.get('window');
export const safeWndAtom = atom({
  width: width,
  height: height,
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
});

export const useSafeWnd = () => useAtom(safeWndAtom);
export const useSafeWndValue = () => useAtomValue(safeWndAtom);
export const useSetSafeWnd = () => useSetAtom(safeWndAtom);
