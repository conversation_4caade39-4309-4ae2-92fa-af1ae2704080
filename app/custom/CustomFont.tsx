import {RefAttributes} from 'react';
import {Text, TextInput, TextInputProps, TextProps} from 'react-native';
import {typography} from '../theme/theme';

export const MyText = (props: TextProps & RefAttributes<Text>) => {
  return (
    <Text
      {...props}
      style={[{fontFamily: typography.fontFamily.regular}, props.style]}
    />
  );
};

export const MyTextInput = (
  props: TextInputProps & RefAttributes<TextInput>,
) => {
  return (
    <TextInput
      {...props}
      style={[{fontFamily: typography.fontFamily.regular}, props.style]}
    />
  );
};

// export class MyText extends Text {
//   constructor(props: TextProps) {
//     super(props);
//     this.setNativeProps({style: [props.style, {fontFamily}]});
//   }
// }
