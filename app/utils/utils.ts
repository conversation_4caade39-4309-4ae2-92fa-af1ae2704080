import Logger from '../services/LoggerService';
import Toast from 'react-native-simple-toast';
import RNFS from 'react-native-fs';
import DeviceInfo from 'react-native-device-info';
export const showToast = (message: string) => {
  Logger.info('showToast', message);
  Toast.showWithGravity(message, Toast.LONG, Toast.CENTER, {
    tapToDismissEnabled: true,
  });
};
export async function fileToBase64(uri: string): Promise<string> {
  try {
    const data = await RNFS.readFile(uri, 'base64');
    return data;
  } catch {
    return '';
  }
}

export function urlEncode(queryString: string): string {
  return encodeURI(queryString);
}

export function GetAppVersion(): {version: string; buildNumber: string} {
  const version = DeviceInfo.getVersion();
  const buildNumber = DeviceInfo.getBuildNumber();
  return {version, buildNumber};
}

// 时间格式化
export function formatTimeChatItem(tsSeconds: number) {
  const d = new Date(tsSeconds * 1000);
  const now = new Date();
  const isToday = d.toDateString() === now.toDateString();

  if (isToday) {
    return `${d.getHours().toString().padStart(2, '0')}:${d.getMinutes().toString().padStart(2, '0')}:${d.getSeconds().toString().padStart(2, '0')}`;
  } else {
    return `${d.getFullYear()}-${(d.getMonth() + 1).toString().padStart(2, '0')}-${d.getDate().toString().padStart(2, '0')} ${d.getHours().toString().padStart(2, '0')}:${d.getMinutes().toString().padStart(2, '0')}:${d.getSeconds().toString().padStart(2, '0')}`;
  }
}

export function unique<T, V = string>(
  arr: T[],
  testUnique?: (item: T) => V,
): T[] {
  // const uniqueMsgs = Array.from(
  //   new Map(arr.map(msg => [testUnique(msg), msg])).values(),
  // );
  const test = testUnique || ((item: T) => item);
  const uniqueMap = new Map<V | T, T>();
  return arr.filter(item => {
    const key = test(item);
    if (!uniqueMap.has(key)) {
      uniqueMap.set(key, item);
      return true;
    }
    return false;
  });
}

type AsyncQueue<T> = AsyncGenerator<T> & {
  push: (value: T | null | undefined) => void;
};
// 异步队列迭代器
export function createAsyncQueue<T>(): AsyncQueue<T> {
  // {
  //   it: AsyncGenerator<T>;
  //   push: (value: T | null | undefined) => void;
  // }
  const queue: (T | null | undefined)[] = [];
  let waitingResolve: ((value: T | null | undefined) => void) | null = null;

  async function* asyncGenerator(): AsyncGenerator<T, void, unknown> {
    while (true) {
      let value: T | null | undefined;
      // 如果队列有数据，直接返回
      if (queue.length > 0) {
        value = queue.shift()!;
      } else {
        // 队列为空，等待新数据
        value = await new Promise<T | null | undefined>(resolve => {
          waitingResolve = resolve;
        });
      }

      // 检查是否结束
      if (value === null || value === undefined) {
        break;
      }

      yield value;
    }
  }

  const push = (value: T | null | undefined) => {
    // 如果有等待中的resolve，直接传递
    if (waitingResolve) {
      waitingResolve(value);
      waitingResolve = null;
    } else {
      // 否则加入队列
      queue.push(value);
    }
  };
  const it = asyncGenerator();
  (it as AsyncQueue<T>).push = push;
  return it as AsyncQueue<T>;

  // return {
  //   it: asyncGenerator(),
  //   push,
  // };
}
