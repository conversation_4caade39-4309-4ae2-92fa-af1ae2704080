// App-wide constants

export const APP_NAME = 'Doll Controller';
export const APP_VERSION = '0.0.1';

// Connection States
export enum ConnectionState {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  ERROR = 'error',
}

// Battery Levels
export enum BatteryLevel {
  CRITICAL = 'critical',
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  FULL = 'full',
}

// Time Constants
export const TIME_CONSTANTS = {
  SPLASH_DURATION: 2500, // milliseconds
  CONNECTION_TIMEOUT: 10000, // milliseconds
  AUTO_DISCONNECT_TIMEOUT: 300000, // 5 minutes
};

// Storage Keys
export const STORAGE_KEYS = {
  USER_SETTINGS: 'user_settings',
  LAST_CONNECTED_DOLL: 'last_connected_doll',
  CONNECTION_HISTORY: 'connection_history',
};

// Default Settings
export const DEFAULT_SETTINGS = {
  hapticFeedback: true,
  soundEnabled: true,
  darkMode: false,
  keepAwake: true,
  autoConnect: false,
};

// API Endpoints (if applicable)
export const API = {
  BASE_URL: 'https://api.dollcontroller.com',
  ENDPOINTS: {
    FIRMWARE: '/firmware',
    UPDATES: '/updates',
    REGISTER: '/auth/register',
    LOGIN: '/auth/login',
  },
};

export default {
  APP_NAME,
  APP_VERSION,
  ConnectionState,
  BatteryLevel,
  TIME_CONSTANTS,
  STORAGE_KEYS,
  DEFAULT_SETTINGS,
  API,
};
