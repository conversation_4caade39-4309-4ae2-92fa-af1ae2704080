import React, {useState, useEffect, useCallback} from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  ActivityIndicator,
} from 'react-native';
import {colors} from '../theme/theme';
import useAppNavigation from '../hooks/useAppNavigation';
import Icon from 'react-native-vector-icons/AntDesign';
import AddDollButtonComponent from '../components/AddDollButtonComponent';
import httpService, {DollInfo} from '../net';
import DollItem from '../components/DollItem';
import {useDollInfosValue, useSetDollInfos} from '../hooks/useAppHooks';
import PermissionChecker from '../components/PermissionChecker';
import {PermissionType} from '../services/PermissionManager';
import Logger from '../services/LoggerService';
import {MyText} from '../custom/CustomFont';
import ScreenPageComponent from '../components/ScreenPageComponent';
import ScalableImage from '../components/ScalableImage';
import {useFocusEffect, useIsFocused} from '@react-navigation/native';
import {unique} from '../utils/utils';
// import Logger from '../services/LoggerService';
// import Toast from 'react-native-root-toast';
const AllDollsScreen: React.FC = () => {
  const navigation = useAppNavigation();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const dollList = useDollInfosValue();
  const setDollInfos = useSetDollInfos();

  const loadDolls = async () => {
    setIsLoading(true);
    try {
      const res = await httpService.doll.getDollInfos();
      if (res.code === 0) {
        const dollList = res.data.dollInfos;
        const uniqueList = unique(dollList, item => item.dollId);
        setDollInfos(uniqueList);
        setError(null);
      } else {
        setError(res.message || '获取设备列表失败');
      }
    } catch (err) {
      Logger.error('加载设备列表失败:', err);
      setError('无法加载设备列表，请稍后重试');
    } finally {
      setIsLoading(false);
    }
  };
  const isFocused = useIsFocused();
  // 加载设备列表
  useFocusEffect(
    useCallback(() => {
      if (isFocused) {
        loadDolls();
      }
    }, [isFocused]),
  );

  const renderDollItem = ({item}: {item: DollInfo}) => {
    return <DollItem item={item} />;
  };

  const handleConnectDoll = () => {
    navigation.navigate('AddDoll');
  };

  const handleOpenProfile = () => {
    navigation.navigate('Profile');
  };

  let pageContent = null;
  // 渲染加载状态
  if (isLoading) {
    pageContent = (
      <View style={[styles.container, styles.centerContent]}>
        <ActivityIndicator size="large" color={colors.primary} />
        <MyText style={styles.loadingText}>加载设备列表中...</MyText>
      </View>
    );
  }
  // 渲染错误状态
  else if (error) {
    pageContent = (
      <View style={[styles.container, styles.centerContent]}>
        <MyText style={styles.errorText}>{error}</MyText>
        <TouchableOpacity
          style={styles.retryButton}
          onPress={() => loadDolls()} // 触发重新加载
        >
          <MyText style={styles.retryButtonText}>重试</MyText>
        </TouchableOpacity>
      </View>
    );
  }
  // 渲染设备列表
  else {
    pageContent = (
      <View style={styles.container}>
        <View style={styles.header}>
          <View style={styles.headerButtons}>
            <TouchableOpacity
              onPress={handleOpenProfile}
              style={styles.profileButton}>
              <ScalableImage
                source={require('../assets/img/btn_profile.png')}
                width={24}
              />
              {/* <Icon name="bars" size={24} onPress={handleOpenProfile}></Icon> */}
            </TouchableOpacity>
            <MyText style={styles.title}>全部设备</MyText>
            <TouchableOpacity
              style={styles.addButton}
              onPress={handleConnectDoll}>
              <Icon name="pluscircleo" size={24}></Icon>
            </TouchableOpacity>
          </View>
        </View>
        {(!dollList || dollList.length === 0) && <AddDollButtonComponent />}
        {dollList && dollList.length > 0 && (
          <FlatList
            data={dollList}
            renderItem={renderDollItem}
            keyExtractor={item => item.dollId}
            contentContainerStyle={styles.listContent}
            showsVerticalScrollIndicator={false}
          />
        )}
      </View>
    );
  }
  return (
    <ScreenPageComponent bgType="bg_doll">
      {pageContent}
      {/* 添加权限检查器组件，这是一个隐形组件，不会显示在UI上 */}
      <PermissionChecker
        permissions={[
          PermissionType.BLUETOOTH,
          PermissionType.LOCATION,
          PermissionType.MICROPHONE,
          // PermissionType.CAMERA,
          PermissionType.MEDIA_LIBRARY,
        ]}
        autoRequest={true}
        permissionMessages={{
          [PermissionType.BLUETOOTH]: '需要蓝牙权限来连接和控制设备',
          [PermissionType.LOCATION]:
            '需要位置权限来发现附近的蓝牙设备和WiFi网络',
        }}
      />
    </ScreenPageComponent>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: colors.textSecondary,
  },
  errorText: {
    fontSize: 16,
    color: colors.error,
    marginBottom: 16,
    textAlign: 'center',
  },
  retryButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    backgroundColor: colors.primary,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: 12,
    paddingBottom: 8,
    // borderBottomWidth: 1,
    // borderBottomColor: colors.divider,
  },
  headerButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
  },
  profileButton: {
    position: 'absolute',
    left: 0,
    paddingVertical: 0,
    // paddingHorizontal: 12,
    borderRadius: 16,
    // backgroundColor: '#f5f3ff',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text,
    textAlign: 'center',
  },
  addButton: {
    position: 'absolute',
    right: 0,
    width: 40,
    height: 40,
    borderRadius: 20,
    // backgroundColor: '#f5f3ff',
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContent: {
    padding: 16,
  },
});

export default AllDollsScreen;
