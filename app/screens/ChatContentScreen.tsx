import React, {useCallback, useRef, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  ScrollView,
} from 'react-native';
import type {BaseResponse, DollInfo, ParentChatResponse} from '../net/types';
import ChatHeaderComponent from '../components/ChatHeaderComponent';
import ChatContentComponent from '../components/ChatContentComponent';
import InputSwitchComponent from '../components/InputSwitchComponent';
import Icon from 'react-native-vector-icons/AntDesign';
import {useNavigation} from '@react-navigation/native';
import BackLeftBtnComponent from '../components/BackLeftBtnComponent';
import Logger from '../services/LoggerService';
import {useParentMsgHistory} from '../hooks/useAppHooks';
import {MyText} from '../custom/CustomFont';
import ScreenPageComponent from '../components/ScreenPageComponent';
import MyPrimaryButton from '../components/MyPrimaryButton';
import {colors} from '../theme/theme';
import {ThemeCss} from '../theme/css';
import httpService from '../net';
import {createAsyncQueue} from '../utils/utils';
interface Props {
  route: {params: {doll: DollInfo}};
}

const ChatContentScreen: React.FC<Props> = ({route}) => {
  const navigation = useNavigation();
  const {doll} = route.params;
  const [tab, setTab] = useState<'doll' | 'parent'>('doll');
  const [, setParentMsgHistory] = useParentMsgHistory();

  const parentChatComponentRef = useRef<{
    onChatSSE: (queryType: 'text' | 'voice', query: string) => void;
  }>(null);

  const handleSendChatMsg = async (
    queryType: 'text' | 'voice',
    query: string,
  ) => {
    Logger.info('chatParentSSE queryType', queryType);
    Logger.info('query', query);
    const dollId = doll.dollId;
    if (!dollId) {
      return;
    }
    try {
      // 调用SSE接口发送聊天消息
      const sse = await httpService.msg.chatParentSSE({
        dollId,
        queryType,
        query,
      });
      // 处理流式响应
      try {
        if (parentChatComponentRef.current) {
          parentChatComponentRef.current.onChatSSE(queryType, query);
        }
        Logger.info('ChatContentScreen', 'chat method called');

        let fullAnswer = '';
        const first = await sse.iterator.next();
        if (first.done || !first.value) {
          Logger.error('ChatContentScreen', 'SSE connection value is null');
          return;
        }
        fullAnswer = first.value.data.answer;
        const it = createAsyncQueue<string>();
        it.push(fullAnswer);
        setParentMsgHistory(prev => {
          if (!dollId) {
            return prev;
          }
          return {
            dollId,
            msgList: [
              {
                id: first.value.data.id,
                query: first.value.data.query,
                shield: first.value.data.shield,
                timestamp: first.value.data.timestamp,
                answer: first.value.data.answer,
                it,
              },
              ...prev.msgList,
            ],
          };
        });

        if (!first.value.data.isDone) {
          for await (const response of sse.iterator) {
            if (response.code === 0) {
              const msg = response.data;

              fullAnswer += msg.answer;
              it.push(fullAnswer);
              if (msg.isDone) {
                break;
              }
            } else {
              Logger.error(
                'ChatContentScreen',
                'Error in SSE stream1:',
                response,
              );
              break;
            }
          }
        }
        setParentMsgHistory(prev => {
          if (!dollId) {
            return prev;
          }
          const newMsg = {
            id: first.value.data.id,
            query: first.value.data.query,
            shield: first.value.data.shield,
            timestamp: first.value.data.timestamp,
            answer: fullAnswer,
          };
          if (prev.msgList.length > 0 && prev.msgList[0].id === newMsg.id) {
            prev.msgList[0] = newMsg;
          } else {
            prev.msgList.unshift(newMsg);
          }

          return {
            dollId,
            msgList: [...prev.msgList],
          };
        });
        // 结束标志
        it.push(null);
      } catch (error) {
        Logger.error('ChatContentScreen', 'Error in SSE stream2:', error);
      } finally {
        sse.eventSource?.close();
      }
    } catch (error: any) {
      Logger.error(error.stack);
      Logger.error('ChatContentScreen', 'Error in chat method:', error);
    }
  };

  const handleRoleChange = () => {
    navigation.navigate('RoleSwitch', {dollId: doll.dollId});
  };

  return (
    <ScreenPageComponent
      style={styles.container}
      bgType="bg_chat"
      keyboardAvoiding={true}>
      {/* 顶部页签 */}
      <View style={styles.tabRow}>
        <BackLeftBtnComponent />
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'center',
            alignItems: 'center',
          }}>
          <TouchableOpacity
            style={[styles.tabBtn]}
            onPress={() => setTab('doll')}>
            <MyText
              style={[styles.tabText, tab === 'doll' && styles.tabTextActive]}>
              聊天内容
            </MyText>
            <View
              style={[
                styles.tabBtnActiveLine,
                tab !== 'doll' && ThemeCss.isHidden,
              ]}
            />
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tabBtn]}
            onPress={() => setTab('parent')}>
            <MyText
              style={[
                styles.tabText,
                tab === 'parent' && styles.tabTextActive,
              ]}>
              家长助手
            </MyText>
            <View
              style={[
                styles.tabBtnActiveLine,
                tab !== 'parent' && ThemeCss.isHidden,
              ]}
            />
          </TouchableOpacity>
        </View>
      </View>
      {/* 内容区 */}
      <View
        style={[
          styles.pageContent,
          tab !== 'doll' && ThemeCss.isHiddenMoveOut,
        ]}>
        <ChatHeaderComponent doll={doll} mode="summary" />
        <View style={styles.flex1}>
          <ChatContentComponent
            type="doll"
            dollId={doll.dollId}
            curTabType={tab}
          />
        </View>
        <MyPrimaryButton onPress={handleRoleChange} style={styles.roleBtn}>
          <Icon
            name="swap"
            style={{marginRight: 3}}
            color={colors.butPurpleText}
            size={20}
          />
          <MyText style={styles.roleBtnText}>角色切换</MyText>
        </MyPrimaryButton>
      </View>
      <View
        style={[
          styles.pageContent,
          tab !== 'parent' && ThemeCss.isHiddenMoveOut,
        ]}>
        <ChatHeaderComponent doll={doll} mode="status" />
        <View style={styles.flex1}>
          <ChatContentComponent
            ref={parentChatComponentRef}
            type="parent"
            dollId={doll.dollId}
            curTabType={tab}
          />
        </View>
        <InputSwitchComponent
          onTextSend={text => {
            handleSendChatMsg('text', text);
          }}
          onVoiceSend={voice => {
            handleSendChatMsg('voice', voice);
          }}
        />
      </View>
    </ScreenPageComponent>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // backgroundColor: '#F6FAFF',
    paddingTop: 0,
  },
  tabRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'transparent',
    marginTop: 8,
    marginBottom: 0,
  },
  tabBtn: {
    // flex: 1,
    alignItems: 'center',
    paddingVertical: 10,
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
    marginHorizontal: 10,
  },
  tabBtnActive: {
    borderBottomColor: colors.butPurpleBorder,
  },
  tabText: {
    fontSize: 17,
    color: '#888',
    // fontWeight: 'light',
  },
  tabTextActive: {
    color: colors.text,
    fontWeight: 'bold',
  },
  tabBtnActiveLine: {
    width: '100%',
    height: 4,
    backgroundColor: '#E3BFFFBB',
    marginTop: -5,
  },
  pageContent: {
    flex: 1,
    paddingTop: 0,
  },
  flex1: {
    flex: 1,
  },
  roleBtn: {
    margin: 18,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: colors.butPurpleBorder,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
  },
  roleBtnText: {
    color: colors.butPurpleText,
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default ChatContentScreen;
