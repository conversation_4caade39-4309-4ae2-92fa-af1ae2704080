import React, {useEffect} from 'react';
import {
  View,
  StyleSheet,
  Animated,
  Dimensions,
  Text,
  Image,
} from 'react-native';
import {APP_NAME, APP_VERSION, TIME_CONSTANTS} from '../constants/appConstants';
import {colors} from '../theme/theme';
import useAppNavigation from '../hooks/useAppNavigation';
import {useSetLoading, useLoadingValue} from '../hooks/useAtoms';
import ScreenPageComponent from '../components/ScreenPageComponent';
import ScalableImage from '../components/ScalableImage';
import authTokenService from '../net/services/authTokenService';
import Logger from '../services/LoggerService';
import {MyText} from '../custom/CustomFont';
import AppBackgroundImg from '../components/AppBackgroudImg';
import {GetAppVersion} from '../utils/utils';

const SplashScreen: React.FC = () => {
  const navigation = useAppNavigation();
  const fadeAnim = React.useRef(new Animated.Value(0)).current;
  const scaleAnim = React.useRef(new Animated.Value(0.9)).current;
  const setLoading = useSetLoading();
  const isLoading = useLoadingValue();

  // const setSafeWnd = useSetAtom(safeWndAtom);
  // const [safeWnd, setSafeWnd] = useAtom(safeWndAtom);
  // const safeAreaInsets = useSafeAreaInsets();

  const handleNavigate = () => {
    authTokenService
      .isTokenValid()
      .then(isLoggedIn => {
        if (isLoggedIn) {
          Logger.info('SplashScreen', '已登录');
          navigation.reset({
            index: 0,
            routes: [{name: 'AllDolls'}],
          });
        } else {
          throw new Error('未登录');
        }
      })
      .catch(err => {
        Logger.info('SplashScreen', err);
        navigation.reset({
          index: 0,
          routes: [{name: 'LoginRegister'}],
        });
      });
  };

  useEffect(() => {
    // 设置加载状态为true
    setLoading(true);

    // Animation sequence
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 8,
        tension: 40,
        useNativeDriver: true,
      }),
    ]).start();

    // Navigate to main app after splash duration
    const timer = setTimeout(() => {
      // 设置加载状态为false
      setLoading(false);
      handleNavigate();
    }, TIME_CONSTANTS.SPLASH_DURATION);

    return () => clearTimeout(timer);
  }, [fadeAnim, scaleAnim, navigation, setLoading]);

  const {width, height} = Dimensions.get('window');

  const {version, buildNumber} = GetAppVersion();
  return (
    <View style={styles.container}>
      <AppBackgroundImg type="bg_splash" />
      <Animated.View
        style={[
          styles.logoContainer,
          {
            opacity: fadeAnim,
            transform: [{scale: scaleAnim}],
          },
        ]}>
        <ScalableImage
          source={require('../assets/img/group_260_3x.png')}
          width={width * 0.4}
          style={{marginBottom: height * 0.4}}
        />
      </Animated.View>
      <View style={styles.versionContainer}>
        <MyText style={styles.appName}>{APP_NAME}</MyText>
        <MyText style={styles.version}>
          v{version}.{buildNumber}
        </MyText>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    // backgroundColor: colors.background,
  },
  logoContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1,
  },
  versionContainer: {
    // 在屏幕右下角显示
    position: 'absolute',
    bottom: 40,
    right: 20,
    alignItems: 'flex-end',
  },
  appName: {
    marginTop: 8,
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.text,
  },
  version: {
    marginTop: 8,
    fontSize: 13,
    color: colors.textSecondary,
  },
});

export default SplashScreen;
