import React, {useCallback, useEffect, useState} from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Image,
  ScrollView,
} from 'react-native';
import {MyText} from '../custom/CustomFont';
import {colors, spacing, typography, borderRadius} from '../theme/theme';
import useAppNavigation from '../hooks/useAppNavigation';
import Icon from 'react-native-vector-icons/AntDesign';
import ScreenPageComponent from '../components/ScreenPageComponent';
import DollDiscoveryModal from '../components/DollDiscoveryModal';
import Logger from '../services/LoggerService';
import {useDiscoveredDevices, useDollInfosValue} from '../hooks/useAppHooks';
import {useDollScanner} from '../hooks/useDollWiFi';
import {useFocusEffect} from '@react-navigation/native';

// 定义连接步骤的状态
interface ConnectionStep {
  id: number;
  text: string;
  completed: boolean;
}
const StepsInitial = [
  {
    id: 1,
    text: '开启玩偶电源，确认玩偶已开启',
    completed: false,
  },
  {
    id: 2,
    text: '打开手机蓝牙，将手机靠近玩偶',
    completed: false,
  },
  {
    id: 3,
    text: '选择已搜索到的FamilyRobot设备',
    completed: false,
  },
  {
    id: 4,
    text: '请输入Wi-Fi名称与密码',
    completed: false,
  },
  {
    id: 5,
    text: '连接成功',
    completed: false,
  },
];

const AddDollScreen: React.FC = () => {
  const navigation = useAppNavigation();
  const [discoveryModalVisible, setDiscoveryModalVisible] = useState(false);
  const [discoveredDevices, setDiscoveredDevices] = useDiscoveredDevices();

  // 使用玩偶扫描Hook
  const {isScanning, devices, startScan, stopScan} = useDollScanner();
  // 连接步骤状态管理
  const [connectionSteps, setConnectionSteps] =
    useState<ConnectionStep[]>(StepsInitial);

  const handleBack = () => {
    setDiscoveredDevices([]);
    navigation.goBack();
  };

  // 切换步骤完成状态
  const toggleStepCompletion = (stepId: number) => {
    setConnectionSteps(prevSteps =>
      prevSteps.map(step =>
        step.id === stepId ? {...step, completed: !step.completed} : step,
      ),
    );
  };

  useFocusEffect(
    useCallback(() => {
      if (discoveredDevices.length > 0) {
        Logger.info('discoveredDevices', {discoveredDevices});
        setDiscoveryModalVisible(true);
      } else {
        setDiscoveryModalVisible(false);
      }
      return () => {
        setDiscoveryModalVisible(false);
      };
    }, [discoveredDevices]),
  );

  // 监听扫描到的设备，更新发现列表
  useEffect(() => {
    const hasIds = discoveredDevices.map(d => d.id);
    const newDevices = devices.filter(device => !hasIds.includes(device.id));

    if (newDevices.length > 0) {
      Logger.info('发现新的玩偶设备', {
        newDevices,
        totalDiscovered: discoveredDevices.length + newDevices.length,
      });
      setDiscoveredDevices(prev => [...prev, ...newDevices]);
    }
  }, [devices]);

  // 开始蓝牙扫描
  useFocusEffect(
    useCallback(() => {
      const startBluetoothScan = async () => {
        try {
          await startScan();
        } catch (error: any) {
          Logger.error('启动蓝牙扫描失败', {error});
        }
      };

      // 延迟1秒后开始扫描，给用户一些准备时间
      const timer = setTimeout(startBluetoothScan, 1000);

      return () => {
        clearTimeout(timer);
        // 组件卸载时停止扫描
        stopScan().catch((error: any) => {
          Logger.error('组件卸载时停止扫描失败', {error});
        });
      };
    }, [startScan, stopScan]),
  );

  // useEffect(() => {
  //   setTimeout(() => {
  //     setDiscoveryModalVisible(true);
  //   }, 3000);
  // }, []);

  return (
    <ScreenPageComponent style={styles.container} bgType="bg_login">
      {/* 头部区域 */}
      <View style={styles.header}>
        <TouchableOpacity onPress={handleBack} style={styles.backButton}>
          <Icon name="left" size={24} color={colors.text} />
        </TouchableOpacity>
        <MyText style={styles.headerTitle}>连接设备</MyText>
        <View style={styles.placeholder} />
      </View>

      {/* 白色卡片容器 */}
      <View style={styles.deviceCard}>
        {/* 标题 */}
        <MyText style={styles.deviceTitle}>确保小兔子玩偶已开启</MyText>

        {/* 玩偶图片展示区域 */}
        <View style={styles.deviceImageContainer}>
          <View style={styles.deviceImageWrapper}>
            <MyText style={styles.placeholderText}>玩偶开关图片展示</MyText>
          </View>
          <MyText style={styles.imageDescription}>按住电源键3秒开机</MyText>
        </View>

        {/* 扫描状态提示 */}
        {/* {isScanning && (
          <View style={styles.scanningIndicator}>
            <MyText style={styles.scanningText}>正在扫描蓝牙设备...</MyText>
            <MyText style={styles.scanningSubText}>
              已发现 {devices.length} 个设备
            </MyText>
          </View>
        )} */}

        {/* 连接步骤列表 */}
        <View style={styles.stepsContainer}>
          {connectionSteps.map(step => (
            <View
              key={step.id}
              style={styles.stepItem}
              // onPress={() => toggleStepCompletion(step.id)}
              // activeOpacity={0.7}
            >
              <Image
                source={
                  step.completed
                    ? require('../assets/img/lujing_4684_3x.png')
                    : require('../assets/img/lujing_4686_3x_1_.png')
                }
                style={styles.checkboxIcon}
                resizeMode="contain"
              />
              <MyText style={styles.stepText}>{step.text}</MyText>
            </View>
          ))}
        </View>
      </View>
      {discoveryModalVisible && (
        <DollDiscoveryModal
          visible={discoveryModalVisible}
          onCancel={() => {
            setDiscoveryModalVisible(false);
          }}
          onStartBinding={dollId => {
            setDiscoveryModalVisible(false);
            navigation.navigate('DollConnectWifi', {dollId});
          }}
        />
      )}
      {/* <TestDollDiscovery /> */}
    </ScreenPageComponent>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.md,
    paddingTop: spacing.sm,
    paddingBottom: spacing.sm,
    // borderBottomWidth: 1,
    // borderBottomColor: colors.divider,
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: '600' as const,
    color: colors.text,
  },
  placeholder: {
    width: 40,
  },
  // content: {
  //   padding: spacing.md,
  //   alignItems: 'center',
  // },
  deviceCard: {
    flex: 1,
    backgroundColor: '#ffffff',
    borderRadius: borderRadius.lg,
    padding: spacing.xl,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    borderWidth: 1,
    borderColor: colors.primary,
    margin: 10,
    marginBottom: 30,
  },
  deviceTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: '600' as const,
    color: colors.primary,
    marginBottom: spacing.xl,
    textAlign: 'center',
  },
  deviceImageContainer: {
    width: '100%',
    alignItems: 'center',
    marginBottom: spacing.xl,
  },
  deviceImageWrapper: {
    width: '100%',
    height: 200,
    backgroundColor: '#E8E1FF',
    borderRadius: borderRadius.md,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  placeholderText: {
    fontSize: typography.fontSize.md,
    color: colors.textSecondary,
    textAlign: 'center',
  },
  imageDescription: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
    textAlign: 'center',
    marginTop: -35,
  },
  stepsContainer: {
    width: '100%',
  },
  stepItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.xs,
  },
  checkboxIcon: {
    width: 20,
    height: 20,
    marginRight: spacing.sm,
  },
  stepText: {
    flex: 1,
    fontSize: typography.fontSize.md,
    color: colors.text,
    lineHeight: 20,
  },
  scanningIndicator: {
    width: '100%',
    backgroundColor: 'rgba(115, 54, 246, 0.1)',
    borderRadius: borderRadius.md,
    padding: spacing.md,
    marginBottom: spacing.lg,
    alignItems: 'center',
  },
  // scanningText: {
  //   fontSize: typography.fontSize.md,
  //   color: colors.primary,
  //   fontWeight: '500' as const,
  //   marginBottom: spacing.xs,
  // },
  // scanningSubText: {
  //   fontSize: typography.fontSize.sm,
  //   color: colors.textSecondary,
  // },
  testButton: {
    backgroundColor: colors.secondary,
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md,
    borderRadius: borderRadius.md,
    marginBottom: spacing.md,
  },
  testButtonText: {
    fontSize: typography.fontSize.sm,
    color: '#ffffff',
    textAlign: 'center',
  },
});

export default AddDollScreen;
