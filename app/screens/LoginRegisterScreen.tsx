import React, {useEffect, useState} from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import {colors} from '../theme/theme';
import useAppNavigation from '../hooks/useAppNavigation';
import httpService from '../net';
import authTokenService from '../net/services/authTokenService';
import {showToast} from '../utils/utils';
import Logger from '../services/LoggerService';
import {useSafeWndValue} from '../hooks/useSafeWnd';
import ScalableImage from '../components/ScalableImage';
import {MyText, MyTextInput} from '../custom/CustomFont';
import ScreenPageComponent from '../components/ScreenPageComponent';
import MyPrimaryButton from '../components/MyPrimaryButton';

const LoginRegisterScreen: React.FC = () => {
  const navigation = useAppNavigation();
  const [phone, setPhone] = useState('');
  const [code, setCode] = useState('');
  const [agreed, setAgreed] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [loading, setLoading] = useState(false);
  const [countryCode, setCountryCode] = useState('86');
  const [isEditingCode, setIsEditingCode] = useState(false);
  const {width, height} = useSafeWndValue();

  useEffect(() => {
    authTokenService.isTokenValid().then(isLoggedIn => {
      if (isLoggedIn) {
        navigation.reset({
          index: 0,
          routes: [{name: 'AllDolls'}],
        });
      }
    });
  }, []);

  const handleSendCode = () => {
    if (!/^1[3-9]\d{9}$/.test(phone)) {
      showToast('请输入有效的手机号');
      return;
    }
    // 模拟验证码发送
    const timer = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
    setCountdown(60);

    httpService.login
      .getVerifyCode({
        area: countryCode,
        phone,
      })
      .then(res => {
        console.log(res);
      })
      .catch(err => {
        Logger.error(err);
        const msg =
          typeof err === 'string' ? err : err?.message || '获取验证码失败';
        showToast(msg);
      });
  };

  const handleLogin = () => {
    if (!/^1[3-9]\d{9}$/.test(phone)) {
      showToast('请输入有效的手机号');
      return;
    }
    if (!code) {
      showToast('请输入验证码');
      return;
    }
    if (!agreed) {
      showToast('请勾选同意隐私政策和用户协议');
      return;
    }
    setLoading(true);
    httpService.login
      .verifyCode({
        area: countryCode,
        phone,
        verifycode: code,
      })
      .then(res => {
        if (res.code === 0) {
          navigation.reset({
            index: 0,
            routes: [{name: 'AllDolls'}],
          });
        } else {
          showToast(res.message || '登录失败');
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 处理国家代码点击事件
  const handleCountryCodePress = () => {
    setIsEditingCode(true);
  };

  // 处理国家代码输入完成事件
  const handleCountryCodeBlur = () => {
    setIsEditingCode(false);
    // 确保国家代码始终以+开头
    // if (countryCode && !countryCode.startsWith('+')) {
    //   setCountryCode(`+${countryCode}`);
    // }
    if (!countryCode || countryCode === '') {
      setCountryCode('86');
    }
  };

  return (
    <ScreenPageComponent
      style={{flex: 1}}
      bgType="bg_login"
      keyboardAvoiding={true}>
      <View style={styles.logoContainer}>
        {/* <View style={styles.logoPlaceholder}>
          <MyText style={styles.logoText}>App Logo</MyText>
        </View>
        <MyText style={styles.appName}>FamilyRobot</MyText> */}

        <ScalableImage
          source={require('../assets/img/group_260_3x.png')}
          width={width * 0.4}
        />
      </View>
      <View style={styles.form}>
        <View style={styles.inputContainer}>
          <MyText style={styles.prefix}>+</MyText>
          {isEditingCode ? (
            <MyTextInput
              style={styles.countryCodeInput}
              value={countryCode}
              onChangeText={setCountryCode}
              keyboardType="phone-pad"
              onBlur={handleCountryCodeBlur}
              autoFocus
              maxLength={4}
            />
          ) : (
            <TouchableOpacity
              style={styles.countryCodeContainer}
              onPress={handleCountryCodePress}>
              <MyText style={styles.prefix}>{countryCode}</MyText>
            </TouchableOpacity>
          )}
          <MyTextInput
            style={styles.input}
            placeholder="请输入手机号"
            placeholderTextColor={colors.textSecondaryLight}
            keyboardType="phone-pad"
            maxLength={11}
            value={phone}
            onChangeText={setPhone}
          />
        </View>
        <View style={styles.inputContainer}>
          <View style={styles.verificationCodeLabelContainer}>
            <MyText style={styles.verificationCodeLabel}>验证码</MyText>
          </View>
          <MyTextInput
            style={[styles.input, styles.codeInput]}
            placeholder="请输入验证码"
            placeholderTextColor={colors.textSecondaryLight}
            keyboardType="number-pad"
            maxLength={6}
            value={code}
            onChangeText={setCode}
          />
          <TouchableOpacity
            style={styles.codeBtn}
            disabled={countdown > 0}
            onPress={handleSendCode}>
            <MyText style={{color: colors.primary}}>
              {countdown > 0 ? `${countdown}s` : '获取验证码'}
            </MyText>
          </TouchableOpacity>
        </View>
        <View style={styles.agreeRow}>
          <TouchableOpacity
            onPress={() => setAgreed(v => !v)}
            style={styles.checkbox}>
            <View
              style={[styles.checkboxBox, agreed && styles.checkboxChecked]}
            />
          </TouchableOpacity>
          <MyText style={styles.agreeText}>
            已阅读并同意
            <MyText style={styles.link}>《隐私政策》</MyText>和
            <MyText style={styles.link}>《用户协议》</MyText>
          </MyText>
        </View>
        <MyPrimaryButton onPress={handleLogin} disabled={loading || !agreed}>
          <MyText style={{color: colors.butPurpleText}}>
            {loading ? '登录中...' : '立即登录'}
          </MyText>
        </MyPrimaryButton>
      </View>
    </ScreenPageComponent>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // backgroundColor: colors.background,
    alignItems: 'center',
    justifyContent: 'center',
  },
  logoContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    // marginBottom: 40,
  },
  // logoPlaceholder: {
  //   width: 100,
  //   height: 100,
  //   borderRadius: 50,
  //   backgroundColor: colors.primary,
  //   justifyContent: 'center',
  //   alignItems: 'center',
  // },
  logoText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  appName: {
    marginTop: 16,
    fontSize: 22,
    fontWeight: 'bold',
    color: colors.text,
  },
  form: {
    flex: 1,
    paddingHorizontal: 36,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 18,
    width: '100%',
    borderBottomWidth: 1,
    borderBottomColor: colors.divider,
  },
  countryCodeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingRight: 8,
    minWidth: 40,
  },
  countryCodeInput: {
    width: 40,
    height: 44,
    fontSize: 16,
    paddingHorizontal: 0,
  },
  prefix: {
    fontSize: 16,
    color: colors.textSecondary,
  },
  verificationCodeLabelContainer: {
    marginRight: 8,
  },
  verificationCodeLabel: {
    fontSize: 14,
    color: colors.textSecondary,
  },
  input: {
    flex: 1,
    height: 44,
    // textAlignVertical: 'bottom',
    margin: 0,
    padding: 0,
    paddingHorizontal: 0,
    fontSize: 16,
    backgroundColor: 'transparent',
  },
  codeInput: {
    flex: 1,
  },
  codeBtn: {
    position: 'absolute',
    right: 0,
    marginLeft: 12,
    paddingHorizontal: 10,
    paddingVertical: 6,
  },
  agreeRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
    marginTop: 80,
  },
  checkbox: {
    marginRight: 8,
  },
  checkboxBox: {
    width: 18,
    height: 18,
    borderWidth: 1,
    borderColor: colors.primary,
    borderRadius: 9,
    backgroundColor: '#fff',
  },
  checkboxChecked: {
    backgroundColor: colors.primary,
  },
  agreeText: {
    fontSize: 14,
    color: colors.textSecondary,
  },
  link: {
    color: colors.primary,
  },
});

export default LoginRegisterScreen;
