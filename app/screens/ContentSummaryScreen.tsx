import React, {useEffect, useState, useCallback} from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  RefreshControl,
  ImageBackground,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import httpService from '../net/services/httpService';
import {useDollInfosValue} from '../hooks/useAppHooks';
import ScalableImage from '../components/ScalableImage';
import {dollService} from '../services/dollService';
import BackLeftBtnComponent from '../components/BackLeftBtnComponent';
import {MyText} from '../custom/CustomFont';
import ScreenPageComponent from '../components/ScreenPageComponent';

const titleImgs = [
  require('../assets/img/group_535_3x.png'),
  require('../assets/img/group_536_3x.png'),
  require('../assets/img/group_537_3x.png'),
  require('../assets/img/group_538_3x.png'),
  require('../assets/img/group_539_3x.png'),
];

const bgHeaderImg = require('../assets/img/summary_bg_header.png');
const bgContentImg = require('../assets/img/summary_bg_content.png');

interface MsgSummaryResponse {
  mainContent: string[];
  emotion: string;
  AIAddiction: string[];
  negativeContent: string[];
  statistics: string;
}

interface ContentSummaryScreenProps {
  route: {
    params: {
      dollId: string;
    };
  };
}
const ContentSummaryScreen: React.FC<ContentSummaryScreenProps> = ({route}) => {
  const navigation = useNavigation();
  const {dollId} = route.params;
  const dollList = useDollInfosValue();
  const doll = dollList.find(d => d.dollId === dollId);

  const [summary, setSummary] = useState<MsgSummaryResponse | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  const avatarSource = dollService.getDollAvatar(doll?.dollType);

  const fetchSummary = useCallback(async () => {
    if (!doll) {
      return;
    }
    setRefreshing(true);
    try {
      const res = await httpService.msg.getMsgSummary({dollId: doll.dollId});
      // if (!res.data.mainContent) {
      //   res.data.mainContent = (res.data as any)['main_content'];
      // }
      setSummary(res.data);
    } catch (e) {
      setSummary(null);
    }
    setRefreshing(false);
  }, [doll?.dollId]);

  useEffect(() => {
    fetchSummary();
  }, [fetchSummary]);

  return (
    <ScreenPageComponent style={styles.container} bgType="bg_chat">
      {/* 顶部header */}
      <View style={styles.headerWrap}>
        <BackLeftBtnComponent />
        <MyText style={styles.headerTitle}>内容总结</MyText>
      </View>
      <View style={styles.cardWrap}>
        <ImageBackground
          source={bgHeaderImg}
          style={styles.bgHeader}
          resizeMode="stretch">
          <View style={styles.avatarHeader}>
            <ScalableImage source={avatarSource} width={60}></ScalableImage>
          </View>
        </ImageBackground>
        <ImageBackground
          source={bgContentImg}
          style={styles.bgContent}
          resizeMode="stretch">
          <ScrollView
            style={styles.scroll}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={fetchSummary}
              />
            }
            contentContainerStyle={{paddingBottom: 32}}>
            <View style={styles.cardContent}>
              {/* 严重负面内容 */}
              {(summary?.negativeContent?.length &&
                summary?.negativeContent?.length > 0 && (
                  <View style={styles.sectionRow}>
                    <ScalableImage
                      source={titleImgs[0]}
                      height={22}
                      style={styles.titleImg}
                    />
                    <View style={styles.sectionList}>
                      {summary.negativeContent.map((item, idx) => (
                        <MyText
                          style={styles.sectionListItem}
                          key={idx}>{`${idx + 1}. ${item}`}</MyText>
                      ))}
                    </View>
                  </View>
                )) || <></>}

              {/* 过度依赖AI */}
              {(summary?.AIAddiction?.length &&
                summary?.AIAddiction?.length > 0 && (
                  <View style={styles.sectionRow}>
                    <ScalableImage
                      source={titleImgs[1]}
                      height={22}
                      style={styles.titleImg}
                    />
                    <View style={styles.sectionList}>
                      {summary.AIAddiction.map((item, idx) => (
                        <MyText
                          style={styles.sectionListItem}
                          key={idx}>{`${idx + 1}. ${item}`}</MyText>
                      ))}
                    </View>
                  </View>
                )) || <></>}

              {/* 对话记录 */}
              {(summary?.mainContent?.length &&
                summary?.mainContent?.length > 0 && (
                  <View style={styles.sectionRow}>
                    <ScalableImage
                      source={titleImgs[2]}
                      height={22}
                      style={styles.titleImg}
                    />
                    <View style={styles.sectionList}>
                      {summary.mainContent.map((item, idx) => (
                        <MyText
                          style={styles.sectionListItem}
                          key={idx}>{`${idx + 1}. ${item}`}</MyText>
                      ))}
                    </View>
                  </View>
                )) || <></>}
              {/* 情绪状态 */}
              {(summary?.emotion && (
                <View style={styles.sectionRow}>
                  <ScalableImage
                    source={titleImgs[3]}
                    height={22}
                    style={styles.titleImg}
                  />
                  <MyText style={styles.sectionText}>{summary?.emotion}</MyText>
                </View>
              )) || <></>}
              {/* 使用情况统计 */}
              {(summary?.statistics && (
                <View style={styles.sectionRow}>
                  <ScalableImage
                    source={titleImgs[4]}
                    height={22}
                    style={styles.titleImg}
                  />
                  <MyText style={styles.sectionText}>
                    {summary?.statistics}
                  </MyText>
                </View>
              )) || <></>}
            </View>
          </ScrollView>
        </ImageBackground>
      </View>
    </ScreenPageComponent>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // backgroundColor: '#F6FAFF',
  },
  headerWrap: {
    height: 60,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
    marginBottom: 8,
  },
  headerBg: {
    ...StyleSheet.absoluteFillObject,
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  closeBtn: {
    position: 'absolute',
    left: 16,
    top: 12,
    zIndex: 2,
    width: 36,
    height: 36,
    alignItems: 'center',
    justifyContent: 'center',
  },
  closeText: {
    fontSize: 28,
    color: '#223366',
    fontWeight: 'bold',
  },
  headerTitle: {
    fontSize: 19,
    color: '#223366',
    fontWeight: 'bold',
    zIndex: 2,
  },
  scroll: {
    flex: 1,
  },
  cardWrap: {
    margin: 16,
    marginBottom: 25,
    borderRadius: 18,
    flex: 1,
    paddingTop: 0,
  },
  avatarHeader: {
    height: 80,
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    overflow: 'hidden',
    paddingTop: 74,
    paddingBottom: 80,
    paddingRight: 20,
    marginTop: -40,
    marginRight: -10,
  },
  cardBg: {
    ...StyleSheet.absoluteFillObject,
    display: 'flex',
  },
  bgHeader: {
    width: '100%',
    height: 74,
  },
  bgContent: {
    flex: 1,
    // width: '100%',
    // height: '100%',
  },
  cardContent: {
    padding: 18,
    paddingTop: 4,
    zIndex: 1,
    overflow: 'hidden',
    position: 'relative',
  },
  sectionRow: {
    marginTop: 16,
    marginBottom: 2,
  },
  titleImg: {
    marginBottom: 4,
  },
  sectionList: {
    marginLeft: 20,
    marginTop: 2,
  },
  sectionListItem: {
    color: '#223366',
    fontSize: 15,
    marginBottom: 2,
  },
  sectionText: {
    color: '#223366',
    fontSize: 15,
    marginLeft: 20,
    marginTop: 2,
  },
});

export default ContentSummaryScreen;
