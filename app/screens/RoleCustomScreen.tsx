import React, {useEffect, useState, useRef} from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  Alert,
} from 'react-native';
import {colors, borderRadius} from '../theme/theme';
import useAppNavigation from '../hooks/useAppNavigation';
import Dialog from '../components/Dialog';
import httpService, {DollRole, RoleGenResponse} from '../net';
import {
  useCheckedTimbre,
  useDollInfosValue,
  useSetDollInfos,
} from '../hooks/useAppHooks';
import {dollService} from '../services/dollService';
import Icon from 'react-native-vector-icons/AntDesign';
import BackLeftBtnComponent from '../components/BackLeftBtnComponent';
import {MyText, MyTextInput} from '../custom/CustomFont';
import ScreenPageComponent from '../components/ScreenPageComponent';
import MyPrimaryButton from '../components/MyPrimaryButton';

interface RoleCustomScreenProps {
  route: {
    params: {
      dollId: string;
      dollRole: DollRole | null;
    };
  };
}

const RoleCustomScreen: React.FC<RoleCustomScreenProps> = ({route}) => {
  const navigation = useAppNavigation();
  const {dollRole} = route.params;
  const {dollId} = route.params;

  const dollList = useDollInfosValue();
  const setDollInfos = useSetDollInfos();

  const checkedDoll = dollList.find(d => d.dollId === dollId);
  if (checkedDoll == null) {
    return (
      <View>
        <Icon name="left" size={24} onPress={() => navigation.goBack()} />
        <MyText>no doll</MyText>
      </View>
    );
  }
  const isDefault = checkedDoll.originalRoleId === dollRole?.roleId;
  // const avatarSource = dollService.getDollAvatar(checkedDoll.dollType);
  const avatarSource = isDefault
    ? dollService.getDollCircleAvatar(checkedDoll.dollType).b
    : dollService.getDollCircleAvatar(checkedDoll.dollType).p;

  const isNew = dollRole === null;
  const [role, setRole] = useState<DollRole | null>(dollRole);
  const [loading, setLoading] = useState(isNew);
  const [saving, setSaving] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showUnsavedDialog, setShowUnsavedDialog] = useState(false);
  const [content, setContent] = useState(dollRole?.roleContent || '');
  const [changed, setChanged] = useState(false);
  const initialRoleRef = useRef<string>('');

  const [checkedTimbre, setCheckedTimbre] = useCheckedTimbre();
  useEffect(() => {
    if (role) {
      setCheckedTimbre({id: role.timbreId, name: role.timbreName});
    }
  }, [role]);

  const handleTimbreRecord = () => {
    navigation.navigate('TimbreRecord');
  };

  // 新增时自动生成角色
  useEffect(() => {
    if (isNew) {
      setLoading(true);
      httpService.role.genRole({dollId}).then(res => {
        if (res.code === 0) {
          setRole({
            roleId: '',
            roleContent: res.data.roleContent,
            timbreId: res.data.timbreId,
            timbreName: res.data.timbreName,
          });
          setContent(res.data.roleContent);
          setCheckedTimbre({id: res.data.timbreId, name: res.data.timbreName});
          initialRoleRef.current = res.data.roleContent;
        }
        setLoading(false);
      });
    } else {
      initialRoleRef.current = dollRole?.roleContent || '';
    }
  }, [isNew, dollId, dollRole]);

  // 检查内容变更
  useEffect(() => {
    setChanged(content !== initialRoleRef.current);
  }, [content]);

  const handleChangeUsedRole = (roleId: string) => {
    setDollInfos(prev => {
      return prev.map(d => {
        if (d.dollId === dollId) {
          return {...d, usedRoleId: roleId};
        }
        return d;
      });
    });
  };
  // 返回按钮处理
  const handleBack = () => {
    if (!isDefault && changed) {
      setShowUnsavedDialog(true);
    } else {
      navigation.goBack();
    }
  };

  // AI润色
  const handleAIPolish = async () => {
    setLoading(true);
    const res = await httpService.role.genRole({dollId});
    if (res.code === 0) {
      setContent(res.data.roleContent);
      setCheckedTimbre({id: res.data.timbreId, name: res.data.timbreName});
      setChanged(true);
    }
    setLoading(false);
  };

  // 保存
  const handleSave = async () => {
    if (!role && !isNew) return;
    setSaving(true);
    const res = await httpService.role.upsertRole({
      dollId,
      roleId: role?.roleId || '',
      roleContent: content,
      timbreId: checkedTimbre?.id || '',
    });
    setSaving(false);
    if (res.code === 0) {
      setChanged(false);
      handleChangeUsedRole(res.data.roleId);
      navigation.goBack();
    } else {
      Alert.alert('保存失败', res.message || '请稍后重试');
    }
  };

  // 删除
  const handleDelete = async () => {
    if (!role) return;
    setDeleting(true);
    const res = await httpService.role.deleteRole({
      dollId,
      roleId: role.roleId,
    });
    setDeleting(false);
    if (res.code === 0) {
      handleChangeUsedRole(res.data.usedRoleId);
      navigation.goBack();
    } else {
      Alert.alert('删除失败', res.message || '请稍后重试');
    }
  };

  return (
    <ScreenPageComponent
      style={styles.root}
      bgType="bg_profile"
      keyboardAvoiding={true}>
      {/* 顶部导航栏 */}
      <View style={styles.header}>
        <BackLeftBtnComponent onPress={handleBack} />
        <MyText style={styles.title}>角色自定义</MyText>
        {(!isDefault && !isNew && (
          <TouchableOpacity
            onPress={() => setShowDeleteDialog(true)}
            style={styles.deleteBtn}>
            <MyText style={styles.deleteIcon}>删除</MyText>
          </TouchableOpacity>
        )) || <View style={{width: 40}} />}
      </View>
      <View style={styles.contentBox}>
        <View style={styles.avatarBox}>
          <Image source={avatarSource} style={styles.avatar} />
        </View>
        <View style={styles.voiceRow}>
          <MyText style={styles.voiceLabel}>声音</MyText>
          {isDefault ? (
            <View style={styles.voiceValueBox}>
              <MyText style={styles.voiceValue}>
                {checkedTimbre?.name || '请选择'}
              </MyText>
            </View>
          ) : (
            <TouchableOpacity
              style={styles.voiceValueBox}
              onPress={handleTimbreRecord}>
              <MyText style={styles.voiceValue}>
                {checkedTimbre?.name || '请选择'}
              </MyText>
              <Icon name="right" size={16} color={colors.textSecondary} />
            </TouchableOpacity>
          )}
        </View>
        <View style={styles.sectionBox}>
          <View style={styles.sectionHeader}>
            <MyText style={styles.sectionTitle}>故事背景介绍</MyText>

            {isDefault ? (
              <></>
            ) : (
              <TouchableOpacity
                style={styles.aiBtn}
                onPress={handleAIPolish}
                disabled={loading}>
                <MyText style={styles.aiBtnText}>✦ AI润色 </MyText>
                {loading && (
                  <ActivityIndicator
                    size="small"
                    color={colors.primary}
                    style={{marginLeft: 6}}
                  />
                )}
              </TouchableOpacity>
            )}
          </View>
          <MyTextInput
            style={styles.textArea}
            value={content}
            onChangeText={setContent}
            editable={!isDefault}
            multiline
            placeholder="请输入故事背景介绍..."
            placeholderTextColor={colors.placeholderText}
            numberOfLines={8}
            textAlignVertical="top"
          />
        </View>

        {/* 底部按钮 */}
        <View style={styles.footer}>
          <MyPrimaryButton
            onPress={handleSave}
            disabled={saving || loading}
            style={styles.saveBtn}>
            <MyText style={[styles.saveBtnText]}>确定</MyText>
          </MyPrimaryButton>
        </View>
      </View>

      <Dialog
        visible={showDeleteDialog}
        title="删除角色"
        content="确定要删除该角色吗？此操作不可撤销。"
        confirmText="删除"
        cancelText="取消"
        isDanger
        isLoading={deleting}
        onCancel={() => setShowDeleteDialog(false)}
        onConfirm={handleDelete}
      />
      {/* 未保存提示弹窗 */}
      <Dialog
        visible={showUnsavedDialog}
        title="未保存的更改"
        content="你有未保存的更改，确定要离开吗？"
        confirmText="离开"
        cancelText="取消"
        onCancel={() => setShowUnsavedDialog(false)}
        onConfirm={() => {
          setShowUnsavedDialog(false);
          navigation.goBack();
        }}
      />
    </ScreenPageComponent>
  );
};

const styles = StyleSheet.create({
  root: {
    flex: 1,
    // backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 16,
    paddingTop: 12,
    paddingBottom: 12,
    // borderBottomWidth: 1,
    // borderBottomColor: colors.divider,
    // backgroundColor: '#fff',
  },
  backBtn: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  backIcon: {
    fontSize: 24,
    color: colors.text,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text,
    textAlign: 'center',
  },
  deleteBtn: {
    position: 'absolute',
    right: 10,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    top: 7,
  },
  deleteIcon: {
    fontSize: 14,
    color: colors.textSecondary,
    // fontWeight: 'bold',
  },
  contentBox: {
    margin: 10,
    padding: 15,
    paddingBottom: 5,
    marginBottom: 30,
    flex: 1,
    borderWidth: 0.5,
    borderColor: colors.blue,
    borderRadius: borderRadius.lg,
  },
  avatarBox: {
    alignItems: 'center',
    marginBottom: 20,
  },
  avatar: {
    width: 100,
    height: 100,
    // borderRadius: 50,
    // backgroundColor: '#f5f5f5',
    // borderWidth: 2,
    // borderColor: colors.primary,
  },
  voiceRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
    padding: 12,
    // borderRadius: borderRadius.md,
    backgroundColor: '#f7f7fa',
    borderWidth: 1,
    borderColor: colors.primaryLight,
    borderRadius: borderRadius.md,
  },
  voiceLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.text,
    marginRight: 12,
  },
  voiceValueBox: {
    flexDirection: 'row',
    flex: 1,
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  voiceValue: {
    fontSize: 16,
    color: colors.textSecondary,
  },
  sectionBox: {
    backgroundColor: '#fff',
    padding: 12,
    marginBottom: 0,

    borderWidth: 1,
    borderColor: colors.primaryLight,
    borderRadius: borderRadius.md,
    flex: 1,
    overflow: 'hidden',
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.text,
    flex: 1,
  },
  aiBtn: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#E1E1FE',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 4,
  },
  aiBtnText: {
    color: colors.primary,
    fontSize: 14,
    fontWeight: '500',
  },
  textArea: {
    flex: 1,
    minHeight: 120,
    fontSize: 14,
    color: colors.primary,
    borderRadius: borderRadius.sm,
    padding: 0,
    marginTop: 8,
    textAlignVertical: 'top',
    overflow: 'scroll',
  },
  footer: {
    paddingVertical: 16,
    // backgroundColor: '#fff',
    // borderTopWidth: 1,
    // borderTopColor: colors.divider,
  },
  saveBtn: {
    borderRadius: borderRadius.md,
  },
  saveBtnText: {
    color: colors.primary,
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default RoleCustomScreen;
