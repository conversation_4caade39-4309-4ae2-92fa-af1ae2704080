import React, {useCallback, useEffect, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  ScrollView,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import {colors, borderRadius} from '../theme/theme';
import useAppNavigation from '../hooks/useAppNavigation';
import httpService, {DollInfo, DollRole} from '../net';
import Logger from '../services/LoggerService';
import {useDollInfosValue} from '../hooks/useAppHooks';
import {dollService, DollType} from '../services/dollService';
import BackLeftBtnComponent from '../components/BackLeftBtnComponent';
import {useFocusEffect, useIsFocused} from '@react-navigation/native';
import {MyText} from '../custom/CustomFont';
import ScreenPageComponent from '../components/ScreenPageComponent';
import Icon from 'react-native-vector-icons/AntDesign';

const {width} = Dimensions.get('window');

const RoleItem: React.FC<{
  role: DollRole;
  doll: DollInfo;
  currentRoleId: string | undefined;
  defaultRoleId: string | undefined;
}> = ({role, doll, currentRoleId, defaultRoleId}) => {
  const navigation = useAppNavigation();

  const isDefaultRole = role.roleId === defaultRoleId;
  const avatarSource = isDefaultRole
    ? dollService.getDollCircleAvatar((doll?.dollType as DollType) || 'rabbit')
        .b
    : dollService.getDollCircleAvatar((doll?.dollType as DollType) || 'rabbit')
        .p;
  const handleRolePress = (role: DollRole) => {
    navigation.navigate('RoleCustom', {
      dollId: doll.dollId,
      dollRole: role,
    });
  };
  return (
    <TouchableOpacity
      key={role.roleId}
      style={[styles.roleItem, isDefaultRole && styles.roleItemDefault]}
      activeOpacity={0.8}
      onPress={() => handleRolePress(role)}>
      {/* <Image source={getAvatarSource()} style={styles.avatar} /> */}
      <Image source={avatarSource} style={styles.avatar} />
      <View style={styles.roleInfo}>
        <MyText style={styles.roleName}>{role.timbreName || '角色'}</MyText>
        <MyText style={styles.roleDesc} numberOfLines={3}>
          {role.roleContent}
        </MyText>
      </View>
      {currentRoleId === role.roleId && (
        <View style={styles.checkIcon}>
          <Icon name="check" size={24} color={colors.primary} />
        </View>
        // <MyText style={styles.checkIcon}></MyText>
      )}
    </TouchableOpacity>
  );
};
interface RoleSwitchScreenProps {
  route: {
    params: {
      dollId: string;
    };
  };
}
const RoleSwitchScreen: React.FC<RoleSwitchScreenProps> = ({route}) => {
  const navigation = useAppNavigation();
  const {dollId} = route.params;
  const dollList = useDollInfosValue();
  const doll = dollList.find(d => d.dollId === dollId);

  const [roles, setRoles] = useState<DollRole[]>([]);
  const [loading, setLoading] = useState(true);

  // 获取默认roleId
  const defaultRoleId = doll?.originalRoleId;
  // 当前选中roleId
  const currentRoleId = doll?.usedRoleId;

  const doRefreshRoles = useCallback(() => {
    if (!dollId) {
      Logger.error('RoleSwitchScreen', 'dollId is required');
      return;
    }
    setLoading(true);
    httpService.role.getRoles({dollId}).then(res => {
      if (res.code === 0) {
        const sortedRoles = res.data.roles.sort((a, b) => {
          if (a.roleId === defaultRoleId) {
            return -1;
          }
          if (b.roleId === defaultRoleId) {
            return 1;
          }
          return 0;
        });
        setRoles(sortedRoles);
      }
      setLoading(false);
    });
  }, [dollId]);

  const isFocused = useIsFocused();
  useFocusEffect(
    useCallback(() => {
      if (isFocused) {
        doRefreshRoles();
      }
    }, [dollId]),
  );

  const handleAddRole = () => {
    navigation.navigate('RoleCustom', {
      dollId,
      dollRole: null,
    });
  };

  // 头像图片
  // const getAvatarSource = () => {
  //   return require('../assets/img/group_260_3x.png');
  // };

  return (
    <ScreenPageComponent style={styles.root} bgType="bg_profile">
      {/* 顶部导航栏 */}
      <View style={styles.header}>
        <BackLeftBtnComponent />
        <MyText style={styles.title}>角色切换</MyText>
      </View>
      {loading ? (
        <View style={styles.loadingBox}>
          <ActivityIndicator size="large" color={colors.primary} />
          <MyText style={styles.loadingText}>加载角色中...</MyText>
        </View>
      ) : (
        <ScrollView contentContainerStyle={styles.scrollContent}>
          {roles.map(
            role =>
              doll && (
                <RoleItem
                  key={role.roleId}
                  role={role}
                  doll={doll}
                  currentRoleId={currentRoleId}
                  defaultRoleId={defaultRoleId}
                />
              ),
          )}
        </ScrollView>
      )}
      {/* 底部自定义角色按钮 */}
      <View style={styles.footer}>
        <TouchableOpacity style={styles.addBtn} onPress={handleAddRole}>
          <MyText style={styles.addBtnText}>+ 自定义角色</MyText>
        </TouchableOpacity>
      </View>
    </ScreenPageComponent>
  );
};

const styles = StyleSheet.create({
  root: {
    flex: 1,
    // backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 16,
    paddingTop: 12,
    paddingBottom: 12,
    // backgroundColor: '#f6f8ff',
  },
  backBtn: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  backIcon: {
    fontSize: 24,
    color: colors.text,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text,
  },
  loadingBox: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: colors.textSecondary,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 80,
  },
  roleItem: {
    height: width * 0.3,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.primary,
    borderRadius: borderRadius.lg,
    padding: 16,
    marginBottom: 10,
    backgroundColor: '#fff',
  },
  // roleItemChecked: {
  //   borderColor: colors.primary,
  //   // backgroundColor: '#f6f3ff',
  // },
  roleItemDefault: {
    borderColor: '#0383df',
  },
  avatar: {
    width: 88,
    height: 88,
    marginRight: 10,
    // borderRadius: 32,
    // marginRight: 16,
    // backgroundColor: '#f5f5f5',
    // borderWidth: 1,
    // borderColor: colors.primaryLight,
  },
  roleInfo: {
    flex: 1,
    justifyContent: 'center',
  },
  roleName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: 8,
    marginTop: 6,
  },
  roleDesc: {
    fontSize: 14,
    color: colors.textSecondary,
    flexGrow: 1,
  },
  checkIcon: {
    position: 'absolute',
    fontSize: 22,
    color: colors.primary,
    fontWeight: 'bold',
    right: 15,
    top: 10,
  },
  footer: {
    padding: 16,
    backgroundColor: 'transparent',
  },
  addBtn: {
    borderWidth: 1,
    borderColor: colors.primaryLight,
    borderRadius: borderRadius.lg,
    backgroundColor: '#E1E1FE',
    alignItems: 'center',
    justifyContent: 'center',
    height: 48,
  },
  addBtnText: {
    color: colors.primary,
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default RoleSwitchScreen;
