import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  ImageSourcePropType,
} from 'react-native';
import {colors} from '../theme/theme';
import useAppNavigation from '../hooks/useAppNavigation';
import Icon from 'react-native-vector-icons/AntDesign';
import KnowledgeLinkModal from '../components/KnowledgeLinkModal';
import httpService from '../net';
import {showToast} from '../utils/utils';
import BackLeftBtnComponent from '../components/BackLeftBtnComponent';
import {MyText} from '../custom/CustomFont';
import ScreenPageComponent from '../components/ScreenPageComponent';

// 菜单项类型
type MenuItem = {
  id: string;
  title: string;
  icon: ImageSourcePropType;
  screen?: string;
  action?: string;
};

// 菜单项
const MENU_ITEMS: MenuItem[] = [
  {
    id: 'order',
    title: '订购',
    icon: require('../assets/img/mask_group_3x.png'),
    action: 'showOrderLink',
  },
  {
    id: 'settings',
    title: '设置',
    icon: require('../assets/img/lujing_4812_3x.png'),
    screen: 'Settings',
  },
  {
    id: 'faq',
    title: '常见问题',
    icon: require('../assets/img/mask_group_3x_1_.png'),
  },
  {
    id: 'instructions',
    title: '使用说明',
    icon: require('../assets/img/mask_group_3x_2_.png'),
  },
];

const ProfileScreen: React.FC = () => {
  const navigation = useAppNavigation();
  const [showOrderLink, setShowOrderLink] = useState(false);
  const [subscriptionLink, setSubscriptionLink] = useState('');

  useEffect(() => {
    const fetchSubscriptionLink = async () => {
      const res = await httpService.setting.getSubscribeUrl();
      if (res.code === 0) {
        setSubscriptionLink(res.data.url);
      } else {
        setSubscriptionLink('');
        showToast('获取订阅链接失败');
      }
    };
    fetchSubscriptionLink();
  }, []);

  const handleMenuItemPress = (item: MenuItem) => {
    if (item.screen) {
      // 导航到对应页面
      navigation.navigate(item.screen as any);
    } else if (item.action === 'showOrderLink') {
      // 显示订购链接弹窗
      setShowOrderLink(true);
    }
  };

  return (
    <ScreenPageComponent style={styles.container} bgType="bg_profile">
      <View style={styles.header}>
        <BackLeftBtnComponent />
        <MyText style={styles.headerTitle}>我的</MyText>
      </View>

      <View style={styles.menuContainer}>
        {MENU_ITEMS.map(item => (
          <TouchableOpacity
            key={item.id}
            style={styles.menuItem}
            onPress={() => handleMenuItemPress(item)}>
            <View style={styles.menuLeft}>
              <Image source={item.icon} style={styles.menuIcon} />
              <MyText style={styles.menuTitle}>{item.title}</MyText>
            </View>
            <Icon name="right" size={20} color={colors.text} />
          </TouchableOpacity>
        ))}
      </View>

      {/* 订购链接弹窗 */}
      <KnowledgeLinkModal
        visible={showOrderLink}
        onClose={() => setShowOrderLink(false)}
        content="请复制下方链接到浏览器查看订阅"
        link={subscriptionLink}
      />
    </ScreenPageComponent>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 16,
    paddingTop: 12,
    paddingBottom: 12,
    // backgroundColor: '#fff',
    position: 'relative',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text,
  },
  closeButton: {
    position: 'absolute',
    right: 16,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  menuContainer: {
    backgroundColor: '#fff',
    borderRadius: 12,
    borderColor: colors.primary,
    borderWidth: 1,
    margin: 16,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: colors.divider,
    height: 60,
  },
  menuLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  menuIcon: {
    width: 24,
    height: 24,
    marginRight: 12,
  },
  menuTitle: {
    fontSize: 16,
    color: colors.text,
  },
});

export default ProfileScreen;
