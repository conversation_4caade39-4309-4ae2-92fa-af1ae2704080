import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  ImageSourcePropType,
  Image,
} from 'react-native';
import {borderRadius, colors} from '../theme/theme';
import useAppNavigation from '../hooks/useAppNavigation';
import Dialog from '../components/Dialog';
import {APP_VERSION} from '../constants/appConstants';
import KnowledgeLinkModal from '../components/KnowledgeLinkModal';
import httpService from '../net';
import Logger from '../services/LoggerService';
import {showToast} from '../utils/utils';
import Icon from 'react-native-vector-icons/AntDesign';
import BackLeftBtnComponent from '../components/BackLeftBtnComponent';
import {MyText} from '../custom/CustomFont';
import ScreenPageComponent from '../components/ScreenPageComponent';
import MyPrimaryButton from '../components/MyPrimaryButton';
// 设置项类型
type SettingItem = {
  id: string;
  icon?: ImageSourcePropType;
  title: string;
  value?: string;
  rightIcon?: string;
  action?: string;
  disabled?: boolean;
  danger?: boolean;
};

// 设置项
const SETTING_ITEMS: SettingItem[] = [
  {
    id: 'version',
    icon: require('../assets/img/mask_group_3x_4_.png'),
    title: '当前版本',
    value: APP_VERSION || '1.0.0',
    disabled: true,
  },
  {
    id: 'parentKnowledge',
    icon: require('../assets/img/mask_group_3x_5_.png'),
    title: '家长个性化知识库导入',
    rightIcon: 'right',
    action: 'showKnowledgeLink',
  },
];

// 底部按钮
const BOTTOM_BUTTONS: SettingItem[] = [
  {
    id: 'logout',
    title: '退出登录',
    action: 'logout',
  },
  {
    id: 'deleteAccount',
    title: '注销账号',
    action: 'deleteAccount',
    danger: true,
  },
];

const SettingsScreen: React.FC = () => {
  const navigation = useAppNavigation();
  const [showLogoutDialog, setShowLogoutDialog] = useState(false);
  const [showDeleteAccountDialog, setShowDeleteAccountDialog] = useState(false);
  const [showKnowledgeLink, setShowKnowledgeLink] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [knowledgeLink, setKnowledgeLink] = useState('');
  useEffect(() => {
    const fetchKnowledgeLink = async () => {
      const res = await httpService.setting.getRepositoryUrl();
      if (res.code === 0) {
        setKnowledgeLink(res.data.url);
      } else {
        setKnowledgeLink('');
        showToast('获取知识库链接失败');
      }
    };
    fetchKnowledgeLink();
  }, []);

  const handleBack = () => {
    navigation.goBack();
  };

  const handleSettingPress = (item: SettingItem) => {
    if (item.disabled) return;

    switch (item.action) {
      case 'showKnowledgeLink':
        setShowKnowledgeLink(true);
        break;
      case 'logout':
        setShowLogoutDialog(true);
        break;
      case 'deleteAccount':
        setShowDeleteAccountDialog(true);
        break;
      default:
        break;
    }
  };

  const handleLogout = async () => {
    setIsLoading(true);
    try {
      // 调用登出服务
      await httpService.setting.logout();
      setShowLogoutDialog(false);
      Logger.info('用户退出登录');

      // 重置导航到登录页
      navigation.reset({
        index: 0,
        routes: [{name: 'LoginRegister'}],
      });
    } catch (error) {
      console.error('退出登录失败:', error);
      Alert.alert('退出失败', '请稍后重试');
      setShowLogoutDialog(false);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteAccount = async () => {
    setIsLoading(true);
    try {
      // 先退出登录
      await httpService.setting.deleteAccount();

      setShowDeleteAccountDialog(false);
      console.log('用户注销账号');

      // 重置导航到登录页
      navigation.reset({
        index: 0,
        routes: [{name: 'LoginRegister'}],
      });
    } catch (error) {
      console.error('注销账号失败:', error);
      Alert.alert('注销失败', '请稍后重试');
      setShowDeleteAccountDialog(false);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ScreenPageComponent style={styles.container} bgType="bg_profile">
      <View style={styles.header}>
        <BackLeftBtnComponent />
        <MyText style={styles.headerTitle}>设置</MyText>
      </View>

      <View style={styles.content}>
        <View style={styles.settingsContainer}>
          {SETTING_ITEMS.map(item => (
            <TouchableOpacity
              key={item.id}
              style={[
                styles.settingItem,
                item.disabled && styles.settingItemDisabled,
              ]}
              onPress={() => handleSettingPress(item)}
              disabled={item.disabled}>
              <View style={{flexDirection: 'row', alignItems: 'center'}}>
                {item.icon && (
                  <Image source={item.icon} style={styles.settingIcon} />
                )}
                <MyText style={styles.settingTitle}>{item.title}</MyText>
              </View>
              <View style={styles.settingRight}>
                {item.value && (
                  <MyText style={styles.settingValue}>{item.value}</MyText>
                )}
                {item.rightIcon && (
                  <Icon name={item.rightIcon} style={styles.settingArrow} />
                )}
              </View>
            </TouchableOpacity>
          ))}
        </View>

        <View style={styles.bottomButtonsContainer}>
          {BOTTOM_BUTTONS.map(item => (
            <MyPrimaryButton
              key={item.id}
              style={[styles.bottomButton]}
              onPress={() => handleSettingPress(item)}>
              <MyText
                style={[
                  styles.bottomButtonText,
                  // item.danger && styles.dangerButtonText,
                ]}>
                {item.title}
              </MyText>
            </MyPrimaryButton>
          ))}
        </View>
      </View>

      {/* 退出登录确认弹窗 */}
      <Dialog
        visible={showLogoutDialog}
        title="提示"
        content="确定退出吗？"
        cancelText="取消"
        confirmText="确定"
        onCancel={() => setShowLogoutDialog(false)}
        onConfirm={handleLogout}
        isLoading={isLoading}
      />

      {/* 注销账号确认弹窗 */}
      <Dialog
        visible={showDeleteAccountDialog}
        title="提示"
        content="注销账号后，您所有数据都将被清除且无法恢复。"
        cancelText="取消"
        confirmText="确认注销"
        onCancel={() => setShowDeleteAccountDialog(false)}
        onConfirm={handleDeleteAccount}
        isDanger
        isLoading={isLoading}
      />

      {/* 家长个性化知识库链接弹窗 */}
      <KnowledgeLinkModal
        visible={showKnowledgeLink}
        onClose={() => setShowKnowledgeLink(false)}
        content="请通过下方链接前往家长个性化知识库导入页面"
        link={knowledgeLink}
      />
    </ScreenPageComponent>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 16,
    paddingTop: 12,
    paddingBottom: 12,
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  backButtonText: {
    fontSize: 24,
    color: colors.text,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text,
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    justifyContent: 'space-between',
  },
  settingsContainer: {
    backgroundColor: '#fff',
    borderRadius: 12,
    borderColor: colors.primary,
    borderWidth: 1,
    margin: 16,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.divider,
    height: 60,
  },
  settingItemDisabled: {
    opacity: 0.7,
  },
  settingIcon: {
    width: 24,
    height: 24,
    marginRight: 8,
  },
  settingTitle: {
    fontSize: 16,
    color: colors.text,
  },
  settingRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingValue: {
    fontSize: 16,
    color: colors.textSecondary,
    marginRight: 8,
  },
  settingArrow: {
    fontSize: 20,
    color: colors.textSecondary,
  },
  bottomButtonsContainer: {
    marginHorizontal: 16,
    marginBottom: 32,
  },
  bottomButton: {
    // borderRadius: 8,
    // padding: 16,
    borderRadius: borderRadius.md,
    marginVertical: 5,
  },
  bottomButtonText: {
    fontSize: 16,
    color: colors.primary,
    fontWeight: 'bold',
  },
});

export default SettingsScreen;
