import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  ScrollView,
} from 'react-native';
import {useAtom} from 'jotai';
import UpdateManager, {
  updateStatusAtom,
  updateInfoAtom,
  updateErrorAtom,
} from '../services/UpdateManager';
import AppBackgroundImg from '../components/AppBackgroudImg';
import {MyText} from '../custom/CustomFont';

const UpdateScreen = () => {
  const [checking, setChecking] = useState(false);
  const [updateStatus] = useAtom(updateStatusAtom);
  const [updateInfo] = useAtom(updateInfoAtom);
  const [updateError] = useAtom(updateErrorAtom);

  // 获取当前版本信息
  const {version, buildNumber} = UpdateManager.getAppVersion();

  // 检查更新
  const handleCheckUpdate = async () => {
    setChecking(true);
    try {
      const {hasUpdate} = await UpdateManager.checkForUpdates();
      setChecking(false);
    } catch (error) {
      setChecking(false);
    }
  };

  // 处理更新
  const handleUpdate = async () => {
    if (updateInfo) {
      await UpdateManager.downloadUpdate(updateInfo);
    }
  };

  // 渲染更新状态
  const renderUpdateStatus = () => {
    if (checking) {
      return (
        <View style={styles.statusContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <MyText style={styles.statusText}>正在检查更新...</MyText>
        </View>
      );
    }

    switch (updateStatus) {
      case 'available':
        if (!updateInfo) return null;
        return (
          <View style={styles.statusContainer}>
            <MyText style={styles.availableText}>发现新版本!</MyText>
            <MyText style={styles.versionText}>
              新版本: {updateInfo.version} (Build {updateInfo.buildNumber})
            </MyText>
            <View style={styles.notesContainer}>
              <MyText style={styles.notesTitle}>更新内容:</MyText>
              <MyText style={styles.notesText}>
                {updateInfo.releaseNotes}
              </MyText>
            </View>
            <TouchableOpacity
              style={styles.updateButton}
              onPress={handleUpdate}>
              <MyText style={styles.updateButtonText}>立即更新</MyText>
            </TouchableOpacity>
          </View>
        );
      case 'not-available':
        return (
          <View style={styles.statusContainer}>
            <MyText style={styles.latestText}>您已经是最新版本</MyText>
            <MyText style={styles.subtitleText}>
              我们会定期提供新功能和性能改进
            </MyText>
          </View>
        );
      case 'error':
        return (
          <View style={styles.statusContainer}>
            <MyText style={styles.errorText}>检查更新失败</MyText>
            <MyText style={styles.errorDetailText}>
              {updateError || '请检查网络连接后重试'}
            </MyText>
          </View>
        );
      default:
        return null;
    }
  };

  return (
    <View style={styles.container}>
      <AppBackgroundImg />
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.headerContainer}>
          <MyText style={styles.headerTitle}>应用更新</MyText>
        </View>

        <View style={styles.versionContainer}>
          <MyText style={styles.currentVersionLabel}>当前版本</MyText>
          <MyText style={styles.currentVersion}>
            {version} (Build {buildNumber})
          </MyText>
        </View>

        {renderUpdateStatus()}

        <TouchableOpacity
          style={styles.checkButton}
          onPress={handleCheckUpdate}
          disabled={checking}>
          <MyText style={styles.checkButtonText}>检查更新</MyText>
        </TouchableOpacity>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // backgroundColor: '#F8F8F8',
  },
  scrollContent: {
    flexGrow: 1,
    padding: 20,
  },
  headerContainer: {
    marginBottom: 30,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  versionContainer: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 20,
    marginBottom: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  currentVersionLabel: {
    fontSize: 16,
    color: '#666',
    marginBottom: 5,
  },
  currentVersion: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  statusContainer: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 20,
    marginBottom: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  statusText: {
    marginTop: 15,
    fontSize: 16,
    color: '#333',
  },
  availableText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#4CAF50',
    marginBottom: 10,
  },
  versionText: {
    fontSize: 16,
    color: '#333',
    marginBottom: 15,
  },
  notesContainer: {
    backgroundColor: '#F1F8E9',
    padding: 15,
    borderRadius: 8,
    width: '100%',
    marginBottom: 20,
  },
  notesTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#333',
  },
  notesText: {
    fontSize: 14,
    lineHeight: 20,
    color: '#333',
  },
  updateButton: {
    backgroundColor: '#007AFF',
    paddingVertical: 12,
    paddingHorizontal: 30,
    borderRadius: 8,
    marginTop: 10,
  },
  updateButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  latestText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#4CAF50',
    marginBottom: 8,
  },
  subtitleText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
  errorText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#F44336',
    marginBottom: 8,
  },
  errorDetailText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
  checkButton: {
    backgroundColor: '#007AFF',
    paddingVertical: 15,
    borderRadius: 10,
    alignItems: 'center',
    marginTop: 20,
  },
  checkButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default UpdateScreen;
