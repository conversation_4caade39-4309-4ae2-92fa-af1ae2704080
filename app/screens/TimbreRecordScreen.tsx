import React, {useEffect, useState, useRef} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  FlatList,
  StyleSheet,
  Alert,
  ActivityIndicator,
} from 'react-native';
import AudioRecorderPlayer from 'react-native-audio-recorder-player';
import httpService from '../net/services/httpService';
import {Timbre} from '../net/types';
import Icon from 'react-native-vector-icons/AntDesign';
import {useCheckedTimbre, useTimbreList} from '../hooks/useAppHooks';
import useAppNavigation from '../hooks/useAppNavigation';
import {fileToBase64} from '../utils/utils';
import {colors} from '../theme/theme';
import FrameAnimation from '../components/FrameAnimation';
import {timbreService} from '../services/timbreService';
import Dialog from '../components/Dialog';
import RecordBottomSheet from '../components/RecordBottomSheet';
import BackLeftBtnComponent from '../components/BackLeftBtnComponent';
import {MyText} from '../custom/CustomFont';
import ScreenPageComponent from '../components/ScreenPageComponent';
const TABS = [
  // {key: 'my', label: '我的'},
  {key: 'reco', label: '推荐'},
];

const audioRecorderPlayer = new AudioRecorderPlayer();

interface TimbreItemProps {
  item: Timbre;
  playingId: string;
  playTimbre: (item: Timbre) => void;
  tab: string;
  handleDeleteTimbre: (item: Timbre) => void;
  deletingId: string;
  checkedId: string;
  handleChangeTimbre: (item: Timbre) => void;
}

const TimbreItem: React.FC<TimbreItemProps> = ({
  item,
  playingId,
  playTimbre,
  tab,
  handleDeleteTimbre,
  deletingId,
  handleChangeTimbre,
  checkedId,
}) => (
  <View style={styles.timbreItem}>
    <TouchableOpacity onPress={() => playTimbre(item)}>
      <View style={{width: 36, height: 36}}>
        <FrameAnimation
          frames={timbreService.getTestTimbreBtnFrames()}
          frameRate={1.5}
          playing={playingId === item.timbreId}
        />
      </View>
    </TouchableOpacity>
    <View style={{flex: 1, marginLeft: 12}}>
      <MyText style={styles.timbreName}>{item.timbreName}</MyText>
      <MyText style={styles.timbreDesc}>
        {item.isCustom ? item.createTime + ' 创建' : '风格: ' + item.style}
      </MyText>
    </View>
    {tab === 'my' && item.isCustom && (
      <TouchableOpacity
        onPress={() => handleDeleteTimbre(item)}
        style={{marginRight: 10}}
        disabled={deletingId === item.timbreId}>
        <Icon name="delete" size={20} color={colors.textSecondary} />
      </TouchableOpacity>
    )}
    <TouchableOpacity onPress={() => handleChangeTimbre(item)}>
      <Icon
        name={checkedId === item.timbreId ? 'check' : 'plus'}
        size={26}
        color={checkedId === item.timbreId ? '#A48AFF' : '#B6B6D4'}
      />
    </TouchableOpacity>
  </View>
);

const TimbreRecordScreen: React.FC = () => {
  const [tab, setTab] = useState('reco');
  const [loading, setLoading] = useState(true);
  const [recordLimit, setRecordLimit] = useState(0);
  const [playingId, setPlayingId] = useState<string | null>(null);
  const [recordingModal, setRecordingModal] = useState(false);
  const [deletingId, setDeletingId] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const navigation = useAppNavigation();

  const [checkedTimbre, setCheckedTimbre] = useCheckedTimbre();
  const [timbreList, setTimbreList] = useTimbreList();

  const audioRecorderPlayer = useRef(new AudioRecorderPlayer());

  useEffect(() => {
    fetchTimbreList();
  }, []);

  const fetchTimbreList = async () => {
    setLoading(true);
    try {
      const res = await httpService.timbre.getTimbreList();
      if (res.code === 0) {
        setTimbreList(res.data.list);
        setRecordLimit(res.data.recordLimitNum);
      } else {
        Alert.alert('获取音色失败', res.message);
      }
    } catch (e) {
      Alert.alert('网络错误', '无法获取音色列表');
    }
    setLoading(false);
  };

  // 试听音色
  const playTimbre = async (timbre: Timbre) => {
    if (playingId === timbre.timbreId) {
      await audioRecorderPlayer.current.stopPlayer();
      setPlayingId(null);
      return;
    }
    try {
      await audioRecorderPlayer.current.stopPlayer();
      await audioRecorderPlayer.current.startPlayer(timbre.timbreSampleUrl);
      setPlayingId(timbre.timbreId);
      audioRecorderPlayer.current.addPlayBackListener(e => {
        if (e.currentPosition >= e.duration) {
          setPlayingId(null);
          audioRecorderPlayer.current.stopPlayer();
        }
        return;
      });
    } catch (e) {
      Alert.alert('播放失败', '音频播放出错');
      setPlayingId(null);
    }
  };

  const handleChangeTimbre = (timbre: Timbre) => {
    setCheckedTimbre({id: timbre.timbreId, name: timbre.timbreName});
  };

  const handleDeleteTimbre = async (timbre: Timbre) => {
    if (deletingId) return;
    setDeletingId(timbre.timbreId);
  };

  // 处理录音完成
  const handleRecordComplete = async (recordedPath: string) => {
    try {
      const voiceBase64 = await fileToBase64(recordedPath);
      const res = await httpService.timbre.recordTimbre({
        voice: voiceBase64,
      });
      if (res.code === 0) {
        setTimbreList([res.data.newTimbre, ...timbreList]);
        setRecordingModal(false);
      } else {
        Alert.alert('上传失败', res.message);
      }
    } catch (e) {
      Alert.alert('上传失败', '音色上传出错');
    }
  };

  const renderTimbreItem = ({item}: {item: Timbre}) => (
    <TimbreItem
      item={item}
      playingId={playingId || ''}
      playTimbre={playTimbre}
      tab={tab}
      handleDeleteTimbre={handleDeleteTimbre}
      deletingId={deletingId || ''}
      handleChangeTimbre={handleChangeTimbre}
      checkedId={checkedTimbre?.id || ''}
    />
  );

  const filteredTimbres = timbreList.filter(t => t.tag === tab);
  const customCount = timbreList.filter(t => t.isCustom).length;

  return (
    <ScreenPageComponent style={styles.container} bgType="bg_profile">
      <View style={styles.header}>
        <BackLeftBtnComponent />
        <MyText style={styles.title}>音色录制</MyText>
      </View>

      {/* 顶部Tabs */}
      <View style={styles.tabs}>
        {TABS.map(t => (
          <TouchableOpacity
            key={t.key}
            style={[styles.tab, tab === t.key && styles.tabActive]}
            onPress={() => setTab(t.key)}>
            <MyText
              style={[styles.tabText, tab === t.key && styles.tabTextActive]}>
              {t.label}
            </MyText>
          </TouchableOpacity>
        ))}
      </View>

      {/* 音色列表 */}
      {loading ? (
        <ActivityIndicator style={{marginTop: 40}} />
      ) : (
        <FlatList
          data={filteredTimbres}
          keyExtractor={item => item.timbreId}
          renderItem={renderTimbreItem}
          contentContainerStyle={{padding: 16}}
        />
      )}

      {/* 底部录制按钮 */}
      {tab === 'my' && (
        <TouchableOpacity
          style={[
            styles.recordBtn,
            customCount >= recordLimit && styles.recordBtnDisabled,
          ]}
          disabled={customCount >= recordLimit}
          onPress={() => setRecordingModal(true)}>
          <MyText
            style={[
              styles.recordBtnText,
              customCount >= recordLimit && {color: '#B6B6D4'},
            ]}>
            添加音色
          </MyText>
        </TouchableOpacity>
      )}

      {/* 使用新的录音弹窗组件 */}
      <RecordBottomSheet
        visible={recordingModal}
        onClose={() => setRecordingModal(false)}
        onRecordComplete={handleRecordComplete}
      />

      {/* 删除音色对话框 */}
      {deletingId && (
        <Dialog
          visible={!!deletingId}
          title="删除音色"
          content="确定要删除该自定义音色吗？"
          isLoading={isDeleting}
          onCancel={() => setDeletingId(null)}
          onConfirm={async () => {
            try {
              setIsDeleting(true);
              const res = await httpService.timbre.deleteTimbre({
                timbreId: deletingId,
              });
              if (res.code === 0) {
                setTimbreList(
                  timbreList.filter(t => t.timbreId !== deletingId),
                );
                const newTimbre = timbreList.find(
                  t => t.timbreId === res.data.timbreId,
                );
                if (newTimbre) {
                  setCheckedTimbre({
                    id: newTimbre.timbreId,
                    name: newTimbre.timbreName,
                  });
                }
              } else {
                Alert.alert('删除失败', res.message);
              }
            } catch (e) {
              Alert.alert('删除失败');
            } finally {
              setIsDeleting(false);
              setDeletingId(null);
            }
          }}
        />
      )}
    </ScreenPageComponent>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // backgroundColor: '#F7F8FC',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 10,
    paddingTop: 12,
    paddingBottom: 0,
    // borderBottomWidth: 1,
    // borderBottomColor: colors.divider,
    // backgroundColor: '#fff',
  },
  backBtn: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  backIcon: {
    fontSize: 24,
    color: colors.text,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text,
    textAlign: 'center',
    marginLeft: -20,
  },
  tabs: {
    flexDirection: 'row',
    marginTop: 16,
    marginLeft: 16,
    justifyContent: 'flex-start',
  },
  tab: {
    paddingHorizontal: 18,
    paddingVertical: 6,
    borderRadius: 6,
    backgroundColor: '#F0F0F6',
    marginHorizontal: 4,
  },
  tabActive: {backgroundColor: '#E6E0FF'},
  tabText: {color: '#B6B6D4', fontWeight: 'bold'},
  tabTextActive: {color: '#A48AFF'},
  timbreItem: {
    flexDirection: 'row',
    alignItems: 'center',
    // backgroundColor: '#fff',
    borderRadius: 8,
    marginBottom: 16,
    padding: 16,
    borderWidth: 1,
    borderColor: colors.primaryLight,
  },
  timbreName: {fontWeight: 'bold', fontSize: 16, color: '#222'},
  timbreDesc: {fontSize: 12, color: colors.textSecondary, marginTop: 6},
  recordBtn: {
    margin: 24,
    backgroundColor: '#E6E0FF',
    borderRadius: 12,
    alignItems: 'center',
    padding: 16,
    display: 'none',
  },
  recordBtnDisabled: {backgroundColor: '#F0F0F6'},
  recordBtnText: {color: colors.primary, fontWeight: 'bold', fontSize: 16},
  modalContent: {
    width: 320,
    backgroundColor: '#fff',
    borderRadius: 20,
    padding: 24,
    alignItems: 'center',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#A48AFF',
    marginBottom: 12,
  },
  modalDesc: {
    color: '#222',
    fontSize: 15,
    marginBottom: 24,
    textAlign: 'center',
  },
  modalBtn: {
    backgroundColor: '#E6E0FF',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 32,
    marginTop: 12,
  },
  modalBtnText: {color: '#A48AFF', fontWeight: 'bold', fontSize: 16},
  modalCancel: {marginTop: 16},
});

export default TimbreRecordScreen;
