import React, {useState, useEffect} from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Image,
  ScrollView,
  TextInput,
  ActivityIndicator,
} from 'react-native';
import {MyText, MyTextInput} from '../custom/CustomFont';
import {colors, spacing, typography, borderRadius} from '../theme/theme';
import useAppNavigation from '../hooks/useAppNavigation';
import Icon from 'react-native-vector-icons/AntDesign';
import ScreenPageComponent from '../components/ScreenPageComponent';
import {wifiScanService, WiFiNetworkDisplay} from '../services/WiFiScanService';
import Logger from '../services/LoggerService';
import {showToast} from '../utils/utils';
import {useWiFiConfig} from '../hooks/useDollWiFi';
import {WiFiConfig} from '../services/DollWiFiService';
import httpService from '../net';
interface DollConnectWifiScreenProps {
  route: {
    params: {
      dollId: string;
    };
  };
}

const DollConnectWifiScreen: React.FC<DollConnectWifiScreenProps> = ({
  route,
}) => {
  const navigation = useAppNavigation();
  const {dollId} = route.params;

  // 状态管理
  const [showWifiList, setShowWifiList] = useState(false);
  const [selectedWifi, setSelectedWifi] = useState('');
  const [wifiPassword, setWifiPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isScanning, setIsScanning] = useState(false);
  const [isConfiguring, setIsConfiguring] = useState(false);
  const [wifiNetworks, setWifiNetworks] = useState<WiFiNetworkDisplay[]>([]);
  const [selectedSecurity, setSelectedSecurity] = useState(3); // 默认WPA2
  const {configureWiFi, isSuccess, isError, isLoading} = useWiFiConfig();

  // 初始化时扫描WiFi
  useEffect(() => {
    scanWiFiNetworks();
  }, []);

  const handleBack = () => {
    navigation.goBack();
  };

  // 扫描WiFi网络
  const scanWiFiNetworks = async () => {
    try {
      setIsScanning(true);
      const networks = await wifiScanService.scanWiFiNetworks();
      const currentWifi = await wifiScanService.getCurrentWiFi();
      let currentWifiInfo: WiFiNetworkDisplay | undefined;
      if (currentWifi) {
        currentWifiInfo = networks.find(
          network => network.name === currentWifi,
        );
      }
      // 过滤2.4GHz网络
      const filtered24GHz = wifiScanService.filter24GHzNetworks(networks);
      if (
        currentWifiInfo &&
        !filtered24GHz.find(network => network.name === currentWifi)
      ) {
        filtered24GHz.push(currentWifiInfo);
      }
      Logger.info('当前连接的WiFi', {currentWifi});
      if (!selectedWifi && currentWifi) {
        if (filtered24GHz.find(network => network.name === currentWifi)) {
          setSelectedWifi(currentWifi);
        }
      }
      const firstWifi = selectedWifi || currentWifi;
      filtered24GHz.sort((a, b) => {
        if (a.name === firstWifi) {
          return -1;
        }
        if (b.name === firstWifi) {
          return 1;
        }
        return 0;
      });

      setWifiNetworks(filtered24GHz);

      Logger.info('WiFi扫描完成', {
        totalNetworks: networks.length,
        filtered24GHz: filtered24GHz.length,
      });
    } catch (error) {
      Logger.error('WiFi扫描失败', {error});
      showToast('WiFi扫描失败，请检查权限设置');
    } finally {
      setIsScanning(false);
    }
  };

  // 切换WiFi列表显示
  const toggleWifiList = async () => {
    if (!showWifiList && wifiNetworks.length === 0) {
      // 如果没有网络数据，先扫描
      await scanWiFiNetworks();
    }
    setShowWifiList(!showWifiList);
  };

  // 选择WiFi网络
  const handleSelectWifi = (network: WiFiNetworkDisplay) => {
    setSelectedWifi(network.name);
    setSelectedSecurity(network.security);
    setShowWifiList(false);
  };

  // 切换密码显示
  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  // 获取WiFi信号强度图标
  const getWifiStrengthIcon = (strength: string) => {
    // 根据信号强度返回不同图标
    switch (strength) {
      case 'strong':
        return require('../assets/img/group_258_3x.png');
      case 'medium':
        return require('../assets/img/group_257_3x.png');
      case 'weak':
        return require('../assets/img/group_257_3x.png');
      default:
        return require('../assets/img/group_257_3x.png');
    }
  };

  // 下一步按钮处理 - 配置WiFi
  const handleNext = async () => {
    if (!selectedWifi.trim() || !wifiPassword.trim()) {
      showToast('请输入WiFi名称和密码');
      return;
    }

    try {
      setIsConfiguring(true);
      // showToast('正在配置WiFi...');

      const wifiConfig: WiFiConfig = {
        ssid: selectedWifi.trim(),
        password: wifiPassword.trim(),
        security: selectedSecurity,
      };

      Logger.info('开始WiFi配置', {
        dollId,
        ssid: wifiConfig.ssid,
        security: wifiConfig.security,
        passwordLength: wifiConfig.password.length,
      });

      try {
        await configureWiFi(wifiConfig);
        const res = await httpService.doll.bindDoll({
          dollId,
        });
        if (res.code === 0) {
          showToast('WiFi连接成功');
          await new Promise(resolve => setTimeout(resolve, 1000));
          navigation.reset({
            index: 0,
            routes: [{name: 'AllDolls'}],
          });
        } else {
          showToast(res.message);
        }
      } catch (error) {
        Logger.error('WiFi配置失败', {error});
        showToast(
          'WiFi配置失败,请重试:' +
            (error instanceof Error ? error.message : String(error)),
        );
      }
    } catch (error) {
      Logger.error('WiFi配置失败', {error});
      showToast(
        `配置失败: ${error instanceof Error ? error.message : String(error)}`,
      );
    } finally {
      setIsConfiguring(false);
    }
  };

  return (
    <ScreenPageComponent style={styles.container} bgType="bg_login">
      {/* 头部区域 */}
      <View style={styles.header}>
        <TouchableOpacity onPress={handleBack} style={styles.backButton}>
          <Icon name="left" size={24} color={colors.text} />
        </TouchableOpacity>
        <MyText style={styles.headerTitle}>连接设备</MyText>
        <View style={styles.placeholder} />
      </View>

      <View style={styles.wifiCard}>
        <ScrollView contentContainerStyle={styles.content}>
          {/* 白色卡片容器 */}
          <View>
            {/* 标题 */}
            <MyText style={styles.wifiTitle}>设置FamilyRobot的wifi</MyText>

            {/* 路由器图片区域 */}
            <View style={styles.routerImageContainer}>
              <Image
                source={require('../assets/img/tuceng_2_3x.png')}
                style={styles.routerImage}
                resizeMode="contain"
              />
            </View>

            {/* 提示文本 */}
            <MyText style={styles.hintText}>请输入2.4g无线网</MyText>

            {/* 扫描状态提示 */}
            {isScanning && (
              <View style={styles.scanningIndicator}>
                <ActivityIndicator size="small" color={colors.primary} />
                <MyText style={styles.scanningText}>正在扫描WiFi网络...</MyText>
              </View>
            )}

            {/* WiFi名称选择区域 */}
            <View style={styles.inputSection}>
              <View style={styles.wifiSelector}>
                <MyTextInput
                  style={styles.wifiSelectorContent}
                  value={selectedWifi}
                  onChangeText={setSelectedWifi}
                  placeholder="请输入wifi名称"
                  placeholderTextColor={colors.textSecondary}
                  editable={!isConfiguring}
                />
                <TouchableOpacity
                  style={styles.wifiDownIcon}
                  onPress={toggleWifiList}
                  disabled={isConfiguring}>
                  {isScanning ? (
                    <ActivityIndicator
                      size="small"
                      color={colors.textSecondary}
                    />
                  ) : (
                    <Icon
                      name={showWifiList ? 'up' : 'down'}
                      size={20}
                      color={colors.textSecondary}
                    />
                  )}
                </TouchableOpacity>
              </View>
              {/* WiFi列表下拉 */}
              {showWifiList && (
                <ScrollView style={styles.wifiList}>
                  {wifiNetworks.map(wifi => (
                    <TouchableOpacity
                      key={wifi.id}
                      style={styles.wifiItem}
                      onPress={() => handleSelectWifi(wifi)}
                      activeOpacity={0.7}>
                      <MyText style={styles.wifiItemText}>{wifi.name}</MyText>
                      <Image
                        source={getWifiStrengthIcon(wifi.strength)}
                        style={styles.wifiStrengthIcon}
                        resizeMode="contain"
                      />
                    </TouchableOpacity>
                  ))}
                </ScrollView>
              )}

              {/* 密码输入区域 */}
              <View style={styles.passwordContainer}>
                {/* <MyText style={styles.inputLabel}>密码</MyText> */}
                <View style={styles.passwordInputWrapper}>
                  <MyTextInput
                    style={styles.passwordInput}
                    value={wifiPassword}
                    onChangeText={setWifiPassword}
                    placeholder="请输入密码"
                    placeholderTextColor={colors.textSecondary}
                    secureTextEntry={!showPassword}
                  />
                  <TouchableOpacity
                    onPress={togglePasswordVisibility}
                    style={styles.eyeButton}>
                    <Icon
                      name={showPassword ? 'eye' : 'eyeo'}
                      size={20}
                      color={colors.textSecondary}
                    />
                  </TouchableOpacity>
                </View>
              </View>

              {/* 保存密码选项 */}
              {/* <TouchableOpacity
              style={styles.savePasswordOption}
              onPress={toggleSavePassword}
              activeOpacity={0.7}>
              <View style={styles.checkboxContainer}>
                <View
                  style={[
                    styles.checkbox,
                    savePassword && styles.checkboxChecked,
                  ]}>
                  {savePassword && (
                    <Icon name="check" size={12} color="white" />
                  )}
                </View>
                <MyText style={styles.savePasswordText}>保存密码</MyText>
              </View>
            </TouchableOpacity> */}
            </View>

            {/* 下一步按钮 */}
          </View>
        </ScrollView>
        <TouchableOpacity
          style={[
            styles.nextButton,
            (!selectedWifi || !wifiPassword || isConfiguring) &&
              styles.nextButtonDisabled,
          ]}
          onPress={handleNext}
          disabled={!selectedWifi || !wifiPassword || isConfiguring}
          activeOpacity={0.8}>
          {isConfiguring ? (
            <View style={styles.configuring}>
              <ActivityIndicator size="small" color="#ffffff" />
              <MyText style={styles.nextButtonText}>配置中...</MyText>
            </View>
          ) : (
            <MyText
              style={[
                styles.nextButtonText,
                (!selectedWifi || !wifiPassword || isConfiguring) &&
                  styles.nextButtonTextDisabled,
              ]}>
              下一步
            </MyText>
          )}
        </TouchableOpacity>
      </View>
    </ScreenPageComponent>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.md,
    paddingTop: spacing.sm,
    paddingBottom: spacing.sm,
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: '600' as const,
    color: colors.text,
  },
  placeholder: {
    width: 40,
  },
  contentContainer: {
    flex: 1,
  },
  content: {
    padding: spacing.md,
  },
  wifiCard: {
    flex: 1,
    backgroundColor: '#ffffff',
    borderRadius: borderRadius.lg,
    padding: spacing.xl,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    borderWidth: 1,
    borderColor: colors.primary,
    margin: 10,
    marginBottom: 25,
  },
  wifiTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: '600' as const,
    color: colors.primary,
    marginBottom: spacing.xl,
    textAlign: 'center',
  },
  routerImageContainer: {
    alignItems: 'center',
    marginBottom: spacing.xl,
    height: 120,
    justifyContent: 'center',
  },
  routerImage: {
    width: 150,
    height: 150,
  },
  inputSection: {
    marginBottom: spacing.xl,
  },
  wifiSelector: {
    borderWidth: 1,
    borderColor: colors.primary,
    borderRadius: borderRadius.md,
    padding: spacing.xs,
    marginBottom: spacing.sm,
    flexDirection: 'row',
  },
  wifiDownIcon: {
    justifyContent: 'center',
  },
  wifiSelectorContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    fontSize: typography.fontSize.md,
  },
  wifiList: {
    borderWidth: 1,
    borderColor: colors.primary,
    borderRadius: borderRadius.md,
    backgroundColor: '#ffffff',
    marginBottom: spacing.xs,
    maxHeight: 200,
  },
  wifiItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(115, 54, 246, 0.1)',
  },
  wifiItemText: {
    fontSize: typography.fontSize.md,
    color: colors.text,
    flex: 1,
  },
  wifiStrengthIcon: {
    width: 20,
    height: 20,
  },
  hintText: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
    marginBottom: spacing.md,
  },
  passwordContainer: {
    borderWidth: 1,
    borderColor: colors.primary,
    borderRadius: borderRadius.md,
    // padding: spacing.sm * 0.5,
    marginBottom: spacing.md,
  },
  passwordInputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  passwordInput: {
    flex: 1,
    fontSize: typography.fontSize.md,
    color: colors.text,
    padding: typography.fontSize.md,
    // height: 24,
  },
  eyeButton: {
    padding: spacing.xs,
  },
  savePasswordOption: {
    marginBottom: spacing.md,
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkbox: {
    width: 18,
    height: 18,
    borderRadius: 9,
    borderWidth: 1,
    borderColor: colors.textSecondary,
    marginRight: spacing.sm,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxChecked: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  savePasswordText: {
    fontSize: typography.fontSize.md,
    color: colors.text,
  },
  nextButton: {
    backgroundColor: colors.primary,
    paddingVertical: spacing.md,
    borderRadius: borderRadius.xl,
    alignItems: 'center',
    justifyContent: 'center',
  },
  nextButtonDisabled: {
    backgroundColor: colors.disabled,
  },
  nextButtonText: {
    fontSize: typography.fontSize.md,
    fontWeight: '600' as const,
    color: '#ffffff',
  },
  nextButtonTextDisabled: {
    color: colors.textSecondary,
  },
  scanningIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(115, 54, 246, 0.1)',
    borderRadius: borderRadius.md,
    padding: spacing.sm,
    marginBottom: spacing.md,
  },
  scanningText: {
    fontSize: typography.fontSize.sm,
    color: colors.primary,
    marginLeft: spacing.xs,
  },
  configuring: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default DollConnectWifiScreen;
