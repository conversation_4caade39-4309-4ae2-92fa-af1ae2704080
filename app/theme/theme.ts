// Theme configuration for the app

export const colors = {
  // primary: '#6200ee',
  primary: '#7336f6',
  primaryDark: '#3700b3',
  primaryLight: '#bb86fc',
  blue: '#0383df',
  secondary: '#03dac6',
  secondaryDark: '#018786',
  background: '#ffffff',
  surface: '#ffffff',
  error: '#b00020',
  text: '#000000',
  textSecondary: '#666666',
  textSecondaryLight: '#bbbbbb',
  disabled: '#dddddd',
  placeholderText: '#999999',
  divider: '#e0e0e0',
  butPurpleText: '#7336f6',
  butPurpleBg: '#E1E1FF',
  butPurpleBorder: '#7336f6',
};

export const spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
};

export const typography = {
  fontFamily: {
    regular: 'AlibabaPuHuiTi-3-55-Regular',
    // medium: 'AlibabaPuHuiTi-3-55-Regular',
    // bold: 'AlibabaPuHuiTi_3_65_Medium',
  },
  fontSize: {
    xs: 12,
    sm: 14,
    md: 16,
    lg: 18,
    xl: 20,
    xxl: 24,
    xxxl: 32,
  },
  fontWeight: {
    light: '300',
    regular: '400',
    medium: '500',
    bold: '700',
  },
};

export const borderRadius = {
  sm: 4,
  md: 8,
  lg: 16,
  xl: 24,
  round: 9999,
};

export const shadows = {
  sm: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
    elevation: 2,
  },
  md: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.23,
    shadowRadius: 2.62,
    elevation: 4,
  },
  lg: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 8,
  },
};

export default {
  colors,
  spacing,
  typography,
  borderRadius,
  shadows,
};
