import React from 'react';
import {View, Text, Button, StyleSheet} from 'react-native';
import {
  useCounter,
  useIncrementCounter,
  useUser,
  useSetUser,
  useAppSettings,
  useSetAppSettings,
} from '../app/hooks/useAtoms';

const JotaiExample: React.FC = () => {
  const [count, setCount] = useCounter();
  const incrementCounter = useIncrementCounter();
  const [user, setUser] = useUser();
  const [appSettings, setAppSettings] = useAppSettings();

  // 切换主题
  const toggleTheme = () => {
    setAppSettings({
      ...appSettings,
      theme: appSettings.theme === 'light' ? 'dark' : 'light',
    });
  };

  // 模拟登录
  const handleLogin = () => {
    setUser({
      id: '123',
      name: '测试用户',
      email: '<EMAIL>',
      isLoggedIn: true,
    });
  };

  // 模拟登出
  const handleLogout = () => {
    setUser({
      id: '',
      name: '',
      email: '',
      isLoggedIn: false,
    });
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Jotai 示例</Text>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>计数器示例:</Text>
        <Text style={styles.counter}>当前计数: {count}</Text>
        <View style={styles.buttonRow}>
          <Button title="增加 (normal)" onPress={() => setCount(count + 1)} />
          <Button title="增加 (atom)" onPress={incrementCounter} />
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>用户状态:</Text>
        <Text>登录状态: {user.isLoggedIn ? '已登录' : '未登录'}</Text>
        {user.isLoggedIn && (
          <Text>
            欢迎, {user.name} ({user.email})
          </Text>
        )}
        <View style={styles.buttonRow}>
          <Button
            title="登录"
            onPress={handleLogin}
            disabled={user.isLoggedIn}
          />
          <Button
            title="登出"
            onPress={handleLogout}
            disabled={!user.isLoggedIn}
          />
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>应用设置:</Text>
        <Text>当前主题: {appSettings.theme}</Text>
        <Text>通知: {appSettings.notifications ? '开启' : '关闭'}</Text>
        <Text>语言: {appSettings.language}</Text>
        <Button title="切换主题" onPress={toggleTheme} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    margin: 8,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  section: {
    marginBottom: 20,
    padding: 12,
    backgroundColor: '#fff',
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#eee',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  counter: {
    fontSize: 18,
    marginBottom: 8,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 8,
  },
});

export default JotaiExample;
